import { defineConfig } from 'umi';

export default defineConfig({
  outputPath: process.env.UMI_ENV === 'production' ? 'dist' : process.env.UMI_ENV === 'test' ? 'testdist' : 'devdist',
  plugins: [
    '@umijs/plugins/dist/antd', 
    '@umijs/plugins/dist/dva', 
    '@umijs/plugins/dist/request',
  ],
  antd: {},
  dva: {},
  request: { dataField: 'data' },
  favicons: [
    // 官网说明：此时将指向 `/favicon.png` ，确保你的项目含有 `public/favicon.png`
    '/favicon.png',
  ],
  routes: [
    { path: '/', redirect: '/patients',},
    { path: '/login', component: 'login',layout: false },
    { path: '/patients', component: 'Patients/index', name: '患者列表' },
    { path: '/reports', component: 'reports', name: '报告' },
    { path: '/viewer', component: 'viewer', name: '阅片' },
    { path: '/prints', component: 'Prints/prints', name: '胶片打印' },
    { path: '/render', component: '3DRender', name: '3D预览' },
    { path: '/user', component: 'userManage', name: '用户管理' },
    { path: '/templateManagement', component: 'TemplateManagement', name: '模板管理' },
  ],
  npmClient: 'yarn',
  jsMinifier: 'terser',
});
