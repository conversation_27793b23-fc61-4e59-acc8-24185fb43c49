import React, { useState, useEffect, useRef } from 'react';
import { Space, Button, Select, List, Empty, message, Tabs, ConfigProvider, Collapse } from 'antd';
import { connect, Dispatch } from 'umi';
import type { ApiTemplateType, ApiTemplateItem } from '../data';
import { ReportType } from '@/models/reports';
import { getImage } from '@/services/reports';
import CustomIcon from '@/components/CustomIcon';

// 声明全局变量 CLOUD_API
declare const CLOUD_API: string;

interface ReportModelProps {
  radioFind?: string;
  cliniDiag?: string;
  dispatch: Dispatch;
  reports: ReportType;
}

const ReportModel: React.FC<ReportModelProps> = ({
  radioFind,
  cliniDiag,
  dispatch,
  reports,
}) => {
  const {
    templateTypes,
    templatesInType,
    selectedTemplateContent,
    groupTemplateTypes,
    groupTemplatesInType,
    selectedGroupTemplateContent
  } = reports;
  const myTemplateTypes = templateTypes || [];
  const teamTemplateTypes = groupTemplateTypes || [];

  const [activeTab, setActiveTab] = useState('my');
  const [messageApi, contextHolder] = message.useMessage();
  const [activeCollapseKey, setActiveCollapseKey] = useState<string | string[]>([]);
  const [teamActiveCollapseKey, setTeamActiveCollapseKey] = useState<string | string[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<ApiTemplateItem | null>(null);
  const [previewContent, setPreviewContent] = useState('');
  // 跟踪创建的blob URLs，用于清理
  const blobUrlsRef = useRef<Set<string>>(new Set());

  // 简化的预览图片处理
  const processImagesInContent = async (content: string): Promise<string> => {
    if (!content || !content.includes('{{IMAGE:')) return content;

    const imageRegex = /\{\{IMAGE:([^}]+)\}\}/g;
    let processedContent = content;
    const matches = Array.from(content.matchAll(imageRegex));

    for (const match of matches) {
      const imageName = match[1];
      const placeholder = match[0];

      try {
        const blob = await getImage({ ImageName: imageName });
        if (blob) {
          const blobUrl = URL.createObjectURL(blob);
          // 跟踪创建的blob URL，仅用于预览清理
          blobUrlsRef.current.add(blobUrl);
          processedContent = processedContent.replace(
            placeholder,
            `<img src="${blobUrl}" alt="${imageName}" style="max-width: 100%; height: auto; margin: 4px 0;" />`
          );
        } else {
          // 如果获取失败，显示图片名称
          processedContent = processedContent.replace(
            placeholder,
            `<span style="color: #ff4d4f; font-style: italic;">[图片: ${imageName}]</span>`
          );
        }
      } catch (error) {
        console.error('预览加载图片失败:', error);
        // 如果获取失败，显示图片名称
        processedContent = processedContent.replace(
          placeholder,
          `<span style="color: #ff4d4f; font-style: italic;">[图片加载失败: ${imageName}]</span>`
        );
      }
    }

    return processedContent;
  };

  useEffect(() => {
    // 获取个人模板分类
    dispatch({
      type: 'reports/listTemplateType',
      payload: {},
    });

    // 获取团队模板分类
    dispatch({
      type: 'reports/listGroupTemplateType',
      payload: {},
    });
  }, [dispatch]);

  useEffect(() => {
    if (myTemplateTypes.length > 0 && !activeCollapseKey.length) {
      const firstKey = myTemplateTypes[0].Id.toString();
      setActiveCollapseKey(firstKey);
    }
  }, [myTemplateTypes]);

  useEffect(() => {
    if (teamTemplateTypes.length > 0 && !teamActiveCollapseKey.length) {
      const firstKey = teamTemplateTypes[0].Id.toString();
      setTeamActiveCollapseKey(firstKey);
    }
  }, [teamTemplateTypes]);

  useEffect(() => {
    const updatePreviewContent = async () => {
      const currentTemplateContent = activeTab === 'my' ? selectedTemplateContent : selectedGroupTemplateContent;

      if (currentTemplateContent) {
        const { RadiographicFindingTemplate, ClinicalDiagnosisTemplate } = currentTemplateContent;
        let newPreviewHtml = '';

        try {
          if (RadiographicFindingTemplate) {
            const processedFinding = await processImagesInContent(RadiographicFindingTemplate);
            newPreviewHtml += `<div><strong>影像所见：</strong></div><div>${processedFinding.replace(
              /\n/g,
              '<br/>',
            )}</div>`;
          }

          if (ClinicalDiagnosisTemplate) {
            if (newPreviewHtml) {
              newPreviewHtml += '<br/>';
            }
            const processedDiagnosis = await processImagesInContent(ClinicalDiagnosisTemplate);
            newPreviewHtml += `<div><strong>诊断结论：</strong></div><div>${processedDiagnosis.replace(
              /\n/g,
              '<br/>',
            )}</div>`;
          }

          setPreviewContent(newPreviewHtml || '模板内容为空');
        } catch (error) {
          console.error('更新预览内容失败:', error);
          setPreviewContent('预览内容加载失败');
        }
      } else {
        // 当未选择模板时，不显示任何内容
        setPreviewContent('暂无内容');
      }
    };

    updatePreviewContent();
  }, [selectedTemplateContent, selectedGroupTemplateContent, activeTab]);

  // 清理blob URLs
  useEffect(() => {
    return () => {
      // 组件卸载时清理所有blob URLs
      blobUrlsRef.current.forEach(blobUrl => {
        URL.revokeObjectURL(blobUrl);
      });
      blobUrlsRef.current.clear();
    };
  }, []);

  // 根据分类加载个人模板
  const loadTemplatesForType = async (typeId: string) => {
    if (!typeId || (templatesInType && templatesInType[typeId])) {
      return;
    }
    dispatch({
      type: 'reports/listTemplateByType',
      payload: { TypeId: parseInt(typeId, 10) },
    });
  };

  // 根据分类加载团队模板
  const loadGroupTemplatesForType = async (typeId: string) => {
    if (!typeId || (groupTemplatesInType && groupTemplatesInType[typeId])) {
      return;
    }
    dispatch({
      type: 'reports/listGroupTemplateByType',
      payload: { TypeId: parseInt(typeId, 10) },
    });
  };

  const handleTemplateClick = (template: ApiTemplateItem) => {
    setSelectedTemplate(template);
    if (activeTab === 'my') {
      dispatch({
        type: 'reports/getTemplateContent',
        payload: { Id: template.Id },
      });
    } else {
      dispatch({
        type: 'reports/getGroupTemplateContent',
        payload: { Id: template.Id },
      });
    }
  };

  const handlePanelChange = (key: string | string[]) => {
    if (activeTab === 'my') {
      setActiveCollapseKey(key);
      const currentKey = Array.isArray(key) ? key[key.length - 1] : key;
      if (currentKey) {
        loadTemplatesForType(currentKey);
      }
    } else {
      setTeamActiveCollapseKey(key);
      const currentKey = Array.isArray(key) ? key[key.length - 1] : key;
      if (currentKey) {
        loadGroupTemplatesForType(currentKey);
      }
    }
  };

  const handleApplyTemplate = () => {
    const currentTemplateContent = activeTab === 'my' ? selectedTemplateContent : selectedGroupTemplateContent;

    if (!currentTemplateContent) {
      messageApi.warning('请先选择一个模板');
      return;
    }

    // 替换: 直接用模板内容覆盖
    dispatch({
      type: 'reports/save',
      payload: {
        radioFind: currentTemplateContent.RadiographicFindingTemplate,
        cliniDiag: currentTemplateContent.ClinicalDiagnosisTemplate,
      },
    });
    messageApi.success('替换成功');
  };

  const handleInsertTemplate = () => {
    const currentTemplateContent = activeTab === 'my' ? selectedTemplateContent : selectedGroupTemplateContent;

    if (!currentTemplateContent) {
      messageApi.warning('请先选择一个模板');
      return;
    }

    // 插入: 在原文本后追加模板内容（以换行分隔）
    const newRadio = `${radioFind ? radioFind + '\n' : ''}${currentTemplateContent.RadiographicFindingTemplate}`;
    const newClini = `${cliniDiag ? cliniDiag + '\n' : ''}${currentTemplateContent.ClinicalDiagnosisTemplate}`;

    dispatch({
      type: 'reports/save',
      payload: {
        radioFind: newRadio,
        cliniDiag: newClini,
      },
    });
    messageApi.success('插入成功');
  };

  return (
    <>
      {contextHolder}
      <div style={{ height: '90%', display: 'flex', flexDirection: 'column' }}>
        {/* 上半部分：模板选择 */}
        <div style={{ flex: '0 0 auto', maxHeight: '45%' }}>
          <ConfigProvider
            theme={{
              components: {
                Tabs: {
                  horizontalMargin: '0',
                  cardBg: 'transparent',
                },
              },
            }}
          >
            <div style={{
              display: 'flex',
              width: '100%',
              paddingLeft: '15px',
              paddingRight: '15px'
            }}>
              <div
                style={{
                  flex: 1,
                  textAlign: 'center',
                  padding: '8px 16px',
                  cursor: 'pointer',
                  backgroundColor: activeTab === 'my' ? '#1F69B4' : '#595959',
                  color: 'white',
                  fontSize: '14px',
                  borderTopLeftRadius: '6px'
                }}
                onClick={() => {
                  setActiveTab('my');
                  setSelectedTemplate(null);
                }}
              >
                我的模板
              </div>
              <div
                style={{
                  flex: 1,
                  textAlign: 'center',
                  padding: '8px 16px',
                  cursor: 'pointer',
                  backgroundColor: activeTab === 'team' ? '#1F69B4' : '#595959',
                  color: 'white',
                  fontSize: '14px',
                  borderTopRightRadius: '6px'
                }}
                onClick={() => {
                  setActiveTab('team');
                  setSelectedTemplate(null);
                }}
              >
                团队模板
              </div>
            </div>
          </ConfigProvider>

          {/* 标签页内容 */}
          <div style={{ padding: '0 15px' }}>
            {activeTab === 'my' && (
              <div style={{ height: '200px', overflow: 'auto', backgroundColor: '#262628' }} className="custom-scrollbar">
                <ConfigProvider
                  theme={{
                    components: {
                      Collapse: {
                        contentPadding: '0 16px',
                      },
                    },
                  }}
                >
                  <Collapse
                    activeKey={activeCollapseKey}
                    onChange={handlePanelChange}
                    style={{ backgroundColor: '#333233' }}
                  >
                    {myTemplateTypes.map((type) => {
                      const isActive = (Array.isArray(activeCollapseKey)
                        ? activeCollapseKey.includes(type.Id.toString())
                        : activeCollapseKey === type.Id.toString());
                      return (
                        <Collapse.Panel
                          showArrow={false}
                          header={
                            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%', height: 28 }}>
                              <div style={{ display: 'flex', alignItems: 'center' }}>
                                <CustomIcon type="icon-fenlei" style={{ fontSize: 60 }} />
                                <span style={{
                                  lineHeight: '28px',
                                  fontSize: 14,
                                  whiteSpace: 'nowrap',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  maxWidth: 120,
                                  display: 'inline-block',
                                  color: '#FFFFFF',
                                  marginLeft: '8px'
                                }} title={type.Name === 'default' ? '默认分类' : type.Name}>
                                  {type.Name === 'default' ? '默认分类' : type.Name}
                                </span>
                              </div>
                              <CustomIcon type={isActive ? 'icon-zhankai' : 'icon-zhedie'} style={{ fontSize: 60 }} />
                            </div>
                          }
                          key={type.Id.toString()}
                        >
                          <List
                            dataSource={
                              ((templatesInType && templatesInType[type.Id.toString()]) || []).slice().sort((a, b) => {
                                if (a.Name === '默认模板') return -1;
                                if (b.Name === '默认模板') return 1;
                                return 0;
                              })
                            }
                            renderItem={(template: ApiTemplateItem) => (
                              <div
                                key={template.Id}
                                onClick={() => handleTemplateClick(template)}
                                style={{
                                  backgroundColor: selectedTemplate?.Id === template.Id ? '#333233' : 'transparent',
                                  padding: '8px',
                                  cursor: 'pointer',
                                  marginLeft: '16px',
                                  color: '#FFFFFF',
                                  whiteSpace: 'nowrap',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  maxWidth: '200px',
                                  display: 'flex',
                                  height: '48px',
                                  alignItems: 'center',
                                  gap: '8px'
                                }}
                                title={template.Name}
                              >
                                <CustomIcon type="icon-moban" style={{ fontSize: 60 }} />
                                {template.Name}
                              </div>
                            )}
                          />
                        </Collapse.Panel>
                      );
                    })}
                  </Collapse>
                </ConfigProvider>
              </div>
            )}

            {activeTab === 'team' && (
              <div style={{ height: '200px', overflow: 'auto', backgroundColor: '#262628' }} className="custom-scrollbar">
                <ConfigProvider
                  theme={{
                    components: {
                      Collapse: {
                        contentPadding: '0 16px',
                      },
                    },
                  }}
                >
                  <Collapse
                    activeKey={teamActiveCollapseKey}
                    onChange={handlePanelChange}
                    style={{ backgroundColor: '#333233' }}
                  >
                    {teamTemplateTypes.map((type) => {
                      const isActive = (Array.isArray(teamActiveCollapseKey)
                        ? teamActiveCollapseKey.includes(type.Id.toString())
                        : teamActiveCollapseKey === type.Id.toString());
                      return (
                        <Collapse.Panel
                          showArrow={false}
                          header={
                            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%', height: 28 }}>
                              <div style={{ display: 'flex', alignItems: 'center' }}>
                                <CustomIcon type="icon-fenlei" style={{ fontSize: 60 }} />
                                <span style={{
                                  lineHeight: '28px',
                                  fontSize: 14,
                                  whiteSpace: 'nowrap',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  maxWidth: 120,
                                  display: 'inline-block',
                                  color: '#FFFFFF',
                                  marginLeft: '8px'
                                }} title={type.Name === 'default' ? '默认分类' : type.Name}>
                                  {type.Name === 'default' ? '默认分类' : type.Name}
                                </span>
                              </div>
                              <CustomIcon type={isActive ? 'icon-zhankai' : 'icon-zhedie'} style={{ fontSize: 60 }} />
                            </div>
                          }
                          key={type.Id.toString()}
                        >
                          <List
                            dataSource={
                              ((groupTemplatesInType && groupTemplatesInType[type.Id.toString()]) || []).slice().sort((a, b) => {
                                if (a.Name === '默认模板') return -1;
                                if (b.Name === '默认模板') return 1;
                                return 0;
                              })
                            }
                            renderItem={(template: ApiTemplateItem) => (
                              <div
                                key={template.Id}
                                onClick={() => handleTemplateClick(template)}
                                style={{
                                  backgroundColor: selectedTemplate?.Id === template.Id ? '#333233' : 'transparent',
                                  padding: '8px',
                                  cursor: 'pointer',
                                  marginLeft: '16px',
                                  color: '#FFFFFF',
                                  whiteSpace: 'nowrap',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  maxWidth: '200px',
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: '8px'
                                }}
                                title={template.Name}
                              >
                                <CustomIcon type="icon-moban" style={{ fontSize: 60 }} />
                                {template.Name}
                              </div>
                            )}
                          />
                        </Collapse.Panel>
                      );
                    })}
                  </Collapse>
                </ConfigProvider>
              </div>
            )}
          </div>
        </div>

        {/* 下半部分：预览 */}
        <div style={{ flex: 1, paddingTop: '16px', paddingLeft: '15px', paddingRight: '15px' }}>
          <div style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: 'white' }}>
            预览
          </div>
          <div style={{ color: 'white', height: 'calc(100% - 35px)', overflow: 'auto' }} className="custom-scrollbar">
            <div style={{ marginBottom: '12px' }}>
              <div
                style={{
                  backgroundColor: '#191919',
                  borderRadius: '4px',
                  height: '230px',
                  overflow: 'auto',
                  padding: '8px',
                  fontSize: '12px',
                  lineHeight: '1.4'
                }}
                className="custom-scrollbar"
                dangerouslySetInnerHTML={{ __html: previewContent || '暂无内容' }}
              />
            </div>

            <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '7px' }}>
              <Button
                onClick={handleApplyTemplate}
                style={{ width: '60px', height: '22.5px', background: 'transparent', border: '1px solid #ffffff', color: '#ffffff', padding: 0 }}
              >
                替换
              </Button>
              <Button
                onClick={handleInsertTemplate}
                style={{ width: '60px', height: '22.5px', background: 'transparent', border: '1px solid #ffffff', color: '#ffffff', padding: 0 }}
              >
                插入
              </Button>
            </div>

          </div>
        </div>
      </div>
    </>
  );
};

const mapStateToProps = ({ reports }: { reports: ReportType }) => ({
  reports,
});

export default connect(mapStateToProps)(ReportModel);
