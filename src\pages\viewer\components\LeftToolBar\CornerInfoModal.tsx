import React, { useState, useEffect } from 'react';
import { Modal, Checkbox, Row, Col, Button, message } from 'antd';
import { EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import styles from './CornerInfoModal.less';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';

interface CornerInfoModalProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (config: CornerInfoConfig) => void;
  initialConfig?: CornerInfoConfig;
}

export interface CornerInfoConfig {
  enabled: boolean;
  ct: {
    // 左上：患者信息
    patientName: boolean;
    patientId: boolean;
    patientAge: boolean;
    patientSex: boolean;
    studyDescription: boolean;
    studyDate: boolean;
    studyTime: boolean;
    seriesNumber: boolean;
    bodyPartExamined: boolean;
    patientPosition: boolean;
    patientBirthDate: boolean;
    // 右上：设备信息
    institutionName: boolean;
    manufacturer: boolean;
    manufacturerModelName: boolean;
    modality: boolean;
    softwareVersions: boolean;
    protocolName: boolean;
    accessionNumber: boolean;
    // 左下：技术参数
    seriesDescription: boolean;
    kvp: boolean;
    xRayTubeCurrent: boolean;
    sliceThickness: boolean;
    tableHeight: boolean;
    spiralPitchFactor: boolean;
    exposureTime: boolean;
    reconstructionDiameter: boolean;
    convolutionKernel: boolean;
    // 右下：显示参数
    imageZoomFactor: boolean;
    windowWidth: boolean;
    cursorPosition: boolean;
    pixelValue: boolean;
  };
  pet: {
    // 左上：患者信息
    patientName: boolean;
    patientId: boolean;
    patientAge: boolean;
    patientSex: boolean;
    studyDescription: boolean;
    studyDate: boolean;
    studyTime: boolean;
    seriesNumber: boolean;
    bodyPartExamined: boolean;
    patientPosition: boolean;
    patientBirthDate: boolean;
    // 右上：设备信息
    institutionName: boolean;
    manufacturer: boolean;
    manufacturerModelName: boolean;
    modality: boolean;
    softwareVersions: boolean;
    protocolName: boolean;
    accessionNumber: boolean;
    // 左下：PET特有参数
    radiopharmaceutical: boolean;
    seriesDescription: boolean;
    matrixSize: boolean;
    sliceThickness: boolean;
    reconstructionDiameter: boolean;
    numberOfSlices: boolean;
    currentSlicePosition: boolean;
    // 右下：显示参数
    imageZoomFactor: boolean;
    suvThreshold: boolean;
    cursorPosition: boolean;
    pixelValue: boolean;
  };
  fusion: {
    // 左上：患者信息
    patientName: boolean;
    patientId: boolean;
    patientAge: boolean;
    patientSex: boolean;
    studyDescription: boolean;
    studyDate: boolean;
    studyTime: boolean;
    seriesNumber: boolean;
    patientPosition: boolean;
    patientBirthDate: boolean;
    // 右上：设备信息
    institutionName: boolean;
    manufacturer: boolean;
    manufacturerModelName: boolean;
    modality: boolean;
    softwareVersions: boolean;
    protocolName: boolean;
    accessionNumber: boolean;
    // 左下：无内容
    // 右下：显示参数 - 融合四角信息设置右下不应该有鼠标位置和鼠标位置的值
    imageZoomFactor: boolean;
  };
}

// 默认配置
const defaultConfig: CornerInfoConfig = {
  enabled: true,
  ct: {
    // 左上：患者信息 - 默认选择核心患者信息
    patientName: true,
    patientId: true,
    patientAge: true,
    patientSex: true,
    studyDescription: true,
    studyDate: true,
    studyTime: true,
    seriesNumber: false,
    bodyPartExamined: false,
    patientPosition: false,
    patientBirthDate: false,
    // 右上：设备信息 - 默认选择基本设备信息
    institutionName: false,
    manufacturer: true,
    manufacturerModelName: true,
    modality: true,
    softwareVersions: false,
    protocolName: false,
    accessionNumber: false,
    // 左下：技术参数 - 默认选择重要技术参数
    seriesDescription: true,
    kvp: true,
    xRayTubeCurrent: true,
    sliceThickness: true,
    tableHeight: false,
    spiralPitchFactor: false,
    exposureTime: false,
    reconstructionDiameter: false,
    convolutionKernel: false,
    // 右下：显示参数 - 默认选择窗宽窗位
    imageZoomFactor: false,
    windowWidth: true,
    cursorPosition: false,
    pixelValue: false,
  },
  pet: {
    // 左上：患者信息 - 默认选择核心患者信息
    patientName: true,
    patientId: true,
    patientAge: true,
    patientSex: true,
    studyDescription: true,
    studyDate: true,
    studyTime: true,
    seriesNumber: false,
    bodyPartExamined: false,
    patientPosition: false,
    patientBirthDate: false,
    // 右上：设备信息 - 默认选择基本设备信息
    institutionName: false,
    manufacturer: true,
    manufacturerModelName: true,
    modality: true,
    softwareVersions: false,
    protocolName: false,
    accessionNumber: false,
    // 左下：PET特有参数 - 默认选择重要PET参数
    radiopharmaceutical: true,
    seriesDescription: true,
    matrixSize: true,
    sliceThickness: true,
    reconstructionDiameter: false,
    numberOfSlices: false,
    currentSlicePosition: false,
    // 右下：显示参数 - 默认选择SUV阈值
    imageZoomFactor: false,
    suvThreshold: true,
    cursorPosition: false,
    pixelValue: false,
  },
  fusion: {
    // 左上：患者信息 - 默认选择核心患者信息
    patientName: true,
    patientId: true,
    patientAge: true,
    patientSex: true,
    studyDescription: true,
    studyDate: true,
    studyTime: true,
    seriesNumber: false,
    patientPosition: false,
    patientBirthDate: false,
    // 右上：设备信息 - 默认选择基本设备信息
    institutionName: false,
    manufacturer: true,
    manufacturerModelName: true,
    modality: true,
    softwareVersions: false,
    protocolName: false,
    accessionNumber: false,
    // 左下：无内容
    // 右下：显示参数 - 融合四角信息设置右下不应该有鼠标位置和鼠标位置的值
    imageZoomFactor: true,
  },
};

const CornerInfoModal: React.FC<CornerInfoModalProps> = ({ visible, onClose, onConfirm, initialConfig = defaultConfig }) => {
  const [config, setConfig] = useState<CornerInfoConfig>(initialConfig);

  useEffect(() => {
    setConfig(initialConfig);
  }, [initialConfig, visible]);

  // 切换显示/隐藏
  const toggleEnabled = () => {
    setConfig((prev) => ({ ...prev, enabled: !prev.enabled }));
  };

  // 处理单个选项变化
  const handleCheckboxChange = (section: 'ct' | 'pet' | 'fusion', key: string, checked: boolean) => {
    setConfig((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: checked,
      },
    }));
  };

  // 验证配置
  const validateConfig = () => {
    const ctSelected = Object.values(config.ct).some(Boolean);
    const petSelected = Object.values(config.pet).some(Boolean);
    const fusionSelected = Object.values(config.fusion).some(Boolean);

    if (!ctSelected) {
      message.error('CT四角信息至少勾选一个');
      return false;
    }
    if (!petSelected) {
      message.error('PET四角信息至少勾选一个');
      return false;
    }
    if (!fusionSelected) {
      message.error('融合四角信息至少勾选一个');
      return false;
    }
    return true;
  };

  // 确认配置
  const handleConfirm = () => {
    if (validateConfig()) {
      onConfirm(config);
      onClose();
    }
  };

  // 渲染选项组 - 四角布局
  const renderCheckboxGroup = (title: string, section: 'ct' | 'pet' | 'fusion', options: { key: string; label: string }[]) => {
    let topLeft: { key: string; label: string }[],
      topRight: { key: string; label: string }[],
      bottomLeft: { key: string; label: string }[],
      bottomRight: { key: string; label: string }[];

    if (section === 'ct') {
      // CT特殊布局：按照医学影像的分类
      topLeft = options.slice(0, 10); // 患者信息 (10项)
      topRight = options.slice(10, 17); // 设备信息 (7项) - 机构名称在右上第一个
      bottomLeft = options.slice(17, 26); // 技术参数 (9项) - 序列描述在左下第一个
      bottomRight = options.slice(26); // 显示参数 (4项) - 缩放比例在右下第一个
    } else if (section === 'pet') {
      // PET特殊布局：按照PET影像的分类
      topLeft = options.slice(0, 10); // 患者信息 (10项)
      topRight = options.slice(10, 17); // 设备信息 (7项) - 机构名称在右上第一个
      bottomLeft = options.slice(17, 24); // PET特有参数 (7项) - 同位素名称在左下第一个
      bottomRight = options.slice(24); // 显示参数 (4项) - 缩放比例在右下第一个
    } else if (section === 'fusion') {
      // 融合特殊布局：简化的分类
      topLeft = options.slice(0, 10); // 患者信息 (10项)
      topRight = options.slice(10, 17); // 设备信息 (7项) - 机构名称在右上第一个
      bottomLeft = []; // 左下：无内容
      bottomRight = options.slice(17); // 显示参数 (1项：只有缩放比例)
    } else {
      // 其他类型自动分配
      const itemsPerCorner = Math.ceil(options.length / 4);
      topLeft = options.slice(0, itemsPerCorner);
      topRight = options.slice(itemsPerCorner, itemsPerCorner * 2);
      bottomLeft = options.slice(itemsPerCorner * 2, itemsPerCorner * 3);
      bottomRight = options.slice(itemsPerCorner * 3);
    }

    const renderCorner = (items: { key: string; label: string }[], position: string) => {
      // 根据位置设置不同的样式类名
      let cornerClass = '';
      switch (position) {
        case '左上':
          cornerClass = styles.topLeft;
          break;
        case '右上':
          cornerClass = styles.topRight;
          break;
        case '左下':
          cornerClass = styles.bottomLeft;
          break;
        case '右下':
          cornerClass = styles.bottomRight;
          break;
      }

      return (
        <div className={`${styles.cornerBox} ${cornerClass}`}>
          <div className={styles.cornerLabel}>{position}</div>
          <div className={styles.checkboxList}>
            {items.map((option) => (
              <Checkbox
                key={option.key}
                checked={config[section][option.key as keyof (typeof config)[typeof section]]}
                onChange={(e: CheckboxChangeEvent) => handleCheckboxChange(section, option.key, e.target.checked)}
                className={styles.checkboxItem}
              >
                <span>{option.label}</span>
              </Checkbox>
            ))}
          </div>
        </div>
      );
    };

    return (
      <div className={styles.configSection}>
        <div className={styles.sectionTitle}>{title}</div>
        <div className={styles.cornerGrid}>
          {/* 上半部分 */}
          <div className={styles.gridRow}>
            {renderCorner(topLeft, '左上')}
            {renderCorner(topRight, '右上')}
          </div>
          {/* 下半部分 */}
          <div className={styles.gridRow}>
            {renderCorner(bottomLeft, '左下')}
            {renderCorner(bottomRight, '右下')}
          </div>
        </div>
      </div>
    );
  };

  // CT配置项 - 按四角分布
  const ctOptions = [
    // 左上：患者信息
    { key: 'patientName', label: '患者姓名' },
    { key: 'patientId', label: '患者编号' },
    { key: 'patientAge', label: '患者年龄' },
    { key: 'patientSex', label: '患者性别' },
    { key: 'studyDescription', label: '检查部位' },
    { key: 'studyDate', label: '检查日期' },
    { key: 'studyTime', label: '检查时间' },
    { key: 'seriesNumber', label: '序列号' },
    { key: 'patientPosition', label: '患者摆位' },
    { key: 'patientBirthDate', label: '患者出生日期' },

    // 右上：设备信息
    { key: 'institutionName', label: '机构名称' }, // 机构名称放在右上第一个
    { key: 'manufacturer', label: '制造商' },
    { key: 'manufacturerModelName', label: '设备型号' },
    { key: 'modality', label: '模态' },
    { key: 'softwareVersions', label: '软件版本' },
    { key: 'protocolName', label: '协议名称' },
    { key: 'accessionNumber', label: '登记号' },

    // 左下：技术参数
    { key: 'seriesDescription', label: '序列描述' }, // 序列描述在左下第一个
    { key: 'kvp', label: '管电压' },
    { key: 'xRayTubeCurrent', label: '管电流' },
    { key: 'sliceThickness', label: '层厚' },
    { key: 'tableHeight', label: '床高' },
    { key: 'spiralPitchFactor', label: '螺距' },
    { key: 'exposureTime', label: '曝光时间' },
    { key: 'reconstructionDiameter', label: '重建尺寸' },
    { key: 'convolutionKernel', label: '重建卷积核' },

    // 右下：显示参数
    { key: 'imageZoomFactor', label: '缩放比例' }, // 缩放比例在右下第一个
    { key: 'windowWidth', label: '窗宽窗位' },
    { key: 'cursorPosition', label: '鼠标位置' },
    { key: 'pixelValue', label: '鼠标位置的值' },
  ];

  // PET配置项 - 按四角分布
  const petOptions = [
    // 左上：患者信息
    { key: 'patientName', label: '患者姓名' },
    { key: 'patientId', label: '患者编号' },
    { key: 'patientAge', label: '患者年龄' },
    { key: 'patientSex', label: '患者性别' },
    { key: 'studyDescription', label: '检查部位' },
    { key: 'studyDate', label: '检查日期' },
    { key: 'studyTime', label: '检查时间' },
    { key: 'seriesNumber', label: '序列号' },
    { key: 'patientPosition', label: '患者摆位' },
    { key: 'patientBirthDate', label: '患者出生日期' },

    // 右上：设备信息
    { key: 'institutionName', label: '机构名称' }, // 机构名称放在右上第一个
    { key: 'manufacturer', label: '制造商' },
    { key: 'manufacturerModelName', label: '设备型号' },
    { key: 'modality', label: '模态' },
    { key: 'softwareVersions', label: '软件版本' },
    { key: 'protocolName', label: '协议名称' },
    { key: 'accessionNumber', label: '登记号' },

    // 左下：PET特有参数
    { key: 'radiopharmaceutical', label: '同位素名称' }, // 同位素名称在左下第一个
    { key: 'seriesDescription', label: '序列描述' },
    { key: 'matrixSize', label: '行列/像素矩阵' },
    { key: 'sliceThickness', label: '层厚' },
    { key: 'reconstructionDiameter', label: '重建视野' },
    { key: 'numberOfSlices', label: '图像层数' },
    { key: 'currentSlicePosition', label: '当前切换位置' },

    // 右下：显示参数
    { key: 'imageZoomFactor', label: '缩放比例' }, // 缩放比例在右下第一个
    { key: 'suvThreshold', label: 'SUV阈值' },
    { key: 'cursorPosition', label: '鼠标位置' },
    { key: 'pixelValue', label: '鼠标位置的值' },
  ];

  // 融合配置项 - 按四角分布
  const fusionOptions = [
    // 左上：患者信息
    { key: 'patientName', label: '患者姓名' },
    { key: 'patientId', label: '患者编号' },
    { key: 'patientAge', label: '患者年龄' },
    { key: 'patientSex', label: '患者性别' },
    { key: 'studyDescription', label: '检查部位' },
    { key: 'studyDate', label: '检查日期' },
    { key: 'studyTime', label: '检查时间' },
    { key: 'seriesNumber', label: '序列号' },
    { key: 'patientPosition', label: '患者摆位' },
    { key: 'patientBirthDate', label: '患者出生日期' },

    // 右上：设备信息
    { key: 'institutionName', label: '机构名称' }, // 机构名称放在右上第一个
    { key: 'manufacturer', label: '制造商' },
    { key: 'manufacturerModelName', label: '设备型号' },
    { key: 'modality', label: '模态' },
    { key: 'softwareVersions', label: '软件版本' },
    { key: 'protocolName', label: '协议名称' },
    { key: 'accessionNumber', label: '登记号' },

    // 左下：无内容（空数组，但为了保持布局结构）

    // 右下：显示参数 - 融合四角信息设置右下不应该有鼠标位置和鼠标位置的值
    { key: 'imageZoomFactor', label: '缩放比例' }, // 缩放比例在右下第一个
  ];

  return (
    <Modal
      title='四角信息设置'
      open={visible}
      onCancel={onClose}
      width={1000}
      bodyStyle={{ maxHeight: '90vh', overflowY: 'auto' }}
      footer={[
        <Button key='cancel' onClick={onClose}>
          取消
        </Button>,
        <Button key='confirm' type='primary' onClick={handleConfirm}>
          确定
        </Button>,
      ]}
      className={styles.cornerInfoModal}
    >
      {/* 显示设置 */}
      <div className={styles.displaySetting}>
        <span className={styles.settingLabel}>显示设置：</span>
        <Button
          className={`${styles.displayButton} ${!config.enabled ? styles.hidden : ''}`}
          icon={config.enabled ? <EyeOutlined /> : <EyeInvisibleOutlined />}
          onClick={toggleEnabled}
        >
          {config.enabled ? '显示' : '隐藏'}
        </Button>
      </div>

      {/* 四角信息设置区域 */}
      <div>
        <div className={styles.configContainer}>
          {renderCheckboxGroup('CT四角信息设置', 'ct', ctOptions)}
          {renderCheckboxGroup('PET四角信息设置', 'pet', petOptions)}
          {renderCheckboxGroup('融合四角信息设置', 'fusion', fusionOptions)}
        </div>
      </div>
    </Modal>
  );
};

export default CornerInfoModal;
export { defaultConfig };
