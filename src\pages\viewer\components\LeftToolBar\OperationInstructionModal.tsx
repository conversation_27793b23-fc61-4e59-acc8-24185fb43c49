import React from 'react';
import { Modal } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import styles from './OperationInstructionModal.less';

interface OperationInstructionModalProps {
  visible: boolean;
  onClose: () => void;
}

const OperationInstructionModal: React.FC<OperationInstructionModalProps> = ({ visible, onClose }) => {

  return (
    <Modal
      title="操作说明"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      centered
      classNames={{
          header: styles.modalHeader,
          content: styles.modalContent,
          body: styles.modalBody,
          footer: styles.modalFooter,
        }}
        closeIcon={<div className={styles.closeIcon}><CloseOutlined /></div>}
    >
      <div className={styles.instructionContent}>
        <table className={styles.instructionTable}>
          <thead>
            <tr>
              <th>操作名称</th>
              <th>控件</th>
              <th>操作说明</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>定位旋转</td>
              <td>鼠标左键</td>
              <td>
                1.在MPR模式三视图时，按住并拖动任意视图的定位线，可联动旋转其它两平面图像；松开鼠标后，鼠标长住地位线并旋转，则同步旋转。<br />
                2.在MPR模式单视图时，按住并拖动当前视图的定位线，当前视图不做旋转。
              </td>
            </tr>
            <tr>
              <td>翻页</td>
              <td>鼠标滚轮</td>
              <td>在图像区鼠标滚轮上下滚动，图像同步上下翻页。</td>
            </tr>
            <tr>
              <td>双击放大</td>
              <td>鼠标左键</td>
              <td>多窗口模式下，双击任意窗口可放大图像，再次双击恢复原始尺寸。</td>
            </tr>
            <tr>
              <td>放大/缩小</td>
              <td>鼠标右键</td>
              <td>在图像窗口按住鼠标右键并上、下拖动鼠标，则可放大、缩小图像。</td>
            </tr>
            <tr>
              <td>调窗</td>
              <td>鼠标滚轮</td>
              <td>
                在图像区按住鼠标滚轮：<br />
                CT: 上下移动鼠标调节窗位，左右移动调节窗宽。<br />
                PET: 左右移动鼠标调节SUV浓度。
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </Modal>
  );
};

export default OperationInstructionModal;