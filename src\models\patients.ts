import { Effect, Reducer } from 'umi';

export interface PatientType {
  cornerstoneInited: boolean;
}

// model内容：
// 1、患者列表信息（已由ProTable接管）
// 2、患者列表的详细信息：序列list和报告report（较报告页更简略）
// 3、序列图片的显示，series粒度

export interface ModelType {
  state: PatientType;
  effects: {
  };
  reducers: {
    save: Reducer<PatientType>;
  };
}

const Model: ModelType = {
  state: {
    cornerstoneInited: false, // 记录cornerstone是否在页面中进行初始化
  },
  effects: {
  },
  reducers: {
    save(state, { payload }) {
      return { ...state, ...payload };
    },
  },
};

export default Model;
