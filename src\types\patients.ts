import { QueryListParamsType } from "./index";

// 检查列表查询参数
export type QueryCheckListParamsType = QueryListParamsType & {
  SearchItem?: string, // 模糊查找,
  SearchType?: string, // 查找类型,
  CheckDate?: string, // 检查日期,
  StatusOfReport?: string, // 报告状态,
}

// 检查列表 table 项
export interface CheckListItemType {
  Id?: number; // 主键
  CreateTime?: string; // 创建时间
  UpdateTime?: string; // 更新时间
  Status?: number; // 状态
  Study_Id?: string; // 检查号
  StudyId?: string; // 检查号
  Patient_Id?: string; // 患者编号
  Name?: string; // 姓名
  Sex?: string; // 性别
  Age?: number; // 年龄
  Birth_Date?: string; // 出生时间
  Machine?: string; // 检查设备名称
  Tracer?: string; // 示踪剂
  Dosage?: string; // 剂量
  Referring_Physician_Name?: string; // 初诊医生
  Study_Description?: string; // Study描述
  Weight?: string; // 体重
  Last_Upload_Time?: string; // 最后上传时间
  Body_Part_Examined?: string; // 检查部位
  Institution_Id?: string; // 机构ID
  Exam_Date?: string; // 检查时间
  Doctor_of_Report?: string; // 医生检查报告
  Report_Date?: string; // 报告时间
  Doctor_of_Check?: string; // 医生检查
  Check_Date?: string; // 检查医生时间
  Rejected_Reason?: string; // 拒绝原因
  Status_of_Report?: number; // 报告状态
}

// 检查列表返回
export interface CheckListType {
  PatientList: CheckListItemType[],
  Length: number,
}

// 序列列表查询参数
export type QuerySeriesListParamsType = QueryListParamsType & {
  StudyId: string | number,
}

// 序列列表 table 项
export interface SeriesListItemType {
  Id?: number; // 主键
  Modality?: string; // 模态，非必须
  NumberOfSlices?: number; // 图像总数，非必须
  StudyId?: string; // StudyId，非必须
  SeriesId?: string; // SeriesId，非必须
  StationName?: string; // 设备名称，非必须
  SeriesDescription?: string; // 序列描述，非必须
  SeriesDate?: string; // 序列时间，非必须
}

// 序列列表返回
export interface SeriesListType {
  List: SeriesListItemType[],
  Total: number,
}

// Dicom图像查询参数
export type QueryDicomListParamsType = {
  SeriesId?: string | number,
  Limit?: number,
  Offset?: number,
}

export interface DicomListItemType {
  Id?: string; // 主键
  InstanceId?: string;
  SeriesId?: string; 
  InstanceNumber?: string; 
}

// DICOM 图像列表返回
export interface DicomListType {
  List: DicomListItemType[],
  Total: number,
}