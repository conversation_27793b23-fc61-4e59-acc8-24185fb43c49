import { Effect, Reducer } from 'umi';
import { message } from 'antd';
import {
  listTemplate,
  ListTemplateType,
  getTemplateContent,
  updateTemplate,
  createTemplate,
  listTemplateCategories,
  createTemplateCategory,
  updateTemplateCategory,
  deleteTemplateCategory,
  listGroupTemplate,
  listGroupTemplateType,
  createGroupTemplate,
  getGroupTemplateContent,
  deleteTemplate as deleteTemplateApi,
  deleteGroupTemplate as deleteGroupTemplateApi,
  deleteGroupTemplateCategory as deleteGroupTemplateCategoryApi,
  updateGroupTemplate as updateGroupTemplateApi,
  updateGroupTemplateCategory as updateGroupTemplateCategoryApi,
  saveTemplateImage,
  saveGroupTemplateImage
} from '@/services/reports';
import type { ApiTemplate, ApiCategory, ApiTemplateType, TemplateContent, ApiListCategoryData, ApiGroupTemplate, ApiGroupCategory } from './data.d';

export interface TemplateManagementState {
  templates: ApiTemplate[];
  templatePagination: {
    current: number;
    pageSize: number;
    total: number;
    SearchItem: string;
  };
  templatesLoading: boolean;

  categories: ApiCategory[];
  categoryPagination: {
    current: number;
    pageSize: number;
    total: number;
    SearchItem: string;
  };
  categoriesLoading: boolean;

  groupTemplates: ApiGroupTemplate[];
  groupTemplatePagination: {
    current: number;
    pageSize: number;
    total: number;
    SearchItem: string;
  };
  groupTemplatesLoading: boolean;

  groupCategories: ApiGroupCategory[];
  groupCategoryPagination: {
    current: number;
    pageSize: number;
    total: number;
    SearchItem: string;
  };
  groupCategoriesLoading: boolean;

  templateTypes: ApiTemplateType[];
  editingTemplateContent: TemplateContent | null;
  modalLoading: boolean;
  // 图片上传相关状态
  imageUploading: boolean;
}

export interface ModelType {
  namespace: 'templateManagement';
  state: TemplateManagementState;
  effects: {
    fetchTemplates: Effect;
    fetchTemplateTypes: Effect;
    fetchTemplateContent: Effect;
    createTemplate: Effect;
    updateTemplate: Effect;
    fetchCategories: Effect;
    createCategory: Effect;
    updateCategory: Effect;
    deleteCategory: Effect;
    deleteGroupCategory: Effect;
    deleteTemplate: Effect;
    deleteGroupTemplate: Effect;
    fetchGroupTemplates: Effect;
    fetchGroupCategories: Effect;
    fetchGroupTemplateContent: Effect;
    createGroupTemplate: Effect;
    updateGroupTemplate: Effect;
    updateGroupCategory: Effect;
    // 图片上传相关Effect
    uploadTemplateImage: Effect;
    uploadGroupTemplateImage: Effect;
  };
  reducers: {
    save: Reducer<TemplateManagementState>;
    clearEditingContent: Reducer<TemplateManagementState>;
  };
}

const Model: ModelType = {
  namespace: 'templateManagement',

  state: {
    templates: [],
    templatePagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      SearchItem: '',
    },
    templatesLoading: false,

    categories: [],
    categoryPagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      SearchItem: '',
    },
    categoriesLoading: false,

    groupTemplates: [],
    groupTemplatePagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      SearchItem: '',
    },
    groupTemplatesLoading: false,

    groupCategories: [],
    groupCategoryPagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      SearchItem: '',
    },
    groupCategoriesLoading: false,

    templateTypes: [],
    editingTemplateContent: null,
    modalLoading: false,
    imageUploading: false,
  },

  effects: {
    *fetchTemplates({ payload }, { call, put }): Generator {
      yield put({ type: 'save', payload: { templatesLoading: true } });
      const { pagination } = payload;
      try {
        const res: { TemplateList: ApiTemplate[], Total: number } = yield call(listTemplate, {
          Limit: -1,
          Offset: -1,
          SearchItem: pagination.SearchItem,
        });
        if (res && res.TemplateList) {
          yield put({
            type: 'save',
            payload: {
              templates: res.TemplateList,
              templatePagination: { ...pagination, total: res.Total },
            },
          });
        }
      } catch (e) {
        message.error('获取模板列表失败');
      } finally {
        yield put({ type: 'save', payload: { templatesLoading: false } });
      }
    },

    *fetchTemplateTypes(_, { call, put }): Generator {
      try {
        const res: { TemplateTypeList: ApiTemplateType[] } = yield call(ListTemplateType, {});
        if (res && res.TemplateTypeList) {
          yield put({ type: 'save', payload: { templateTypes: res.TemplateTypeList } });
        }
      } catch (e) {
        message.error('获取模板分类失败');
      }
    },

    *fetchTemplateContent({ payload }, { call, put }): Generator {
      yield put({ type: 'save', payload: { modalLoading: true } });
      try {
        const res: { data?: TemplateContent, Template?: TemplateContent } = yield call(getTemplateContent, payload);
        const content = res?.data || res?.Template || res;
        if (content) {
          yield put({ type: 'save', payload: { editingTemplateContent: content } });
        }
      } catch (e) {
        message.error('获取模板内容失败');
      } finally {
        yield put({ type: 'save', payload: { modalLoading: false } });
      }
    },

    *createTemplate({ payload }, { call, put, select }): Generator {
      yield put({ type: 'save', payload: { modalLoading: true } });
      try {
        yield call(createTemplate, payload.params);
        message.success('创建成功');
        payload.onSuccess();
        const { templatePagination } = yield select((state: { templateManagement: TemplateManagementState }) => state.templateManagement);
        yield put({ type: 'fetchTemplates', payload: { pagination: templatePagination } });
      } finally {
        yield put({ type: 'save', payload: { modalLoading: false } });
      }
    },

    *updateTemplate({ payload }, { call, put, select }): Generator {
      yield put({ type: 'save', payload: { modalLoading: true } });
      try {
        yield call(updateTemplate, payload.params);
        message.success('更新成功');
        payload.onSuccess();
        const { templatePagination } = yield select((state: { templateManagement: TemplateManagementState }) => state.templateManagement);
        yield put({ type: 'fetchTemplates', payload: { pagination: templatePagination } });
      } catch (e) {
        // message.error('更新失败');
      } finally {
        yield put({ type: 'save', payload: { modalLoading: false } });
      }
    },

    *fetchCategories({ payload }, { call, put }): Generator {
      yield put({ type: 'save', payload: { categoriesLoading: true } });
      const { pagination } = payload;
      try {
        const res: ApiListCategoryData & { TemplateTypeList?: ApiCategory[]; Total?: number } = yield call(listTemplateCategories, {
          Limit: -1,
          Offset: -1,
          SearchItem: pagination.SearchItem,
        });
        if (res && res.TemplateTypeList) {
          yield put({
            type: 'save',
            payload: {
              categories: res.TemplateTypeList,
              categoryPagination: { ...pagination, total: res.Total ?? res.TemplateTypeList.length },
            },
          });
        }
      } catch (e) {
        message.error('获取分类列表失败');
      } finally {
        yield put({ type: 'save', payload: { categoriesLoading: false } });
      }
    },

    *createCategory({ payload }, { call, put, select }): Generator {
      try {
        yield call(createTemplateCategory, { Name: payload.name });
        message.success('创建分类成功');
        if (payload.onSuccess) {
          payload.onSuccess();
        }
        const { categoryPagination } = yield select((state: { templateManagement: TemplateManagementState }) => state.templateManagement);
        yield put({ type: 'fetchCategories', payload: { pagination: categoryPagination } });
      } catch (e) {
        // message.error('创建分类失败');
        if (payload.onFailure) {
          payload.onFailure();
        }
      }
    },

    *updateCategory({ payload }, { call, put, select }): Generator {
      try {
        yield call(updateTemplateCategory, { Id: payload.id, Name: payload.name });
        message.success('更新分类成功');
        if (payload.onSuccess) {
          payload.onSuccess();
        }
        const { categoryPagination } = yield select((state: { templateManagement: TemplateManagementState }) => state.templateManagement);
        yield put({ type: 'fetchCategories', payload: { pagination: categoryPagination } });
      } catch (e) {
        // message.error('更新分类失败');
        if (payload.onFailure) {
          payload.onFailure();
        }
      }
    },

    *deleteCategory({ payload }, { call, put, select }): Generator {
      try {
        yield call(deleteTemplateCategory, { Id: payload.id });
        message.success('删除分类成功');
        const { categoryPagination } = yield select((state: { templateManagement: TemplateManagementState }) => state.templateManagement);
        yield put({ type: 'fetchCategories', payload: { pagination: { ...categoryPagination, current: 1 } } });
      } catch (e) {
        message.error('删除分类失败');
      }
    },

    *deleteGroupCategory({ payload }, { call, put, select }): Generator {
      try {
        yield call(deleteGroupTemplateCategoryApi, { Id: payload.id });
        message.success('删除分类成功');
        const { groupCategoryPagination } = yield select((state: { templateManagement: TemplateManagementState }) => state.templateManagement);
        yield put({
          type: 'fetchGroupCategories',
          payload: { pagination: { ...groupCategoryPagination, current: 1 } },
        });
      } catch (e) {
        message.error('删除分类失败');
      }
    },

    *deleteTemplate({ payload }, { call, put, select }): Generator {
      try {
        yield call(deleteTemplateApi, { Id: payload.id });
        message.success('删除模板成功');
        const { templates, templatePagination } = yield select((state: { templateManagement: TemplateManagementState }) => state.templateManagement);
        const updatedList = templates.filter((t: ApiTemplate) => t.Id !== payload.id);
        yield put({ type: 'save', payload: { templates: updatedList } });
        yield put({
          type: 'fetchTemplates',
          payload: { pagination: { ...templatePagination, current: 1 } },
        });
      } catch (e) {
        message.error('删除模板失败');
      }
    },

    *deleteGroupTemplate({ payload }, { call, put, select }): Generator {
      try {
        yield call(deleteGroupTemplateApi, { Id: payload.id });
        message.success('删除模板成功');
        const { groupTemplates, groupTemplatePagination } = yield select((state: { templateManagement: TemplateManagementState }) => state.templateManagement);
        const updatedGroup = groupTemplates.filter((t: ApiGroupTemplate) => t.Id !== payload.id);
        yield put({ type: 'save', payload: { groupTemplates: updatedGroup } });
        yield put({
          type: 'fetchGroupTemplates',
          payload: { pagination: { ...groupTemplatePagination, current: 1 } },
        });
      } catch (e) {
        message.error('删除模板失败');
      }
    },

    *fetchGroupTemplates({ payload }, { call, put }): Generator {
      yield put({ type: 'save', payload: { groupTemplatesLoading: true } });
      const { pagination } = payload;
      try {
        const res: { data?: { TemplateList: ApiGroupTemplate[], Total: number }, Data?: { TemplateList: ApiGroupTemplate[], Total: number } } = yield call(listGroupTemplate, {
          Limit: -1,
          Offset: -1,
          SearchItem: pagination.SearchItem,
        });
        const result: any = res && (res as any).Data ? (res as any).Data : (res as any).data ? (res as any).data : res;
        if (result?.TemplateList) {
          yield put({
            type: 'save',
            payload: {
              groupTemplates: result.TemplateList,
              groupTemplatePagination: { ...pagination, total: result.Total ?? result.TemplateList.length },
            },
          });
        }
      } catch (e) {
        message.error('获取团队模板列表失败');
      } finally {
        yield put({ type: 'save', payload: { groupTemplatesLoading: false } });
      }
    },

    *fetchGroupCategories({ payload }, { call, put }): Generator {
      yield put({ type: 'save', payload: { groupCategoriesLoading: true } });
      const { pagination } = payload;
      try {
        const res: { data?: { TemplateTypeList: ApiGroupCategory[], Total: number }, Data?: { TemplateTypeList: ApiGroupCategory[], Total: number } } = yield call(listGroupTemplateType, {
          Limit: -1,
          Offset: -1,
          SearchItem: pagination.SearchItem,
        });
        const result: any = res && (res as any).Data ? (res as any).Data : (res as any).data ? (res as any).data : res;
        if (result?.TemplateTypeList) {
          yield put({
            type: 'save',
            payload: {
              groupCategories: result.TemplateTypeList,
              groupCategoryPagination: { ...pagination, total: result.Total ?? result.TemplateTypeList.length },
            },
          });
        }
      } catch (e) {
        message.error('获取团队模板分类列表失败');
      } finally {
        yield put({ type: 'save', payload: { groupCategoriesLoading: false } });
      }
    },

    *fetchGroupTemplateContent({ payload }, { call, put }): Generator {
      yield put({ type: 'save', payload: { modalLoading: true } });
      try {
        const res: { data?: any; Template?: any } = yield call(getGroupTemplateContent, payload);
        const content = res?.data || res?.Template || res;
        if (content) {
          yield put({ type: 'save', payload: { editingTemplateContent: content } });
        }
      } catch (e) {
        message.error('获取模板内容失败');
      } finally {
        yield put({ type: 'save', payload: { modalLoading: false } });
      }
    },

    *createGroupTemplate({ payload }, { call, put, select }): Generator {
      yield put({ type: 'save', payload: { modalLoading: true } });
      try {
        yield call(createGroupTemplate, payload.params);
        message.success('创建成功');
        payload.onSuccess();
        const { groupTemplatePagination } = yield select((state: { templateManagement: TemplateManagementState }) => state.templateManagement);
        yield put({ type: 'fetchGroupTemplates', payload: { pagination: groupTemplatePagination } });
      } finally {
        yield put({ type: 'save', payload: { modalLoading: false } });
      }
    },

    *updateGroupTemplate({ payload }, { call, put, select }): Generator {
      yield put({ type: 'save', payload: { modalLoading: true } });
      try {
        yield call(updateGroupTemplateApi, payload.params);
        message.success('更新成功');
        payload.onSuccess && payload.onSuccess();
        const { groupTemplatePagination } = yield select((state: { templateManagement: TemplateManagementState }) => state.templateManagement);
        yield put({ type: 'fetchGroupTemplates', payload: { pagination: groupTemplatePagination } });
      } catch (e) {
        // message.error('更新失败');
      } finally {
        yield put({ type: 'save', payload: { modalLoading: false } });
      }
    },

    *updateGroupCategory({ payload }, { call, put, select }): Generator {
      try {
        yield call(updateGroupTemplateCategoryApi, { Id: payload.id, Name: payload.name });
        message.success('更新分类成功');
        if (payload.onSuccess) payload.onSuccess();
        const { groupCategoryPagination } = yield select((state: { templateManagement: TemplateManagementState }) => state.templateManagement);
        yield put({ type: 'fetchGroupCategories', payload: { pagination: groupCategoryPagination } });
      } catch (e) {
        // message.error('更新分类失败');
        if (payload.onFailure) payload.onFailure();
      }
    },

    // 新增：个人模板图片上传
    *uploadTemplateImage({ payload }, { call, put }): Generator {
      yield put({ type: 'save', payload: { imageUploading: true } });
      try {
        const res: { Name: string } = yield call(saveTemplateImage, payload.file);
        const imageName = res?.Name;
        if (imageName) {
          message.success('图片上传成功');
          if (payload.onSuccess) {
            payload.onSuccess(imageName);
          }
        } else {
          message.error('图片上传失败：未获取到图片名称');
        }
      } catch (e) {
        message.error('图片上传失败');
        if (payload.onFailure) {
          payload.onFailure();
        }
      } finally {
        yield put({ type: 'save', payload: { imageUploading: false } });
      }
    },

    // 新增：团队模板图片上传
    *uploadGroupTemplateImage({ payload }, { call, put }): Generator {
      yield put({ type: 'save', payload: { imageUploading: true } });
      try {
        const res: { Name: string } = yield call(saveGroupTemplateImage, payload.file);
        const imageName = res?.Name;
        if (imageName) {
          message.success('图片上传成功');
          if (payload.onSuccess) {
            payload.onSuccess(imageName);
          }
        } else {
          message.error('图片上传失败：未获取到图片名称');
        }
      } catch (e) {
        message.error('图片上传失败');
        if (payload.onFailure) {
          payload.onFailure();
        }
      } finally {
        yield put({ type: 'save', payload: { imageUploading: false } });
      }
    },
  },

  reducers: {
    save(state, { payload }) {
      return { ...state, ...payload };
    },
    clearEditingContent(state) {
      const emptyState: TemplateManagementState = {
        templates: [],
        templatePagination: { current: 1, pageSize: 10, total: 0, SearchItem: '' },
        templatesLoading: false,
        categories: [],
        categoryPagination: { current: 1, pageSize: 10, total: 0, SearchItem: '' },
        categoriesLoading: false,
        groupTemplates: [],
        groupTemplatePagination: { current: 1, pageSize: 10, total: 0, SearchItem: '' },
        groupTemplatesLoading: false,
        groupCategories: [],
        groupCategoryPagination: { current: 1, pageSize: 10, total: 0, SearchItem: '' },
        groupCategoriesLoading: false,
        templateTypes: [],
        editingTemplateContent: null,
        modalLoading: false,
        imageUploading: false,
      };
      if (!state) return emptyState;
      return { ...state, editingTemplateContent: null };
    }
  },
};

export default Model;