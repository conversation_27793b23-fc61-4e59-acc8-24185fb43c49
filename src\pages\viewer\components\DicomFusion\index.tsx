import {
  RenderingEngine,
  getRenderingEngine,
  Enums,
  Types,
  setVolumesForViewports,
  utilities as csUtils,
} from '@cornerstonejs/core';
import { volumeLoader } from '@cornerstonejs/core';
//import { Enums as csToolsEnums } from '@cornerstonejs/tools';
import React, { useEffect, useRef, useState, useMemo, useCallback, Key, WheelEventHandler } from 'react';
import styles from './index.less';
import { IRenderingEngine, IVolumeViewport } from '@cornerstonejs/core/dist/types/types';
const { ViewportType, Events } = Enums;

import { connect, useSelector, type Dispatch } from 'umi';
import type { ViewType } from '@/models/views';
import { Col, Row, message } from 'antd';

import * as cornerstoneTools from '@cornerstonejs/tools';
import { initCornerstone, addManipulationBindings } from '@/components/prints/cornerstone/index';
import { useDeepCompareEffectDebounce } from '@ant-design/pro-components';
import { set } from '@kitware/vtk.js/macros';
import setPetColorMapTransferFunctionForVolumeActor from '@/utils/petColorMap';

interface IDicomRenderProps {
  dispatch: Dispatch;
}

interface Props extends IDicomRenderProps {
  selectedCt?: any;
  selectedPt?: any;
  instanceListsCT?: any[];
  instanceListsPT?: any[];
  selectedPtTags?: any;
}

// 视口ID
const viewportIdCT = 'CT_AXIAL';
const viewportIdPT = 'PT_AXIAL';
const viewportIdFusion = 'FUSION_AXIAL';
const viewportIdMIP = 'MIP_AXIAL'; // 为MIP预留ID，但暂不使用

// 体积ID - 使用不同的ID以避免冲突
const volumeIdCT = 'cornerstoneStreamingImageVolume: CTVolume';
const volumeIdPT = 'cornerstoneStreamingImageVolume: PTVolume';
const volumeIdFusionCT = 'cornerstoneStreamingImageVolume: FusionCTVolume';
const volumeIdFusionPT = 'cornerstoneStreamingImageVolume: FusionPTVolume';

// 渲染引擎ID
const renderingEngineIdCT = 'viewerEngineCT';
const renderingEngineIdPT = 'viewerEnginePT';
const renderingEngineIdFusion = 'viewerEngineFusion';

const DicomFusion: React.FC<Props> = React.memo((props) => {
  const { dispatch, selectedCt, selectedPt, instanceListsCT, instanceListsPT, selectedPtTags } = props;
  const axialEleRefCT = useRef() as React.MutableRefObject<HTMLDivElement>; // CT轴位图
  const axialEleRefPT = useRef() as React.MutableRefObject<HTMLDivElement>; // PT轴位图
  const axialEleRefFusion = useRef() as React.MutableRefObject<HTMLDivElement>; // 融合轴位图
  const axialEleRefMIP = useRef() as React.MutableRefObject<HTMLDivElement>; // MIP图 - 暂不使用

  const [renderingEngineCT, setRenderingEngineCT] = useState<IRenderingEngine>();
  const [renderingEnginePT, setRenderingEnginePT] = useState<IRenderingEngine>();
  const [renderingEngineFusion, setRenderingEngineFusion] = useState<IRenderingEngine>();

  const [isInitialized, setIsInitialized] = useState(false);
  const [imageIdsCT, setImageIdsCT] = useState<string[]>([]);
  const [imageIdsPT, setImageIdsPT] = useState<string[]>([]);

  // 添加加载状态管理
  const [loadingStates, setLoadingStates] = useState({
    ct: false,
    pt: false,
    fusion: false,
    ctCompleted: false,
    ptCompleted: false,
    fusionCompleted: false,
  });

  // 初始化cornerstone和渲染引擎
  useEffect(() => {
    const initializeEngines = async () => {
      try {
        await initCornerstone();

        console.log('开始创建渲染引擎...');
        const renderingEngineCT: IRenderingEngine = new RenderingEngine(renderingEngineIdCT);
        const renderingEnginePT: IRenderingEngine = new RenderingEngine(renderingEngineIdPT);
        const renderingEngineFusion: IRenderingEngine = new RenderingEngine(renderingEngineIdFusion);

        console.log('渲染引擎已创建:', {
          CT: renderingEngineIdCT,
          PT: renderingEngineIdPT,
          Fusion: renderingEngineIdFusion,
        });

        setRenderingEngineCT(renderingEngineCT);
        setRenderingEnginePT(renderingEnginePT);
        setRenderingEngineFusion(renderingEngineFusion);
        setIsInitialized(true);

        console.log('所有渲染引擎初始化完成');
      } catch (error) {
        console.error('初始化渲染引擎失败:', error);
        message.error('初始化渲染引擎失败，请刷新页面重试');
      }
    };

    initializeEngines();

    // 组件卸载时清理资源
    return () => {
      try {
        if (renderingEngineCT) renderingEngineCT.destroy();
        if (renderingEnginePT) renderingEnginePT.destroy();
        if (renderingEngineFusion) renderingEngineFusion.destroy();
      } catch (error) {
        console.error('销毁渲染引擎失败:', error);
      }
    };
  }, []);

  useEffect(() => {
    if (selectedCt && selectedPt) {
      dispatch({
        type: 'views/fetchInstancesCT',
        payload: selectedCt.SeriesId,
      });
      dispatch({
        type: 'views/fetchInstancesPT',
        payload: selectedPt.SeriesId,
      });
    }
  }, [selectedCt, selectedPt]);

  // 获取CT和PT的图片URL
  useEffect(() => {
    if (instanceListsCT && instanceListsPT) {
      console.log('开始设置图像ID...');
      console.log('CT实例数量:', instanceListsCT.length);
      console.log('PT实例数量:', instanceListsPT.length);

      const ctImageIds = instanceListsCT.map((instance: any) => {
        return `wadouri:http://27.17.30.150:40095/public/getInstanceDicom?InstanceId=${instance.Id}`;
      });

      const ptImageIds = instanceListsPT.map((instance: any) => {
        return `wadouri:http://27.17.30.150:40095/public/getInstanceDicom?InstanceId=${instance.Id}`;
      });

      console.log('CT图像ID设置完成:', ctImageIds.length);
      console.log('PT图像ID设置完成:', ptImageIds.length);

      setImageIdsCT(ctImageIds);
      setImageIdsPT(ptImageIds);
    }
  }, [instanceListsCT, instanceListsPT]);

  // 加载CT图像
  const loadCTImage = async () => {
    if (!imageIdsCT || !imageIdsCT.length || !renderingEngineCT) return;

    console.log('开始加载CT图像...');
    setLoadingStates((prev) => ({ ...prev, ct: true, ctCompleted: false }));

    try {
      const viewportInputCT = {
        viewportId: viewportIdCT,
        type: ViewportType.ORTHOGRAPHIC,
        element: axialEleRefCT.current,
        defaultOptions: {
          orientation: Enums.OrientationAxis.AXIAL,
        },
      };

      renderingEngineCT.enableElement(viewportInputCT);
      const viewport = renderingEngineCT.getViewport(viewportIdCT) as Types.IVolumeViewport;

      const volume = await volumeLoader.createAndCacheVolume(volumeIdCT, {
        imageIds: imageIdsCT,
      });
      await volume.load();

      viewport.setVolumes([{ volumeId: volumeIdCT }]);

      // 设置窗宽窗位
      viewport.setProperties({
        voiRange: {
          lower: -150,
          upper: 300,
        },
      });

      renderingEngineCT.render();

      console.log('CT图像加载完成');
      setLoadingStates((prev) => ({ ...prev, ct: false, ctCompleted: true }));
    } catch (error) {
      console.error('加载CT图像失败:', error);
      setLoadingStates((prev) => ({ ...prev, ct: false, ctCompleted: false }));
    }
  };

  // 加载PET图像
  const loadPTImage = async () => {
    if (!imageIdsPT || !imageIdsPT.length || !renderingEnginePT) {
      console.error('PET加载条件不满足:', {
        imageIdsPT: imageIdsPT?.length,
        renderingEnginePT: !!renderingEnginePT,
      });
      return;
    }

    console.log('开始加载PET图像...');
    console.log('PET渲染引擎ID:', renderingEngineIdPT);
    console.log('PET视口ID:', viewportIdPT);
    console.log('PET图像数量:', imageIdsPT.length);
    setLoadingStates((prev) => ({ ...prev, pt: true, ptCompleted: false }));

    try {
      // 检查DOM元素是否存在且唯一
      if (!axialEleRefPT.current) {
        throw new Error('PET DOM元素不存在');
      }

      // 验证DOM元素的唯一性和内容
      console.log('PET DOM元素验证:', {
        element: axialEleRefPT.current,
        className: axialEleRefPT.current.className,
        innerHTML: axialEleRefPT.current.innerHTML,
        parentElement: axialEleRefPT.current.parentElement?.className,
        hasCanvas: !!axialEleRefPT.current.querySelector('canvas'),
      });

      // 检查是否已经有视口绑定到这个元素
      const existingViewports = renderingEnginePT.getViewports();
      console.log(
        'PET渲染引擎现有视口:',
        existingViewports.map((vp) => vp.id)
      );

      const viewportInputPT = {
        viewportId: viewportIdPT,
        type: ViewportType.ORTHOGRAPHIC,
        element: axialEleRefPT.current,
        defaultOptions: {
          orientation: Enums.OrientationAxis.AXIAL,
        },
      };

      console.log('PET视口配置:', viewportInputPT);
      console.log('PET DOM元素:', axialEleRefPT.current);

      // 如果视口已存在，先禁用
      try {
        const existingViewport = renderingEnginePT.getViewport(viewportIdPT);
        if (existingViewport) {
          console.log('发现已存在的PET视口，先禁用');
          renderingEnginePT.disableElement(viewportIdPT);
        }
      } catch (e) {
        // 视口不存在，这是正常的
        console.log('PET视口不存在，准备创建新视口');
      }

      renderingEnginePT.enableElement(viewportInputPT);
      const viewport = renderingEnginePT.getViewport(viewportIdPT) as Types.IVolumeViewport;

      console.log('PET视口已创建:', viewport);

      const volume = await volumeLoader.createAndCacheVolume(volumeIdPT, {
        imageIds: imageIdsPT,
      });
      await volume.load();

      console.log('PET体积已加载:', volume);

      const wrappedCallback = (volumeInfo: any) => {
        console.log('PET颜色映射回调被调用:', volumeInfo);
        setPetColorMapTransferFunctionForVolumeActor(volumeInfo, selectedPtTags);
      };

      viewport.setVolumes([
        {
          volumeId: volumeIdPT,
          callback: wrappedCallback,
        },
      ]);

      console.log('PET体积已设置到视口');

      // 设置窗宽窗位 - 调整为更适合PET显示的范围
      viewport.setProperties({
        voiRange: {
          lower: -100,
          upper: 1000,
        },
      });

      console.log('PET窗宽窗位已设置: lower=-100, upper=1000');

      renderingEnginePT.render();

      // 强制多次渲染确保显示
      setTimeout(() => {
        renderingEnginePT.render();
        console.log('PET图像延迟渲染完成');
      }, 100);

      setTimeout(() => {
        renderingEnginePT.render();
        console.log('PET图像二次延迟渲染完成');
      }, 500);

      console.log('PET图像加载完成，开始渲染');
      console.log(
        'PET渲染引擎最终视口列表:',
        renderingEnginePT.getViewports().map((vp) => vp.id)
      );
      setLoadingStates((prev) => ({ ...prev, pt: false, ptCompleted: true }));
    } catch (error) {
      console.error('加载PET图像失败:', error);
      setLoadingStates((prev) => ({ ...prev, pt: false, ptCompleted: false }));
    }
  };

  // 加载融合图像
  const loadFusionImage = async () => {
    if (!imageIdsCT || !imageIdsCT.length || !imageIdsPT || !imageIdsPT.length || !renderingEngineFusion) return;

    console.log('开始加载融合图像...');
    console.log('融合图像DOM元素验证:', {
      element: axialEleRefFusion.current,
      className: axialEleRefFusion.current?.className,
      innerHTML: axialEleRefFusion.current?.innerHTML,
      parentElement: axialEleRefFusion.current?.parentElement?.className,
      hasCanvas: !!axialEleRefFusion.current?.querySelector('canvas'),
    });

    setLoadingStates((prev) => ({ ...prev, fusion: true, fusionCompleted: false }));

    try {
      const viewportInputFusion = {
        viewportId: viewportIdFusion,
        type: ViewportType.ORTHOGRAPHIC,
        element: axialEleRefFusion.current,
        defaultOptions: {
          orientation: Enums.OrientationAxis.AXIAL,
        },
      };

      renderingEngineFusion.enableElement(viewportInputFusion);
      const viewport = renderingEngineFusion.getViewport(viewportIdFusion) as Types.IVolumeViewport;

      // 使用不同的体积ID以避免冲突
      const volumeCT = await volumeLoader.createAndCacheVolume(volumeIdFusionCT, {
        imageIds: imageIdsCT,
      });
      await volumeCT.load();

      const volumePT = await volumeLoader.createAndCacheVolume(volumeIdFusionPT, {
        imageIds: imageIdsPT,
      });
      await volumePT.load();

      // 使用闭包封装 ptTags 到 callback 中
      const wrappedCallback = (volumeInfo: any) => {
        console.log('应用PET颜色映射，volumeInfo:', volumeInfo);
        console.log('使用的ptTags:', selectedPtTags);
        setPetColorMapTransferFunctionForVolumeActor(volumeInfo, selectedPtTags);
      };

      viewport.setVolumes([
        { volumeId: volumeIdFusionCT },
        {
          volumeId: volumeIdFusionPT,
          callback: wrappedCallback,
        },
      ]);

      // 设置窗宽窗位，增强融合图像显示
      viewport.setProperties({
        voiRange: {
          lower: -150,
          upper: 300,
        },
      });

      renderingEngineFusion.render();
      console.log('融合图像渲染完成');
      setLoadingStates((prev) => ({ ...prev, fusion: false, fusionCompleted: true }));
    } catch (error) {
      console.error('加载融合图像失败:', error);
      setLoadingStates((prev) => ({ ...prev, fusion: false, fusionCompleted: false }));
    }
  };

  // 序列化加载所有图像
  useEffect(() => {
    const loadImagesSequentially = async () => {
      if (!isInitialized || !imageIdsCT.length || !imageIdsPT.length) {
        return;
      }

      // 防止重复加载
      if (loadingStates.ct || loadingStates.pt || loadingStates.fusion) {
        console.log('已有图像正在加载中，跳过重复加载');
        return;
      }

      try {
        console.log('开始序列化加载图像...');
        console.log('加载状态:', loadingStates);

        // 第一步：加载CT图像
        console.log('第一步：加载CT图像');
        await loadCTImage();

        // 等待CT完成后再加载PET
        console.log('第二步：加载PET图像');
        await loadPTImage();

        // 等待PET完成后再加载融合图像
        console.log('第三步：加载融合图像');
        await loadFusionImage();

        console.log('所有图像加载完成');

        // 最终验证所有DOM元素状态
        setTimeout(() => {
          console.log('=== 最终DOM状态验证 ===');
          console.log('CT DOM状态:', {
            hasCanvas: !!axialEleRefCT.current?.querySelector('canvas'),
            canvasCount: axialEleRefCT.current?.querySelectorAll('canvas').length,
            innerHTML: axialEleRefCT.current?.innerHTML.substring(0, 100),
          });
          console.log('PET DOM状态:', {
            hasCanvas: !!axialEleRefPT.current?.querySelector('canvas'),
            canvasCount: axialEleRefPT.current?.querySelectorAll('canvas').length,
            innerHTML: axialEleRefPT.current?.innerHTML.substring(0, 100),
          });
          console.log('融合 DOM状态:', {
            hasCanvas: !!axialEleRefFusion.current?.querySelector('canvas'),
            canvasCount: axialEleRefFusion.current?.querySelectorAll('canvas').length,
            innerHTML: axialEleRefFusion.current?.innerHTML.substring(0, 100),
          });

          // 验证所有渲染引擎的视口
          console.log(
            'CT渲染引擎视口:',
            renderingEngineCT?.getViewports().map((vp) => vp.id)
          );
          console.log(
            'PET渲染引擎视口:',
            renderingEnginePT?.getViewports().map((vp) => vp.id)
          );
          console.log(
            '融合渲染引擎视口:',
            renderingEngineFusion?.getViewports().map((vp) => vp.id)
          );
        }, 1000);
      } catch (error) {
        console.error('序列化加载图像失败:', error);
        message.error('加载图像失败，请重试');
      }
    };

    loadImagesSequentially();
  }, [imageIdsCT, imageIdsPT, isInitialized, renderingEngineCT, renderingEnginePT, renderingEngineFusion]);

  return (
    <div className={styles.fusionContainer}>
      <Row gutter={[8, 8]}>
        <Col span={12}>
          <div className={styles.viewportContainer}>
            <div className={styles.viewportTitle}>
              CT {loadingStates.ct && '(加载中...)'}
              {loadingStates.ctCompleted && '(已完成)'}
            </div>
            <div className={styles.viewport} ref={axialEleRefCT} onContextMenu={(e) => e.preventDefault()}></div>
          </div>
        </Col>
        <Col span={12}>
          <div className={styles.viewportContainer}>
            <div className={styles.viewportTitle}>
              PET {loadingStates.pt && '(加载中...)'}
              {loadingStates.ptCompleted && '(已完成)'}
            </div>
            <div className={styles.viewport} ref={axialEleRefPT} onContextMenu={(e) => e.preventDefault()}></div>
          </div>
        </Col>
      </Row>
      <Row gutter={[8, 8]} style={{ marginTop: '8px' }}>
        <Col span={12}>
          <div className={styles.viewportContainer}>
            <div className={styles.viewportTitle}>
              融合 {loadingStates.fusion && '(加载中...)'}
              {loadingStates.fusionCompleted && '(已完成)'}
            </div>
            <div className={styles.viewport} ref={axialEleRefFusion} onContextMenu={(e) => e.preventDefault()}></div>
          </div>
        </Col>
        <Col span={12}>
          <div className={styles.viewportContainer}>
            <div className={styles.viewportTitle}>MIP (暂未实现)</div>
            <div className={styles.viewport} ref={axialEleRefMIP} onContextMenu={(e) => e.preventDefault()}></div>
          </div>
        </Col>
      </Row>
    </div>
  );
});

const mapStateToProps = ({ views }: { views: ViewType }) => {
  const { selectedCt, selectedPt, instanceListsCT, instanceListsPT, selectedPtTags } = views;
  return { selectedCt, selectedPt, instanceListsCT, instanceListsPT, selectedPtTags };
};

export default connect(mapStateToProps)(DicomFusion);
