import React, { useEffect, useRef, useState, useMemo } from 'react';
import { Row, Col } from 'antd';
import { v4 as uuidv4 } from 'uuid';
import { type Dispatch } from 'umi';
import { connect } from 'dva';
import { RenderingEngine, Enums } from '@cornerstonejs/core';
import type { IRenderingEngine } from '@cornerstonejs/core/dist/types/types';
import { initCornerstone } from '@/components/prints/cornerstone/index';
import { ViewType } from '@/models/views';
import { handleMipSeriesChange } from '@/utils/mipReconstruction';
import { layoutNumToArr } from '@/utils/utils';

import Dicom2D from './Dicom2D';

const { ViewportType, Events } = Enums;

interface DisplayView {
  series: any | null;
  orientation: { name: string; value: any } | null;
  span?: number; // 跨行数量，用于MPR布局
}

interface InfoProps {
  dispatch: Dispatch;
  pid?: number;
  studyLists?: any[];
  seriesLists?: any[];
  displayedSeriesList?: any[]; // 添加专门用于显示的序列列表
  instanceLists?: any[];
  curInstances?: Map<string, any[]>;
  studyId?: any;
  row?: number;
  col?: number;
  InstanceTag?: any[];
  selectedSeriesId?: string; // 添加选中的序列ID
  cornerInfoConfig?: any; // 四角信息配置
  directionIndicatorVisible?: boolean; // 方向指示器是否可见
  sensitiveInfoHidden?: boolean; // 敏感信息是否隐藏
  currentTool?: string; // 当前工具
  resetViewport?: boolean; // 重置视口
  windowWidth?: number; // 窗宽
  windowCenter?: number; // 窗位
  annotationDeletedTrigger?: boolean; // 注释删除触发器
  selectedColorMap?: string; // 选中的颜色图
}

const DicomArea: React.FC<InfoProps> = ({
  dispatch,
  studyLists,
  seriesLists,
  displayedSeriesList,
  instanceLists,
  curInstances,
  studyId,
  row,
  col,
  InstanceTag,
  selectedSeriesId,
  cornerInfoConfig,
  directionIndicatorVisible,
  sensitiveInfoHidden,
  currentTool,
  resetViewport,
  windowWidth,
  windowCenter,
  annotationDeletedTrigger,
  selectedColorMap,
}) => {
  // Dicom布局的
  const [dicomLayoutArr, setDicomLayoutArr] = useState<[number, number]>([0, 0]);
  // 对于每一个series创建一个独有的renderingEngineMapRef
  const renderingEngineMapRefs = useRef<Map<string, { uuid: string; renderingEngine: IRenderingEngine }>>(new Map());
  const [dicomLayout, setDicomLayout] = useState<number>(22);
  curInstances = curInstances || new Map();

  const orientations = [
    { name: 'AXIAL', value: Enums.OrientationAxis.AXIAL },
    { name: 'SAGITTAL', value: Enums.OrientationAxis.SAGITTAL },
    { name: 'CORONAL', value: Enums.OrientationAxis.CORONAL },
  ];

  // 拖拽状态管理
  const [isDragging, setIsDragging] = useState(false);
  const [dragOverCell, setDragOverCell] = useState<{ row: number; col: number } | null>(null);

  // 视图方向状态管理
  const [orientation, setOrientation] = useState<{ name: string; value: any }[]>(orientations);

  //记录是否全屏
  const [isFullScreen, setIsFullScreen] = useState(false);
  //记录进入全屏前的布局
  const [prevLayout, setPrevLayout] = useState<[number, number]>([0, 0]);
  // 记录当前全屏显示的 DICOM 项
  const [fullScreenItem, setFullScreenItem] = useState<{
    series: any;
    orientation: { name: string; value: any };
  } | null>(null);

  // 设置dicomLayout
  useEffect(() => {
    if (row !== undefined && col !== undefined) {
      console.log('设置布局:', { row, col });
      // 正确设置布局格式：第一位是列数，第二位是行数
      // 例如：23表示2列3行的布局
      const newLayout = (col + 1) * 10 + (row + 1);
      console.log('新布局值:', newLayout);
      setDicomLayout(newLayout);
    }
  }, [row, col]);

  // 根据dicomLayout(11 22 33 num型)计算出对应的行和列
  useEffect(() => {
    setDicomLayoutArr(layoutNumToArr(dicomLayout));
  }, [dicomLayout]);

  // 布局变化时，强制重新渲染所有视口
  useEffect(() => {
    // 给布局变化一点时间来应用
    const timer = setTimeout(() => {
      console.log('布局变化，强制重新渲染所有视口:', dicomLayoutArr);
      // 遍历所有渲染引擎并强制重新渲染
      renderingEngineMapRefs.current.forEach((engineData) => {
        try {
          engineData.renderingEngine.render();
          console.log(`渲染引擎 ${engineData.uuid} 已重新渲染`);

          // 再次延迟渲染，确保完全适应新布局
          setTimeout(() => {
            try {
              engineData.renderingEngine.render();
              console.log(`渲染引擎 ${engineData.uuid} 第二次重新渲染完成`);

              // 第三次延迟渲染，确保图像完全适应新布局
              setTimeout(() => {
                try {
                  engineData.renderingEngine.render();
                  console.log(`渲染引擎 ${engineData.uuid} 第三次重新渲染完成`);

                  // 第四次延迟渲染，确保图像在所有布局情况下都能完全适应
                  setTimeout(() => {
                    try {
                      engineData.renderingEngine.render();
                      console.log(`渲染引擎 ${engineData.uuid} 第四次重新渲染完成`);
                    } catch (error) {
                      console.error('第四次延迟重新渲染失败:', error);
                    }
                  }, 800);
                } catch (error) {
                  console.error('第三次延迟重新渲染失败:', error);
                }
              }, 500);
            } catch (error) {
              console.error('延迟重新渲染失败:', error);
            }
          }, 300);
        } catch (error) {
          console.error(`渲染引擎 ${engineData.uuid} 重新渲染失败:`, error);
        }
      });
    }, 100);

    return () => clearTimeout(timer);
  }, [dicomLayoutArr]);

  // 注释掉这段代码，因为主组件已经处理了studyId的获取和series的加载
  // useEffect(() => {
  //   dispatch({
  //     type: 'views/fetchSeries',
  //     payload: { StudyId: storeUtil.get('studyId').value },
  //   });
  // }, []);

  // 添加一个useEffect，当seriesLists加载完成且有selectedSeriesId时，自动显示该序列
  useEffect(() => {
    if (seriesLists && seriesLists.length > 0) {
      if (selectedSeriesId) {
        const selectedSeries = seriesLists.find((series) => series.SeriesId === selectedSeriesId);

        if (selectedSeries) {
          // 自动显示选中的序列
          updateSeriesForDrop([selectedSeries]);
        } else {
          // 如果找不到选中的序列，则显示第一个序列
          updateSeriesForDrop([seriesLists[0]]);

          // 更新选中的序列ID
          dispatch({
            type: 'views/save',
            payload: { selectedSeriesId: seriesLists[0].SeriesId },
          });
        }
      } else {
        // 如果没有selectedSeriesId，则显示第一个序列
        updateSeriesForDrop([seriesLists[0]]);

        // 设置第一个序列为选中序列
        dispatch({
          type: 'views/save',
          payload: { selectedSeriesId: seriesLists[0].SeriesId },
        });
      }
    }
  }, [seriesLists, selectedSeriesId]);

  //得到所有curInstances，模拟点击第一条
  useEffect(() => {
    if (seriesLists && seriesLists.length > 0) {
      seriesLists.forEach((series) => {
        dispatch({
          type: 'views/fetchInstances',
          payload: series.SeriesId,
        });
      });
    }
  }, [seriesLists]);

  // 获取InstanceTag数据
  useEffect(() => {
    if (curInstances && curInstances.size > 0) {
      // 获取第一个series的第一个instance的标签数据
      const firstSeriesInstances = Array.from(curInstances.values())[0];
      if (firstSeriesInstances && firstSeriesInstances.length > 0) {
        const firstInstanceId = firstSeriesInstances[0].Id;
        dispatch({
          type: 'views/FetchInstanceAllTag',
          payload: firstInstanceId,
        });
      }
    }
  }, [curInstances]);

  //自适应调整显示的seriesLists(填充空白或删掉多余项)
  const adaptiveSeriesList = useMemo(() => {
    // 优先使用 displayedSeriesList，如果不存在则使用 seriesLists
    const sourceSeriesList = displayedSeriesList || seriesLists;

    if (!sourceSeriesList || !dicomLayoutArr) {
      return [];
    }

    // 如果有选中的序列ID，则只显示该序列
    let displaySeriesList = sourceSeriesList;
    if (selectedSeriesId) {
      const selectedSeries = sourceSeriesList.find((series) => series.SeriesId === selectedSeriesId);
      if (selectedSeries) {
        displaySeriesList = [selectedSeries];
      }
    }

    // 在3x3布局中，每行显示一个序列的3个视图，所以最多需要3个序列
    // 对于其他布局，计算能容纳多少个序列
    const itemsPerRow = dicomLayoutArr[0];
    const totalRows = dicomLayoutArr[1];

    // 特殊处理：对于2x2布局（MPR三视图），确保至少有一个序列
    if (itemsPerRow === 2 && totalRows === 2 && displaySeriesList.length === 0 && sourceSeriesList.length > 0) {
      displaySeriesList = [sourceSeriesList[0]];
    }
    const totalItems = itemsPerRow * totalRows;

    let maxSeriesCount;
    if (totalItems >= 9) {
      // 大布局（3x3等），每行对应一个序列
      maxSeriesCount = totalRows;
    } else {
      // 小布局，计算能容纳多少个序列
      maxSeriesCount = Math.floor(totalItems / 3);
      if (totalItems % 3 > 0) {
        maxSeriesCount += 1; // 如果有剩余空间，可以再放一个序列
      }
    }

    // 限制序列数量
    let resultSeriesList;
    if (displaySeriesList.length > maxSeriesCount) {
      resultSeriesList = displaySeriesList.slice(0, maxSeriesCount);
    } else {
      resultSeriesList = displaySeriesList;
    }

    return resultSeriesList;
  }, [dicomLayoutArr, seriesLists, displayedSeriesList, selectedSeriesId]);

  //为每个 seriesId 创建并管理一个独立的 RenderingEngine 实例
  const createRenderingEngine = (seriesId: string) => {
    console.log('createRenderingEngine=+++++++++++++++++++++++++++', seriesId);
    const uuid = `renderingEngine${seriesId}_${uuidv4()}`;
    // 创建渲染引擎实例，并保存到映射中
    const renderingEngine = new RenderingEngine(uuid);
    renderingEngineMapRefs?.current.set(seriesId, {
      uuid,
      renderingEngine,
    });
  };

  useEffect(() => {
    initCornerstone();
  }, []);

  const getRenderingEngine = (seriesId: string) => {
    console.log('getRenderingEngine', seriesId);
    try {
      console.log('renderingEngineMapRefs', renderingEngineMapRefs);
      const engineData = renderingEngineMapRefs.current.get(seriesId);
      // 从映射中获取去渲染引擎实例
      console.log('engineData', engineData);
      if (engineData) {
        return engineData.renderingEngine;
      }
    } catch (error) {
      console.log('getRenderingEngine error', error);
    }
  };

  // orientations已在组件顶部定义

  // 计算 gridData：行列结构的视图数据
  const gridData = useMemo(() => {
    // 全屏模式下只显示一个 DICOM
    if (isFullScreen && fullScreenItem) {
      return [[{ series: fullScreenItem.series, orientation: fullScreenItem.orientation }]];
    }

    const gridItemsPerRow = dicomLayoutArr[0]; // 每行展示几个视图
    const gridTotalRows = dicomLayoutArr[1]; // 总共几行
    const totalItems = gridItemsPerRow * gridTotalRows; // 最大可容纳视图数

    // 特殊处理：当布局为2x2时，显示MPR三视图
    if (gridItemsPerRow === 2 && gridTotalRows === 2 && adaptiveSeriesList.length > 0) {
      const series = adaptiveSeriesList[0]; // 使用第一个序列

      // 新的MPR三视图布局：左上轴状面，左下矢状面，右边（右上+右下）冠状面
      const mprGrid = [
        [
          { series, orientation: orientations[0], span: 1 }, // 左上：轴位 (AXIAL)
          { series, orientation: orientations[2], span: 2 }, // 右上：冠状位 (CORONAL)，跨2行
        ],
        [
          { series, orientation: orientations[1], span: 1 }, // 左下：矢状位 (SAGITTAL)
          null, // 右下：被右上的冠状位占用
        ],
      ];

      return mprGrid;
    }

    // 其他布局的原有逻辑
    const data = [];
    let itemPos = 0;

    const displayViews: DisplayView[] = [];

    // 遍历所有 series，尝试加入它们的三个视图
    for (const series of adaptiveSeriesList) {
      if (!series) continue;

      if (totalItems < 3 && itemPos <= totalItems && itemPos == 0) {
        displayViews.push({ series, orientation: orientations[0] });
        itemPos++;
        continue;
      }

      if (totalItems < 3 && itemPos <= totalItems && itemPos == 1) {
        displayViews.push({ series, orientation: orientations[1] });
        itemPos++;
        continue;
      }

      // 如果剩下的空间不足以容纳 3 个视图，跳过该 series
      if (itemPos + orientations.length > totalItems) {
        continue;
      }

      // 添加该 series 的三个视图
      orientations.forEach((orientation) => {
        displayViews.push({ series, orientation });
        itemPos++;
      });
    }

    // 补齐不足的视图，填充空白项
    while (displayViews.length < totalItems) {
      displayViews.push({ series: null, orientation: orientations[0] });
    }

    // 按照行列排布
    for (let i = 0; i < totalItems; i += gridItemsPerRow) {
      const rowData = displayViews.slice(i, i + gridItemsPerRow);
      data.push(rowData);
    }

    return data;
  }, [dicomLayoutArr, orientations, adaptiveSeriesList]);

  //处理双击全屏
  const handleDicomDoubleClick = (series: any, orientation: any) => {
    if (!series) return;

    const [currentCol, currentRow] = dicomLayoutArr;
    console.log('双击处理，当前布局:', { currentCol, currentRow, dicomLayoutArr });

    // 如果已经是 1x1 布局，则退出全屏
    if (currentCol === 1 && currentRow === 1) {
      exitFullScreen();
      return;
    }

    // 否则进入全屏，保存当前布局以便退出全屏时恢复
    console.log('保存当前布局:', dicomLayoutArr);
    setPrevLayout(dicomLayoutArr);
    setFullScreenItem({ series, orientation });
    setDicomLayout(11); // 设置为 1x1 布局
    setIsFullScreen(true);
  };

  //退出全屏
  const exitFullScreen = () => {
    if (!isFullScreen) return;

    // 从prevLayout中获取之前的布局信息
    const [col, row] = prevLayout;
    console.log('退出全屏，恢复布局:', { col, row, prevLayout });

    // 确保正确恢复布局，使用正确的格式：第一位是列数，第二位是行数
    setDicomLayout((col ?? 1) * 10 + (row ?? 1)); // 如 23表示2列3行的布局
    setIsFullScreen(false);

    // 延迟重新渲染所有视口，确保布局正确恢复
    setTimeout(() => {
      renderingEngineMapRefs.current.forEach((engineData) => {
        try {
          engineData.renderingEngine.render();
        } catch (error) {
          console.error(`退出全屏时渲染引擎重新渲染失败:`, error);
        }
      });
    }, 100);
  };

  // 处理拖拽进入
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
  };

  // 处理拖拽进入特定单元格
  const handleDragEnter = (e: React.DragEvent, rowIndex: number, colIndex: number) => {
    e.preventDefault();
    setIsDragging(true);
    setDragOverCell({ row: rowIndex, col: colIndex });
  };

  // 处理拖拽离开特定单元格
  const handleDragLeave = (e: React.DragEvent, rowIndex: number, colIndex: number) => {
    e.preventDefault();
    // 只有当真正离开该单元格时才清除状态
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setDragOverCell(null);
    }
  };

  // 处理拖拽放置
  const handleDrop = (e: React.DragEvent, targetRowIndex: number, targetColIndex: number) => {
    e.preventDefault();
    setIsDragging(false);
    setDragOverCell(null);

    try {
      const dragData = JSON.parse(e.dataTransfer.getData('application/json'));
      // console.log('拖拽数据:', dragData);
      // console.log('拖拽目标位置:', { targetRowIndex, targetColIndex });

      if (dragData.type === 'series') {
        handleSeriesDrop(dragData.seriesId, targetRowIndex, targetColIndex);
      }
    } catch (error) {
      console.error('处理拖拽数据失败:', error);
    }
  };

  // 处理序列拖拽放置逻辑
  const handleSeriesDrop = (seriesId: string, targetRowIndex: number, targetColIndex: number) => {
    // 获取要拖拽的序列数据
    const targetSeries = seriesLists?.find((series) => series.SeriesId === seriesId);
    if (!targetSeries) {
      console.error('未找到目标序列:', seriesId);
      return;
    }

    const itemsPerRow = dicomLayoutArr[0];
    const totalRows = dicomLayoutArr[1];
    const totalItems = itemsPerRow * totalRows;

    // 计算拖拽位置对应的视图索引
    const dropPosition = targetRowIndex * itemsPerRow + targetColIndex;

    // 根据布局逻辑处理拖拽
    if (totalItems < 3) {
      // 小布局（如1x1, 1x2, 2x1），直接替换
      updateSeriesForDrop([targetSeries]);
    } else if (totalItems >= 9) {
      // 大布局（如3x3），智能插入/替换
      handleLargeLayoutDrop(targetSeries, targetRowIndex, targetColIndex);
    } else {
      // 中等布局（如2x2, 1x3, 3x1等），替换当前序列
      updateSeriesForDrop([targetSeries]);
    }
  };

  // 处理大布局的拖拽逻辑
  const handleLargeLayoutDrop = (targetSeries: any, targetRowIndex: number, targetColIndex: number) => {
    const currentSeriesLists = displayedSeriesList || seriesLists || [];

    // 重新设计逻辑：拖拽到第N行时，只显示前N行的序列
    let newSeriesLists = [];

    // 创建新的序列数组：只保留到目标行为止的序列
    for (let i = 0; i <= targetRowIndex; i++) {
      if (i === targetRowIndex) {
        // 目标行放置拖拽的序列
        newSeriesLists[i] = targetSeries;
      } else {
        // 目标行之前的行保持原有序列（如果存在）
        if (currentSeriesLists.length > i && currentSeriesLists[i]) {
          newSeriesLists[i] = currentSeriesLists[i];
        }
      }
    }

    // 过滤掉空的序列，只保留有效的序列
    const validSeriesLists = newSeriesLists.filter((series) => series !== null && series !== undefined);

    updateSeriesForDrop(validSeriesLists);
  };

  // 更新序列数据并获取instances
  const updateSeriesForDrop = (newSeriesLists: any[]) => {
    // 重要修改：不再更新全局的 seriesLists，而是使用一个新的状态字段
    dispatch({
      type: 'views/save',
      payload: {
        displayedSeriesList: newSeriesLists, // 使用新的字段专门管理显示的序列
        // 不再清除selectedSeriesId，保留当前选中状态
      },
    });

    // 获取新序列的instances数据
    newSeriesLists.forEach((series) => {
      if (series && series.SeriesId) {
        dispatch({
          type: 'views/fetchInstances',
          payload: series.SeriesId,
        });
      }
    });

    // 获取第一个序列的标签数据
    if (newSeriesLists.length > 0 && newSeriesLists[0]) {
      // 延迟获取标签数据，等待instances加载完成
      setTimeout(() => {
        // 先获取instances，然后从中提取第一个实例的Id
        const firstSeriesId = newSeriesLists[0].SeriesId;
        const firstSeriesInstances = curInstances?.get(String(firstSeriesId));
        if (firstSeriesInstances && firstSeriesInstances.length > 0) {
          const firstInstanceId = firstSeriesInstances[0].Id;
          dispatch({
            type: 'views/FetchInstanceAllTag',
            payload: firstInstanceId,
          });
        }
      }, 500);
    }

    // 处理MIP重建的序列切换
    setTimeout(() => {
      try {
        handleMipSeriesChange();
      } catch (error) {
        console.warn('拖拽序列后MIP重建状态重置失败:', error);
      }
    }, 800); // 延迟执行，确保新序列完全加载
  };

  // 特殊处理2x2布局，使用CSS Grid
  if (dicomLayoutArr[0] === 2 && dicomLayoutArr[1] === 2 && adaptiveSeriesList.length > 0) {
    const series = adaptiveSeriesList[0];

    // 确保为每个视图创建渲染引擎
    const seriesId = series.SeriesId;
    if (!getRenderingEngine(seriesId)) createRenderingEngine(seriesId);

    return (
      <div
        style={{
          width: '100%',
          height: '100%',
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gridTemplateRows: '1fr 1fr',
          gap: '1px',
          cursor: currentTool === 'Pan' ? 'grab' : 'default',
          backgroundColor: '#333',
          boxSizing: 'border-box', // 确保盒模型计算包含边框和内边距
          overflow: 'hidden', // 防止内容溢出
        }}
        onDragOver={handleDragOver}
      >
        {/* 左上：轴状面 */}
        <div
          style={{
            gridColumn: '1',
            gridRow: '1',
            border: '1px solid #333',
            backgroundColor: '#000',
          }}
          onDoubleClick={() => handleDicomDoubleClick(series, orientations[0])}
          onDrop={(e) => handleDrop(e, 0, 0)}
          onDragOver={handleDragOver}
          onDragEnter={(e) => handleDragEnter(e, 0, 0)}
          onDragLeave={(e) => handleDragLeave(e, 0, 0)}
        >
          <Dicom2D
            key={`${seriesId}-orientation-AXIAL-0`}
            renderIndex={0}
            row={1}
            col={1}
            seriesId={seriesId}
            curInstances={curInstances}
            getRenderingEngine={getRenderingEngine}
            orientation={orientations[0].value}
            seriesLists={seriesLists}
            views={{
              seriesLists,
              displayedSeriesList,
              selectedSeriesId,
            }}
            onSeriesChange={(newSeriesId) => {
              dispatch({
                type: 'views/selectSeries',
                payload: { seriesId: newSeriesId },
              });
            }}
            onOrientationChange={(newOrientation) => {
              console.log('DicomArea: 改变方向', newOrientation);
              const newOrientationArr = [...orientations];
              const foundOrientation = orientations.find((item) => item.value === newOrientation);
              if (foundOrientation) newOrientationArr[0] = foundOrientation;
              if (newOrientationArr) setOrientation(newOrientationArr);
              console.log('DicomArea: 改变方向', newOrientationArr);
            }}
          />
          {/* 拖拽覆盖层 */}
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 123, 255, 0.1)',
              border: '2px dashed rgba(0, 123, 255, 0.5)',
              display: isDragging && dragOverCell?.row === 0 && dragOverCell?.col === 0 ? 'flex' : 'none',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#007bff',
              fontWeight: 'bold',
              pointerEvents: 'none',
            }}
          >
            替换序列
          </div>
        </div>

        {/* 左下：矢状面 */}
        <div
          style={{
            gridColumn: '1',
            gridRow: '2',
            border: '1px solid #333',
            backgroundColor: '#000',
          }}
          onDoubleClick={() => handleDicomDoubleClick(series, orientations[1])}
          onDrop={(e) => handleDrop(e, 1, 0)}
          onDragOver={handleDragOver}
          onDragEnter={(e) => handleDragEnter(e, 1, 0)}
          onDragLeave={(e) => handleDragLeave(e, 1, 0)}
        >
          <Dicom2D
            key={`${seriesId}-orientation-SAGITTAL-1`}
            renderIndex={1}
            row={1}
            col={1}
            seriesId={seriesId}
            curInstances={curInstances}
            getRenderingEngine={getRenderingEngine}
            orientation={orientations[1].value}
            seriesLists={seriesLists}
            views={{
              seriesLists,
              displayedSeriesList,
              selectedSeriesId,
            }}
            onSeriesChange={(newSeriesId) => {
              console.log('newSeriesId', newSeriesId);
              dispatch({
                type: 'views/selectSeries',
                payload: { seriesId: newSeriesId },
              });
            }}
            onOrientationChange={(newOrientation) => {
              const newOrientationArr = [...orientations];
              const foundOrientation = orientations.find((item) => item.value === newOrientation);
              if (foundOrientation) newOrientationArr[1] = foundOrientation;
              if (newOrientationArr) setOrientation(newOrientationArr);
            }}
          />
          {/* 拖拽覆盖层 */}
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 123, 255, 0.1)',
              border: '2px dashed rgba(0, 123, 255, 0.5)',
              display: isDragging && dragOverCell?.row === 1 && dragOverCell?.col === 0 ? 'flex' : 'none',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#007bff',
              fontWeight: 'bold',
              pointerEvents: 'none',
            }}
          >
            替换序列
          </div>
        </div>

        {/* 右边：冠状面（跨两行） */}
        <div
          style={{
            gridColumn: '2',
            gridRow: '1 / 3',
            border: '1px solid #333',
            backgroundColor: '#000',
          }}
          onDoubleClick={() => handleDicomDoubleClick(series, orientations[2])}
          onDrop={(e) => handleDrop(e, 0, 1)}
          onDragOver={handleDragOver}
          onDragEnter={(e) => handleDragEnter(e, 0, 1)}
          onDragLeave={(e) => handleDragLeave(e, 0, 1)}
        >
          <Dicom2D
            key={`${seriesId}-orientation-CORONAL-2`}
            renderIndex={2}
            row={1}
            col={1}
            seriesId={seriesId}
            curInstances={curInstances}
            getRenderingEngine={getRenderingEngine}
            orientation={orientation[2].value}
            seriesLists={seriesLists}
            views={{
              seriesLists,
              displayedSeriesList,
              selectedSeriesId,
            }}
            onSeriesChange={(newSeriesId) => {
              dispatch({
                type: 'views/selectSeries',
                payload: { seriesId: newSeriesId },
              });
            }}
            onOrientationChange={(newOrientation) => {
              const newOrientationArr = [...orientations];
              const foundOrientation = orientations.find((item) => item.value === newOrientation);
              if (foundOrientation) newOrientationArr[2] = foundOrientation;
              if (newOrientationArr) setOrientation(newOrientationArr);
            }}
          />
          {/* 拖拽覆盖层 */}
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 123, 255, 0.1)',
              border: '2px dashed rgba(0, 123, 255, 0.5)',
              display: isDragging && dragOverCell?.row === 0 && dragOverCell?.col === 1 ? 'flex' : 'none',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#007bff',
              fontWeight: 'bold',
              pointerEvents: 'none',
            }}
          >
            替换序列
          </div>
        </div>
      </div>
    );
  }

  // 其他布局使用原有的Row/Col逻辑
  return (
    <Row
      gutter={[0, 0]}
      style={{
        width: '100%',
        height: '100%',
        margin: 0,
        cursor: currentTool === 'Pan' ? 'grab' : 'default',
        position: 'relative',
        display: 'flex',
        flexWrap: 'wrap',
        boxSizing: 'border-box',
        overflow: 'hidden', // 防止内容溢出
      }}
      onDragOver={handleDragOver}
    >
      {gridData.map((row, rowIndex) => (
        <React.Fragment key={`row-${rowIndex}`}>
          {row.map((item, colIndex) => {
            // 处理null值（被跨行元素占用的位置）
            if (item === null) {
              return null;
            }

            const { series, orientation, span } = item;

            // 处理被跨行元素占用的位置（span为0）
            if (span === 0) {
              return null;
            }

            if (!orientation) {
              return null;
            }

            const viewportKey = series && orientation ? `${series.Id}-${orientation.name}` : `empty-${rowIndex}-${colIndex}`;

            if (!series) {
              const isDropTarget = isDragging && dragOverCell?.row === rowIndex && dragOverCell?.col === colIndex;

              // 获取第一行第一列的图像（如果存在）
              const firstSeries = gridData[0]?.[0]?.series;
              const firstOrientation = gridData[0]?.[0]?.orientation;

              // 如果存在第一行第一列的图像，则显示该图像
              if (firstSeries && firstOrientation) {
                const firstSeriesId = firstSeries.SeriesId;
                const hasEngineToFirstSeries = renderingEngineMapRefs.current.has(String(firstSeriesId));
                !hasEngineToFirstSeries && createRenderingEngine(String(firstSeriesId));

                return (
                  <Col
                    span={24 / dicomLayoutArr[0]}
                    style={{
                      height: `calc(100% / ${dicomLayoutArr[1]})`,
                      width: `calc(100% / ${dicomLayoutArr[0]})`, // 确保宽度自适应布局列数
                      position: 'relative',
                      padding: 0,
                      margin: 0,
                      boxSizing: 'border-box',
                      border: isDropTarget ? '2px dashed #007bff' : '1px solid #333',
                      backgroundColor: '#000',
                      overflow: 'hidden', // 防止内容溢出
                    }}
                    key={viewportKey}
                    onDoubleClick={() => handleDicomDoubleClick(firstSeries, firstOrientation)}
                    onDrop={(e) => handleDrop(e, rowIndex, colIndex)}
                    onDragOver={handleDragOver}
                    onDragEnter={(e) => handleDragEnter(e, rowIndex, colIndex)}
                    onDragLeave={(e) => handleDragLeave(e, rowIndex, colIndex)}
                  >
                    <div
                      style={{
                        width: '100%',
                        height: '100%',
                        position: 'relative',
                      }}
                    >
                      <Dicom2D
                        key={`${firstSeries?.seriesId}-orientation-${firstOrientation.name}-${colIndex}-clone`}
                        renderIndex={rowIndex * dicomLayoutArr[0] + colIndex}
                        row={1}
                        col={1}
                        seriesId={firstSeriesId}
                        curInstances={curInstances}
                        getRenderingEngine={getRenderingEngine}
                        orientation={firstOrientation.value}
                        seriesLists={seriesLists}
                        views={{
                          seriesLists,
                          displayedSeriesList,
                          selectedSeriesId,
                        }}
                        onSeriesChange={(newSeriesId) => {
                          dispatch({
                            type: 'views/selectSeries',
                            payload: { seriesId: newSeriesId },
                          });
                        }}
                      />
                      {/* 拖拽覆盖层 */}
                      {isDropTarget && (
                        <div
                          style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            backgroundColor: 'rgba(0, 123, 255, 0.1)',
                            border: '2px dashed rgba(0, 123, 255, 0.5)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: '#007bff',
                            fontWeight: 'bold',
                            pointerEvents: 'none',
                          }}
                        >
                          替换序列
                        </div>
                      )}
                    </div>
                  </Col>
                );
              } else {
                // 如果不存在第一行第一列的图像，则显示原来的拖拽提示
                return (
                  <Col
                    span={24 / dicomLayoutArr[0]}
                    style={{
                      height: `calc(100% / ${dicomLayoutArr[1]})`,
                      width: `calc(100% / ${dicomLayoutArr[0]})`, // 确保宽度自适应布局列数
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      border: isDropTarget ? '2px dashed #007bff' : '1px dashed #ccc',
                      backgroundColor: isDropTarget ? '#e6f3ff' : '#191919',
                      transition: 'all 0.3s ease',
                      padding: 0,
                      margin: 0,
                      boxSizing: 'border-box',
                      overflow: 'hidden', // 防止内容溢出
                    }}
                    key={viewportKey}
                    onDrop={(e) => handleDrop(e, rowIndex, colIndex)}
                    onDragOver={handleDragOver}
                    onDragEnter={(e) => handleDragEnter(e, rowIndex, colIndex)}
                    onDragLeave={(e) => handleDragLeave(e, rowIndex, colIndex)}
                  >
                    <span style={{ color: isDropTarget ? '#007bff' : '#999', fontWeight: isDropTarget ? 'bold' : 'normal' }}>
                      {isDropTarget ? '松开鼠标放置序列' : '拖拽序列到此处'}
                    </span>
                  </Col>
                );
              }
            }

            const seriesId = series.SeriesId;
            const hasEngineToSeries = renderingEngineMapRefs.current.has(String(seriesId));
            !hasEngineToSeries && createRenderingEngine(String(seriesId));

            // 计算高度和列宽
            const colSpan = 24 / dicomLayoutArr[0];
            const height = `calc(100% / ${dicomLayoutArr[1]})`;

            return (
              <Col
                span={colSpan}
                key={viewportKey}
                onDoubleClick={() => handleDicomDoubleClick(series, orientation)}
                onDrop={(e) => handleDrop(e, rowIndex, colIndex)}
                onDragOver={handleDragOver}
                onDragEnter={(e) => handleDragEnter(e, rowIndex, colIndex)}
                onDragLeave={(e) => handleDragLeave(e, rowIndex, colIndex)}
                style={{
                  height: height,
                  position: 'relative',
                  padding: 0,
                  margin: 0,
                  boxSizing: 'border-box',
                  border: '1px solid #333',
                  width: `calc(100% / ${dicomLayoutArr[0]})`, // 确保宽度自适应布局列数
                  overflow: 'hidden', // 防止内容溢出
                }}
              >
                <div
                  style={{
                    width: '100%',
                    height: '100%',
                  }}
                >
                  <Dicom2D
                    key={`${series?.seriesId}-orientation-${orientation.name}-${colIndex}`}
                    renderIndex={rowIndex * dicomLayoutArr[0] + colIndex}
                    row={1}
                    col={1}
                    seriesId={seriesId}
                    curInstances={curInstances}
                    getRenderingEngine={getRenderingEngine}
                    orientation={orientation.value}
                    seriesLists={seriesLists}
                    views={{
                      seriesLists,
                      displayedSeriesList,
                      selectedSeriesId,
                      // 其他必要的 views 属性
                    }}
                    onSeriesChange={(newSeriesId) => {
                      // 更新选中的序列ID
                      dispatch({
                        type: 'views/selectSeries',
                        payload: { seriesId: newSeriesId },
                      });
                    }}
                    onOrientationChange={(newOrientation) => {
                      // 更新视图方向，确保名称和值一致
                      const newOrientationObj = {
                        AXIAL: { name: 'Axial', value: 'AXIAL' },
                        SAGITTAL: { name: 'Sagittal', value: 'SAGITTAL' },
                        CORONAL: { name: 'Coronal', value: 'CORONAL' },
                      }[newOrientation];

                      if (newOrientationObj) {
                        // 立即更新状态
                        setOrientation(newOrientationObj);

                        // 获取当前渲染引擎并强制刷新
                        try {
                          const curRenderingEngine = getRenderingEngine(String(seriesId));
                          if (curRenderingEngine) {
                            // 立即刷新一次
                            curRenderingEngine.render();

                            // 延迟再刷新一次，确保视图完全更新
                            setTimeout(() => {
                              try {
                                curRenderingEngine.render();

                                // 再次延迟刷新，确保视图完全更新
                                setTimeout(() => {
                                  try {
                                    curRenderingEngine.render();
                                  } catch (error) {
                                    console.error('DicomArea视图方向切换后第二次延迟刷新渲染引擎失败:', error);
                                  }
                                }, 300);
                              } catch (error) {
                                console.error('DicomArea视图方向切换后延迟刷新渲染引擎失败:', error);
                              }
                            }, 200);
                          }
                        } catch (error) {
                          console.error('DicomArea获取渲染引擎失败:', error);
                        }
                      }
                    }}
                  />
                </div>
                {/* 拖拽覆盖层 */}
                <div
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    border: '2px dashed rgba(0, 123, 255, 0.5)',
                    display: isDragging && dragOverCell?.row === rowIndex && dragOverCell?.col === colIndex ? 'flex' : 'none',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#007bff',
                    fontWeight: 'bold',
                    pointerEvents: 'none',
                  }}
                  className='drop-overlay'
                >
                  替换序列
                </div>
              </Col>
            );
          })}
        </React.Fragment>
      ))}
    </Row>
  );
};

const mapStateToProps = ({ views }: { views: ViewType }) => {
  return {
    // 阅片model存储的信息
    pid: views.patientId,
    studyLists: views.studyLists,
    seriesLists: views.seriesLists,
    displayedSeriesList: views.displayedSeriesList, // 添加专门用于显示的序列列表
    instanceLists: views.instanceLists,
    curInstances: views.curInstances,
    studyId: views.studyId,
    row: views.row,
    col: views.col,
    InstanceTag: views.InstanceTag,
    selectedSeriesId: views.selectedSeriesId, // 添加选中的序列ID
    cornerInfoConfig: views.cornerInfoConfig, // 四角信息配置
    directionIndicatorVisible: views.directionIndicatorVisible, // 方向指示器是否可见
    sensitiveInfoHidden: views.sensitiveInfoHidden, // 敏感信息是否隐藏
    currentTool: views.currentTool, // 当前工具
    resetViewport: views.resetViewport, // 重置视口
    windowWidth: views.windowWidth, // 窗宽
    windowCenter: views.windowCenter, // 窗位
    annotationDeletedTrigger: views.annotationDeletedTrigger, // 注释删除触发器
    selectedColorMap: views.selectedColorMap, // 选中的颜色图
  };
};

export default connect(mapStateToProps)(DicomArea);
