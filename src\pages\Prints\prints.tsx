import React, { useState, useEffect } from 'react';
import withAuth from '@/hocs/withAuth';
import { Button, message } from 'antd';

import styles from './prints.less';
// @ts-ignore
import { WidthProvider, Responsive } from 'react-grid-layout';
import { TopToolBar, SequencesList, PrintSettings, FilmShowcaseArea } from './components';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import { LAYOUT_BREAK_COLS, LAYOUT_BREAK_POINTS } from '@/utils/consts';
import { useResizeDetector } from 'react-resize-detector';
import printJS from 'print-js';
// @ts-ignore
import _ from 'lodash';
import { history } from 'umi';
import { PanTool, PlanarRotateTool, ZoomTool, addTool, removeTool } from '@cornerstonejs/tools';
// @ts-ignore
import html2canvas from '@html2canvas/html2canvas';
import { jsPDF } from 'jspdf';

const ResponsiveReactGridLayout = WidthProvider(Responsive);

const Prints: React.FC = () => {
  const { width, height, ref: targetRef } = useResizeDetector();
  const [layouts, setLayouts] = useState<any>(undefined);
  const [currentBreakpoint, setCurrentBreakpoint] = useState('lg');
  useEffect(() => {
    if (!localStorage.getItem('PatientId')) {
      history.push('/patients');
      message.info('请先行选择患者进行查看');
    } else {
      // console.log('2DGlobalToolsInited');
      addTool(ZoomTool); // 缩放
      addTool(PanTool); // 平移位移
      addTool(PlanarRotateTool); // 旋转角度
    }
    return () => {
      // console.log('2DGlobalToolsRemoved');
      removeTool(ZoomTool);
      removeTool(PanTool);
      removeTool(PlanarRotateTool);
    };
  }, []);
  useEffect(() => {
    setLayouts({
      lg: [
        {
          i: 'top',
          x: 0,
          y: 0,
          w: 2.5,
          h: 1,
          static: true,
          resizeHandles: [],
        },
        { i: 'list', x: 0, y: 1, w: 2.5, h: 4, resizeHandles: [] },
        { i: 'settings', x: 0, y: 5, w: 2.5, h: 10, resizeHandles: [] },
        { i: 'buttons', x: 0, y: 15.5, w: 2.5, h: 1, resizeHandles: [] },
        { i: 'img', x: 2.5, y: 0, w: 9.5, h: 16.5, resizeHandles: [] },
      ],
      xl: [
        {
          i: 'top',
          x: 0,
          y: 0,
          w: 3,
          h: 2,
          static: true,
          resizeHandles: [],
        },
        { i: 'list', x: 0, y: 2, w: 3, h: 5, resizeHandles: [] },
        { i: 'settings', x: 0, y: 7, w: 3, h: 12 },
        { i: 'buttons', x: 0, y: 19, w: 3, h: 1, static: true },
        { i: 'img', x: 3, y: 0, w: 9, h: 20 },
      ],
    });
  }, []);

  const printCurFilms = () => {
    printJS({
      printable: 'printArea',
      type: 'html',
      maxWidth: '100%',
      targetStyles: ['*'],
      css: './printBlock.css',
      style: '@media print { [class*="selected"] {border: none !important}}',
    });
  };

  const saveAsPDF = () => {
    // 图片形式保存，更加容易
    const containerElement = document.getElementById('printArea') as HTMLElement;
    html2canvas(containerElement, {
      allowTaint: true,
      useCORS: true,
      scale: 2,
      height: containerElement.scrollHeight,
      windowHeight: containerElement.scrollHeight,
    }).then((canvas: any) => {
      // blob
      canvas.toBlob((blob: any) => {
        const href = window.URL.createObjectURL(new Blob([blob]));
        const link = document.createElement('a');
        link.href = href;
        link.download = '胶片打印.png';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }, 'image/png');
    });
  };

  const generateDOM = () => {
    if (!layouts) return <></>;
    return _.map(layouts[currentBreakpoint], (item: any) => {
      return (
        <div key={item.i} style={{ backgroundColor: '#0e0e0e' }}>
          {Contents[item.i]()}
        </div>
      );
    });
  };

  const Contents = {
    top: () => <TopToolBar />,
    list: () => <SequencesList />,
    settings: () => <PrintSettings />,
    buttons: () => (
      <div className={styles.printButtons}>
        <Button style={{ backgroundColor: '#2bbc89', color: '#ffffff' }} onClick={saveAsPDF}>
          保存胶片
        </Button>
        <Button type='primary' onClick={printCurFilms}>
          打印
        </Button>
      </div>
    ),
    img: () => <FilmShowcaseArea />,
  };

  return (
    <div className={styles.pageContainer} ref={targetRef}>
      <ResponsiveReactGridLayout
        className='layout'
        style={{ width: width, height: height }}
        layouts={layouts}
        breakpoints={LAYOUT_BREAK_POINTS}
        cols={LAYOUT_BREAK_COLS} // 12
        isBounded
        rowHeight={50} // 21 row
        verticalCompact
        compactType='horizontal'
        draggableHandle='.dragHandler'
        margin={[2, 2]}
        containerPadding={[0, 0]}
        resizeHandles={['se']}
      >
        {generateDOM()}
      </ResponsiveReactGridLayout>
    </div>
  );
};

export default withAuth(Prints);
