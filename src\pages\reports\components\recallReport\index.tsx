import React, { useEffect, useState } from 'react';
import { Modal, Flex, ConfigProvider, Button, message } from 'antd';
import { CloseOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import styles from './index.less';

interface RecallReportProps {
  visible: boolean;
  handleOk: () => void;
  handleCancel: () => void;
}
const RecallReport: React.FC<RecallReportProps> = ({ visible, handleOk, handleCancel }) => {
  return (
    <div>
      <ConfigProvider
        theme={{
          components: {
            Button: {
              colorBorder: '#d9d9d9',
            },
          },
        }}
      >
        <Modal
          classNames={{
            header: styles.modalHeader,
            content: styles.modalContent,
            body: styles.modalBody,
            footer: styles.modalFooter,
          }}
          title='提示'
          width={900}
          open={visible}
          onOk={handleOk}
          onCancel={handleCancel}
          closeIcon={
            <div className={styles.closeIcon}>
              <CloseOutlined />
            </div>
          }
          footer={(_, { OkBtn, CancelBtn }) => (
            <>
              <CancelBtn />
              <OkBtn />
            </>
          )}
        >
          <Flex style={{ width: '100%', margin: '130px auto 70px', fontSize: '18px' }} justify='center' gap={8}>
            <ExclamationCircleOutlined style={{ color: '#EFB844', fontSize: '22px' }} />
            <span>请确认召回该审核申请吗？ 召回后主任医生将无法审批！</span>
          </Flex>
        </Modal>
      </ConfigProvider>
    </div>
  );
};

export default RecallReport;
