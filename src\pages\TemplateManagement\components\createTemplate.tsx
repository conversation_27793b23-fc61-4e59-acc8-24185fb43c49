import React, { useState, useEffect, useMemo } from 'react';
import { Modal, Form, Input, Select, Button, ConfigProvider, message } from 'antd';
import { connect, Dispatch } from 'umi';
import RichTextEditor from '@/components/RichTextEditor';
import styles from './createTemplate.less';
import { ApiTemplate, ApiTemplateType, ApiGroupTemplate } from '../data';
import { TemplateManagementState } from '../model';

interface CreateTemplateProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  editingTemplate?: ApiTemplate | null;
  editingGroupTemplate?: ApiGroupTemplate | null;
  isGroupTemplate?: boolean;
  dispatch: Dispatch;
  templateManagement: TemplateManagementState;
  readOnly?: boolean;
}

const CreateTemplate: React.FC<CreateTemplateProps> = ({
  visible,
  onClose,
  onSuccess,
  editingTemplate,
  editingGroupTemplate,
  isGroupTemplate = false,
  dispatch,
  templateManagement,
  readOnly: propReadOnly = false,
}) => {
  const [form] = Form.useForm();
  const { templateTypes, groupCategories, modalLoading, editingTemplateContent, imageUploading } = templateManagement;

  const categoryOptions = useMemo(() => {
    return isGroupTemplate ? groupCategories : templateTypes;
  }, [isGroupTemplate, groupCategories, templateTypes]);

  // 判断是否为默认模板的辅助函数
  const isDefaultTemplate = (value: string) => {
    return value === 'default' || value.includes('默认') || value.includes('default');
  };

  // 优先使用外部传递的 readOnly
  const isReadOnly = typeof propReadOnly === 'boolean'
    ? propReadOnly
    : (
      isDefaultTemplate(editingTemplate?.TypeName || '') ||
      isDefaultTemplate(editingGroupTemplate?.TypeName || '') ||
      isDefaultTemplate(editingTemplate?.Name || '') ||
      isDefaultTemplate(editingGroupTemplate?.Name || '')
    );

  useEffect(() => {
    if (visible) {
      if (isGroupTemplate) {
        dispatch({ type: 'templateManagement/fetchGroupCategories', payload: { pagination: { current: 1, pageSize: 100 } } });
      } else {
        dispatch({ type: 'templateManagement/fetchTemplateTypes' });
      }

      if (editingTemplate) {
        form.setFieldsValue({ name: editingTemplate.Name });
        dispatch({
          type: 'templateManagement/fetchTemplateContent',
          payload: { Id: editingTemplate.Id }
        });
      } else if (editingGroupTemplate) {
        form.setFieldsValue({ name: editingGroupTemplate.Name });
        dispatch({
          type: 'templateManagement/fetchGroupTemplateContent',
          payload: { Id: editingGroupTemplate.Id }
        });
      } else {
        form.resetFields();
        dispatch({ type: 'templateManagement/clearEditingContent' });
      }
    }
  }, [visible, editingTemplate, editingGroupTemplate, dispatch, form, isGroupTemplate]);

  useEffect(() => {
    if (editingTemplateContent) {
      const { RadiographicFindingTemplate, ClinicalDiagnosisTemplate } = editingTemplateContent;

      // 添加HTML解码函数来处理转义字符
      const decodeHtml = (html: string) => {
        if (!html) return '';



        // 先处理Unicode转义
        let decoded = html.replace(/\\u[\dA-F]{4}/gi, function (match) {
          return String.fromCharCode(parseInt(match.replace(/\\u/g, ''), 16));
        });



        // 再处理HTML实体
        const txt = document.createElement('textarea');
        txt.innerHTML = decoded;
        const result = txt.value;


        return result;
      };

      const decodedFinding = decodeHtml(RadiographicFindingTemplate || '');
      const decodedConclusion = decodeHtml(ClinicalDiagnosisTemplate || '');

      form.setFieldsValue({
        finding: decodedFinding,
        conclusion: decodedConclusion,
      });
    }
  }, [editingTemplateContent, form]);

  useEffect(() => {
    if (categoryOptions.length === 0) return;
    if (editingTemplate) {
      const type = categoryOptions.find((t: any) => t.Name === editingTemplate.TypeName);
      form.setFieldsValue({ typeId: type?.Id });
    } else if (editingGroupTemplate) {
      const type = categoryOptions.find((t: any) => t.Name === editingGroupTemplate.TypeName);
      form.setFieldsValue({ typeId: type?.Id });
    }
  }, [editingTemplate, editingGroupTemplate, categoryOptions, form]);

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      const params = {
        Id: editingTemplate?.Id || editingGroupTemplate?.Id,
        Name: values.name?.trim(),
        TypeId: values.typeId,
        RadiographicFindingTemplate: values.finding || '',
        ClinicalDiagnosisTemplate: values.conclusion || '',
      };
      let actionType: string;
      if (editingTemplate) {
        actionType = 'updateTemplate';
      } else if (editingGroupTemplate) {
        actionType = 'updateGroupTemplate';
      } else if (isGroupTemplate) {
        actionType = 'createGroupTemplate';
      } else {
        actionType = 'createTemplate';
      }
      dispatch({
        type: `templateManagement/${actionType}`,
        payload: {
          params,
          onSuccess: () => {
            form.resetFields();
            onSuccess();
          }
        }
      });
    } catch (error) {
      // 表单验证失败
    }
  }

  const handleClose = () => {
    form.resetFields();
    dispatch({ type: 'templateManagement/clearEditingContent' });
    onClose();
  }

  // 图片上传处理函数
  const handleImageUpload = (file: File, callback: (imageName: string) => void) => {
    const actionType = isGroupTemplate ? 'uploadGroupTemplateImage' : 'uploadTemplateImage';

    dispatch({
      type: `templateManagement/${actionType}`,
      payload: {
        file,
        onSuccess: (imageName: string) => {
          callback(imageName);
        },
        onFailure: () => {
          message.error('图片上传失败');
        }
      }
    });
  };

  return (
    <Modal
      title={editingTemplate || editingGroupTemplate ? "编辑报告模板" : "新建报告模板"}
      open={visible}
      onCancel={handleClose}
      width={1200}
      style={{ top: 40 }}
      styles={{
        body: { padding: 0 },
        content: { padding: 0, borderRadius: '8px', overflow: 'hidden', backgroundColor: '#333233' },
        header: {
          padding: '16px 24px',
          margin: 0,
          borderBottom: '1px solid #424244',
          backgroundColor: '#4C4C4C'
        },
      }}
      destroyOnClose
      closeIcon={<img src="/icon/report/close.png" alt="close" style={{ width: '24px', height: '24px' }} />}
      footer={(
        <div style={{ padding: '12px 24px', textAlign: 'right' }}>
          <Button key="cancel" onClick={handleClose} style={{ marginRight: 8 }}>{isReadOnly ? '关闭' : '取消'}</Button>
          {!isReadOnly && <Button key="confirm" type="primary" loading={modalLoading} onClick={handleSave} style={{ background: '#1F69B4' }}>保存</Button>}
        </div>
      )}
    >
      <ConfigProvider
        theme={{
          components: {
            // Input: {
            //   colorBgContainer: '#333233',
            //   colorText: 'white',
            //   colorTextPlaceholder: 'rgba(255, 255, 255, 0.45)',
            //   colorBorder: '#555555',
            //   colorTextDescription: 'rgba(255, 255, 255, 0.65)',
            // },
            // Select: {
            //   colorBgContainer: '#333233',
            //   colorText: 'white',
            //   colorTextPlaceholder: 'rgba(255, 255, 255, 0.45)',
            //   colorBorder: '#555555',
            //   colorBgElevated: '#333233',
            //   controlItemBgHover: '#555555',
            //   optionSelectedBg: '#1F69B4',
            // },
          },
        }}
      >
        <div style={{ padding: '24px' }}>
          <Form form={form} layout="vertical" preserve={false}>
            <Form.Item
              name="name"
              label="报告模板名称:"
              rules={[
                { required: true, message: '请输入报告模板名称' },
                {
                  validator: (_, value) => {
                    if (!value || value.trim() === '') {
                      return Promise.reject(new Error('模板名称不能为空或纯空格'));
                    }
                    if (value && value.length > 99) {
                      return Promise.reject(new Error('模板名称不能超过99个字符'));
                    }
                    return Promise.resolve();
                  }
                }
              ]}
            >
              <Input
                placeholder="请输入报告模板名称"
                disabled={isReadOnly}
                maxLength={99}
                showCount={{
                  formatter: ({ count, maxLength }) => count <= 99 ? `${count}/99` : '',
                }}
              />
            </Form.Item>

            <Form.Item
              name="typeId"
              label="模板分类:"
              rules={[{ required: true, message: '请选择模板分类' }]}
            >
              <Select placeholder="请选择模板分类" showArrow disabled={isReadOnly}>
                {categoryOptions.map((type: any) => (
                  <Select.Option key={type.Id} value={type.Id}>
                    {type.Name === 'default' ? '默认分类' : type.Name}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>

            <div style={{
              height: '400px',
              display: 'flex',
              gap: '20px',
              minHeight: 0
            }}>
              <ConfigProvider
                theme={{
                  token: {
                    colorBgContainer: isReadOnly ? '#333233' : 'white',
                    colorBgBase: isReadOnly ? '#333233' : 'white',
                    colorText: isReadOnly ? 'white' : 'black',
                  },
                  components: {
                    Card: {
                      headerHeight: 40,
                    },
                  },
                }}
              >
                <div style={{
                  flex: '1 1 50%',
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  minHeight: 0,
                  minWidth: 0
                }}>
                  {/* 影像所见标题 */}
                  <div style={{
                    color: 'white',
                    fontSize: '14px',
                    fontWeight: 400,
                    marginBottom: '8px',
                    fontFamily: 'Microsoft YaHei UI',
                    flexShrink: 0
                  }}>
                    影像所见
                  </div>
                  <div style={{ flex: 1, minHeight: 0 }}>
                    <Form.Item name="finding" noStyle>
                      <RichTextEditor
                        className={styles.quill}
                        readOnly={isReadOnly}
                        disableToolbar={isReadOnly}
                        maxLength={9999}
                        isGroupTemplate={isGroupTemplate}
                        onImageUpload={handleImageUpload}
                        imageUploading={imageUploading}
                      />
                    </Form.Item>
                  </div>
                </div>

                <div style={{
                  flex: '1 1 50%',
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  minHeight: 0,
                  minWidth: 0
                }}>
                  {/* 诊断意见标题 */}
                  <div style={{
                    color: 'white',
                    fontSize: '14px',
                    fontWeight: 400,
                    marginBottom: '8px',
                    fontFamily: 'Microsoft YaHei UI',
                    flexShrink: 0
                  }}>
                    诊断意见
                  </div>
                  <div style={{ flex: 1, minHeight: 0 }}>
                    <Form.Item name="conclusion" noStyle>
                      <RichTextEditor
                        className={styles.quill}
                        readOnly={isReadOnly}
                        disableToolbar={isReadOnly}
                        maxLength={9999}
                        isGroupTemplate={isGroupTemplate}
                        onImageUpload={handleImageUpload}
                        imageUploading={imageUploading}
                      />
                    </Form.Item>
                  </div>
                </div>
              </ConfigProvider>
            </div>
          </Form>
        </div>
      </ConfigProvider>
    </Modal>
  );
};

const mapStateToProps = ({ templateManagement }: { templateManagement: TemplateManagementState }) => ({
  templateManagement,
});

export default connect(mapStateToProps)(CreateTemplate);
