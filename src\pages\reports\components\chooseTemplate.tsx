import React, { useState, useEffect } from 'react';
import { Modal, Row, Col, List, Typography, Button, ConfigProvider, Collapse } from 'antd';
import { TeamOutlined, UserOutlined } from '@ant-design/icons';
import styles from './chooseTemplate.less';
import { ListTemplateType, ListTemplateByType, getTemplateContent, listGroupTemplateType, listGroupTemplateByType, getGroupTemplateContent } from '@/services/reports';
import type { TemplateCategory, TemplateItem, ApiTemplateType, ApiTemplateItem, TemplateContent } from '../data';
import CustomIcon from '@/components/CustomIcon';

const { Title } = Typography;

interface ChooseTemplateProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (template: any) => void;
  templates?: any[];
}

const ChooseTemplate: React.FC<ChooseTemplateProps> = ({
  visible,
  onClose,
  onConfirm,
  templates = [],
}) => {
  // 当前选中的分类
  const [activeCategory, setActiveCategory] = useState<string>('myTemplates');
  // 当前选中的模板
  const [selectedTemplate, setSelectedTemplate] = useState<TemplateItem | null>(null);
  const [myTemplateTypes, setMyTemplateTypes] = useState<ApiTemplateType[]>([]);
  const [teamTemplateTypes, setTeamTemplateTypes] = useState<ApiTemplateType[]>([]);
  // 当前展开的面板
  const [activeCollapseKey, setActiveCollapseKey] = useState<string | string[]>([]);
  // 分类下的模板列表（用一个对象来存储每个分类的模板）
  const [templatesInType, setTemplatesInType] = useState<Record<string, TemplateItem[]>>({});
  const [teamTemplatesInType, setTeamTemplatesInType] = useState<Record<string, TemplateItem[]>>({});

  // 模板分类数据
  const categories: TemplateCategory[] = [
    {
      key: 'myTemplates',
      title: '我的模板',
      icon: <UserOutlined />,
      templates: templates.filter(t => t.type === 'personal'),
    },
    {
      key: 'teamTemplates',
      title: '团队模板',
      icon: <TeamOutlined />,
      templates: templates.filter(t => t.type === 'team'),
    },
  ];

  // 根据选中的分类获取模板列表
  const activeTemplates = categories.find(c => c.key === activeCategory)?.templates || [];

  useEffect(() => {
    if (visible) {
      const fetchTemplateTypes = async () => {
        try {


          // 获取个人模板分类
          const personalRes = await ListTemplateType({});

          if (personalRes && personalRes.TemplateTypeList) {
            const personalTypes = personalRes.TemplateTypeList;
            setMyTemplateTypes(personalTypes);

          } else {

          }

          // 获取团队模板分类
          const teamRes = await listGroupTemplateType({
            Limit: -1,
            Offset: -1,
          });

          if (teamRes && teamRes.TemplateTypeList) {
            const teamTypes = teamRes.TemplateTypeList;
            setTeamTemplateTypes(teamTypes);

          } else {

          }

          // 默认展开第一个个人模板分类并加载数据
          if (personalRes?.TemplateTypeList?.length > 0) {
            const firstKey = personalRes.TemplateTypeList[0].Id.toString();
            setActiveCollapseKey(firstKey);
            // 直接调用加载函数，因为onChange不会被程序性改变触发
            loadTemplatesForType(firstKey);
          }
        } catch (error) {

        }
      };
      fetchTemplateTypes();
    }
  }, [visible]);

  // 根据类型ID加载个人模板的函数
  const loadTemplatesForType = async (typeId: string) => {
    if (!typeId || templatesInType[typeId]) {

      return;
    }


    try {
      const res = await ListTemplateByType({
        TypeId: parseInt(typeId, 10),
        Offset: -1, // 获取所有
        Limit: 10000, // 设定一个较大的Limit
      });

      if (res && res.TemplateList) {
        const fetchedTemplates: TemplateItem[] = res.TemplateList.map((item: ApiTemplateItem) => ({
          id: item.Id.toString(),
          name: item.Name,
          type: 'personal',
          content: {
            radiographicFinding: item.RadiographicFindingTemplate,
            clinicalDiagnosis: item.ClinicalDiagnosisTemplate,
          },
        }));
        setTemplatesInType(prev => ({ ...prev, [typeId]: fetchedTemplates }));
      } else {

        setTemplatesInType(prev => ({ ...prev, [typeId]: [] })); // 设置为空数组防止无限加载
      }
    } catch (error) {

      setTemplatesInType(prev => ({ ...prev, [typeId]: [] })); // 出错时也设置为空数组
    }
  };

  // 根据类型ID加载团队模板的函数
  const loadTeamTemplatesForType = async (typeId: string) => {
    if (!typeId || teamTemplatesInType[typeId]) {

      return;
    }


    try {
      const res = await listGroupTemplateByType({
        TypeId: parseInt(typeId, 10),
        Offset: -1, // 获取所有
        Limit: 10000, // 设定一个较大的Limit
      });

      if (res && res.TemplateList) {
        const fetchedTemplates: TemplateItem[] = res.TemplateList.map((item: ApiTemplateItem) => ({
          id: item.Id.toString(),
          name: item.Name,
          type: 'team',
          content: {
            radiographicFinding: item.RadiographicFindingTemplate,
            clinicalDiagnosis: item.ClinicalDiagnosisTemplate,
          },
        }));
        setTeamTemplatesInType(prev => ({ ...prev, [typeId]: fetchedTemplates }));
      } else {

        setTeamTemplatesInType(prev => ({ ...prev, [typeId]: [] })); // 设置为空数组防止无限加载
      }
    } catch (error) {

      setTeamTemplatesInType(prev => ({ ...prev, [typeId]: [] })); // 出错时也设置为空数组
    }
  };

  // 点击折叠面板时的处理函数
  const handlePanelChange = (key: string | string[]) => {

    setActiveCollapseKey(key);
    const currentKey = Array.isArray(key) ? key[key.length - 1] : key;
    if (currentKey) {
      // 根据当前选中的分类决定调用哪个加载函数
      if (activeCategory === 'myTemplates') {
        loadTemplatesForType(currentKey);
      } else if (activeCategory === 'teamTemplates') {
        loadTeamTemplatesForType(currentKey);
      }
    }
    setSelectedTemplate(null); // 清空上次选中的模板
  };

  // 点击具体模板时的处理函数
  const handleTemplateClick = async (template: TemplateItem) => {
    // 立即选中以更新UI高亮
    setSelectedTemplate(template);

    try {


      // 根据模板类型调用不同的接口
      let res: ApiTemplateItem;
      if (template.type === 'team') {
        res = await getGroupTemplateContent({ Id: parseInt(template.id, 10) });
      } else {
        res = await getTemplateContent({ Id: parseInt(template.id, 10) });
      }

      // 直接检查返回对象是否包含模板内容字段
      if (res && res.RadiographicFindingTemplate !== undefined && res.ClinicalDiagnosisTemplate !== undefined) {
        // 使用获取到的新内容更新选中的模板
        const updatedTemplate = {
          ...template,
          content: {
            radiographicFinding: res.RadiographicFindingTemplate,
            clinicalDiagnosis: res.ClinicalDiagnosisTemplate,
          },
        };
        setSelectedTemplate(updatedTemplate);

      } else {

      }
    } catch (error) {

    }
  };



  return (
    <ConfigProvider
      theme={{
        token: {
          colorBgContainer: '#333233',
          colorBgElevated: '#333233',
          colorText: 'white',
        },
        components: {
          Modal: {
            contentBg: '#333233',
            headerBg: '#333233',
            titleColor: 'white',
          },
          Button: {
            colorText: 'white',
            colorBgContainer: '#404040',
            colorBorder: '#666',
          },
        },
      }}
    >
      <Modal
        title="选择报告模版"
        open={visible}
        onCancel={onClose}
        width={1200}
        style={{ top: 20 }}
        closeIcon={<img src="/icon/report/close.png" alt="close" style={{ width: '24px', height: '24px' }} />}
        footer={[
          <Button key="cancel" onClick={onClose}>
            跳过
          </Button>,
          <Button
            key="confirm"
            type="primary"
            style={{
              background: '#1F69B4',
              borderColor: '#1F69B4',
              color: '#fff',
              boxShadow: 'none',
            }}
            onClick={() => selectedTemplate && onConfirm(selectedTemplate)}
          >
            确定
          </Button>,
        ]}
      >
        <Row gutter={24} style={{ height: 'calc(100vh - 200px)' }}>
          {/* 左侧模板列表 */}
          <Col span={8} style={{ borderRight: '1px solid #404040', height: '100%', backgroundColor: '#262628', borderRadius: '10px' }}>
            <div style={{ height: '100%', display: 'flex', flexDirection: 'column', padding: '10px', boxSizing: 'border-box' }}>
              {/* 分类列表 */}
              <div style={{ display: 'flex', marginBottom: '16px', gap: '8px' }}>
                {categories.map(category => (
                  <div
                    key={category.key}
                    style={{
                      flex: 1,
                      textAlign: 'center',
                      padding: '12px 16px',
                      cursor: 'pointer',
                      backgroundColor: activeCategory === category.key ? '#1F69B4' : 'transparent',
                      color: 'white',
                      borderRadius: '4px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '8px',
                      transition: 'all 0.3s',
                    }}
                    onClick={() => {
                      setActiveCategory(category.key);
                      // 当切换到团队模板时，默认展开第一个团队模板分类
                      if (category.key === 'teamTemplates' && teamTemplateTypes.length > 0) {
                        const firstKey = teamTemplateTypes[0].Id.toString();
                        setActiveCollapseKey(firstKey);
                        loadTeamTemplatesForType(firstKey);
                      } else if (category.key === 'myTemplates' && myTemplateTypes.length > 0) {
                        const firstKey = myTemplateTypes[0].Id.toString();
                        setActiveCollapseKey(firstKey);
                        loadTemplatesForType(firstKey);
                      }
                      setSelectedTemplate(null); // 清空选中的模板
                    }}
                  >
                    {category.icon}
                    <span style={{
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      maxWidth: 100,
                      display: 'inline-block',
                    }} title={category.title}>
                      {category.title}
                    </span>
                  </div>
                ))}
              </div>

              {/* 模板列表 */}
              <div style={{ flex: 1, overflowY: 'auto' }} className="custom-scrollbar">
                {activeCategory === 'myTemplates' ? (
                  // "我的模板" 的UI - 折叠面板
                  <Collapse
                    activeKey={activeCollapseKey}
                    onChange={handlePanelChange}

                    accordion
                    ghost
                  >
                    {myTemplateTypes.map((type) => {
                      const isActive = (Array.isArray(activeCollapseKey)
                        ? activeCollapseKey.includes(type.Id.toString())
                        : activeCollapseKey === type.Id.toString());
                      return (
                        <Collapse.Panel
                          showArrow={false}
                          header={
                            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%', height: 28 }}>
                              <div style={{ display: 'flex', alignItems: 'center' }}>
                                <CustomIcon type="icon-fenlei" style={{ fontSize: 60 }} />
                                <span style={{
                                  lineHeight: '28px',
                                  fontSize: 14,
                                  whiteSpace: 'nowrap',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  maxWidth: 120,
                                  display: 'inline-block',
                                }} title={type.Name === 'default' ? '默认分类' : type.Name}>
                                  {type.Name === 'default' ? '默认分类' : type.Name}
                                </span>
                              </div>
                              <CustomIcon type={isActive ? 'icon-zhankai' : 'icon-zhedie'} style={{ fontSize: 60 }} />
                            </div>
                          }
                          key={type.Id.toString()}
                        >
                          <List
                            dataSource={(() => {
                              const list = templatesInType[type.Id.toString()] || [];
                              // 先分离出“默认模板”
                              let defaultTemplate: TemplateItem | undefined;
                              let otherTemplates: TemplateItem[] = [];
                              if (list.length) {
                                const defaultIndex = list.findIndex(t => t.name === '默认模板');
                                if (defaultIndex >= 0) {
                                  defaultTemplate = list[defaultIndex];
                                  otherTemplates = list.filter((_, idx) => idx !== defaultIndex);
                                } else {
                                  otherTemplates = list;
                                }
                                // 其余模板按id倒序
                                otherTemplates = otherTemplates.slice().sort((a, b) => Number(b.id) - Number(a.id));
                              }
                              return defaultTemplate ? [defaultTemplate, ...otherTemplates] : otherTemplates;
                            })()}
                            renderItem={(template: TemplateItem) => (
                              <div
                                key={template.id}
                                style={{
                                  minWidth: '120px',
                                  padding: '0 8px 0 8px',
                                  height: '32px',
                                  borderRadius: '5px',
                                  cursor: 'pointer',
                                  display: 'flex',
                                  alignItems: 'center',
                                  marginBottom: '8px',
                                  backgroundColor: selectedTemplate?.id === template.id ? '#1F69B4' : '#262628',
                                  border: 'none',
                                  color: 'white',
                                  transition: 'background 0.2s',
                                }}
                                onClick={() => handleTemplateClick(template)}
                              >
                                <CustomIcon type="icon-moban" style={{ fontSize: 60 }} />
                                <span style={{
                                  whiteSpace: 'nowrap',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  maxWidth: 120,
                                  display: 'inline-block',
                                }} title={template.name}>
                                  {template.name}
                                </span>
                              </div>
                            )}
                          />
                        </Collapse.Panel>
                      );
                    })}
                  </Collapse>
                ) : activeCategory === 'teamTemplates' ? (
                  // "团队模板" 的UI - 折叠面板
                  <Collapse
                    activeKey={activeCollapseKey}
                    onChange={handlePanelChange}

                    accordion
                    ghost
                  >
                    {teamTemplateTypes.map((type) => {
                      const isActive = (Array.isArray(activeCollapseKey)
                        ? activeCollapseKey.includes(type.Id.toString())
                        : activeCollapseKey === type.Id.toString());
                      return (
                        <Collapse.Panel
                          showArrow={false}
                          header={
                            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%', height: 28 }}>
                              <div style={{ display: 'flex', alignItems: 'center' }}>
                                <CustomIcon type="icon-fenlei" style={{ fontSize: 60 }} />
                                <span style={{
                                  lineHeight: '28px',
                                  fontSize: 14,
                                  whiteSpace: 'nowrap',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  maxWidth: 120,
                                  display: 'inline-block',
                                }} title={type.Name === 'default' ? '默认分类' : type.Name}>
                                  {type.Name === 'default' ? '默认分类' : type.Name}
                                </span>
                              </div>
                              <CustomIcon type={isActive ? 'icon-zhankai' : 'icon-zhedie'} style={{ fontSize: 60 }} />
                            </div>
                          }
                          key={type.Id.toString()}
                        >
                          <List
                            dataSource={(() => {
                              const list = teamTemplatesInType[type.Id.toString()] || [];
                              // 先分离出“默认模板”
                              let defaultTemplate: TemplateItem | undefined;
                              let otherTemplates: TemplateItem[] = [];
                              if (list.length) {
                                const defaultIndex = list.findIndex(t => t.name === '默认模板');
                                if (defaultIndex >= 0) {
                                  defaultTemplate = list[defaultIndex];
                                  otherTemplates = list.filter((_, idx) => idx !== defaultIndex);
                                } else {
                                  otherTemplates = list;
                                }
                                // 其余模板按id倒序
                                otherTemplates = otherTemplates.slice().sort((a, b) => Number(b.id) - Number(a.id));
                              }
                              return defaultTemplate ? [defaultTemplate, ...otherTemplates] : otherTemplates;
                            })()}
                            renderItem={(template: TemplateItem) => (
                              <div
                                key={template.id}
                                style={{
                                  minWidth: '120px',
                                  padding: '0 8px 0 8px',
                                  height: '32px',
                                  borderRadius: '5px',
                                  cursor: 'pointer',
                                  display: 'flex',
                                  alignItems: 'center',
                                  marginBottom: '8px',
                                  backgroundColor: selectedTemplate?.id === template.id ? '#1F69B4' : '#262628',
                                  border: 'none',
                                  color: 'white',
                                  transition: 'background 0.2s',
                                }}
                                onClick={() => handleTemplateClick(template)}
                              >
                                <CustomIcon type="icon-moban" style={{ fontSize: 60 }} />
                                <span style={{
                                  whiteSpace: 'nowrap',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  maxWidth: 120,
                                  display: 'inline-block',
                                }} title={template.name}>
                                  {template.name}
                                </span>
                              </div>
                            )}
                          />
                        </Collapse.Panel>
                      );
                    })}
                  </Collapse>
                ) : (
                  <List
                    dataSource={activeTemplates}
                    renderItem={(item: TemplateItem) => (
                      <div
                        style={{
                          padding: '8px 16px',
                          cursor: 'pointer',
                          backgroundColor:
                            selectedTemplate?.id === item.id ? '#404040' : 'transparent',
                        }}
                        onClick={() => setSelectedTemplate(item)}
                      >
                        {item.name}
                      </div>
                    )}
                  />
                )}
              </div>
            </div>
          </Col>

          {/* 右侧预览区域 */}
          <Col span={16}>
            <div style={{ height: '100%' }}>
              <Title level={5} style={{ color: 'white', marginBottom: '16px' }}>
                影像所见:
              </Title>
              <div
                style={{
                  backgroundColor: '#EDEDED',
                  padding: '16px',
                  borderRadius: '10px',
                  height: '280px',
                  marginBottom: '24px',
                  color: '#484747',
                  overflowY: 'auto'
                }}
              >
                {selectedTemplate ? (
                  selectedTemplate.content?.radiographicFinding ? (
                    <div dangerouslySetInnerHTML={{ __html: selectedTemplate.content.radiographicFinding }} />
                  ) : (
                    <span style={{ color: '#484747' }}>暂无内容</span>
                  )
                ) : (
                  <span style={{ color: '#484747' }}>请选择模板预览</span>
                )}
              </div>

              <Title level={5} style={{ color: 'white', marginBottom: '16px' }}>
                诊断结论:
              </Title>
              <div
                style={{
                  backgroundColor: '#EDEDED',
                  padding: '16px',
                  borderRadius: '4px',
                  height: '280px',
                  marginBottom: '24px',
                  color: '#484747',
                  overflowY: 'auto'
                }}
              >
                {selectedTemplate ? (
                  selectedTemplate.content?.clinicalDiagnosis ? (
                    <div dangerouslySetInnerHTML={{ __html: selectedTemplate.content.clinicalDiagnosis }} />
                  ) : (
                    <span style={{ color: '#484747' }}>暂无内容</span>
                  )
                ) : (
                  <span style={{ color: '#484747' }}>请选择模板预览</span>
                )}
              </div>
            </div>
          </Col>
        </Row>
      </Modal>
    </ConfigProvider>
  );
};

export default ChooseTemplate;
