import React, { useEffect, useRef, useState } from 'react';
import { type Dispatch } from 'umi';
import { connect } from 'dva';
import { Row, Col } from 'antd';
import style from './index.less';
import withAuth from '@/hocs/withAuth';
import { ViewType } from '@/models/views';
import {
  RenderingEngine,
  Enums,
  setVolumesForViewports,
  getRenderingEngine,
  eventTarget,
  init,
} from '@cornerstonejs/core';
import { volumeLoader } from '@cornerstonejs/core';
import type { IRenderingEngine, IStackViewport, IVolumeViewport, IViewport } from '@cornerstonejs/core/dist/types/types';
import { initCornerstone, addManipulationBindings } from '@/components/prints/cornerstone/index';

import storeUtil from '@/utils/store';
import { set } from '@kitware/vtk.js/macros';

const { ViewportType, Events } = Enums;

interface InfoProps {
  dispatch: Dispatch;
  pid?: number;
  studyLists?: any[];
  seriesLists?: any[];
  instanceLists?: any[];
  curInstances?: Map<string, any[]>;
  studyId?: any;
  seriesDescriptions?: Map<string, string>;
}

const renderingEngineId = 'viewerEngine';
const viewportId1 = 'CT_AXIAL';
const viewportId2 = 'CT_SAGITTAL';
const viewportId3 = 'CT_CORONAL';
const volumeId = 'cornerstoneStreamingImageVolume: myVolume';

const DisplayArea: React.FC<InfoProps> = ({
  dispatch,
  studyLists,
  seriesLists,
  instanceLists,
  curInstances,
  studyId,
  seriesDescriptions,
}) => {
  const [curSeriesId, setCurSeries] = useState();

  // cornerstone设置
  const axialEleRef = useRef() as React.MutableRefObject<HTMLDivElement>; // 轴位图
  const sagiEleRef = useRef() as React.MutableRefObject<HTMLDivElement>; // 矢状图
  const coronEleRef = useRef() as React.MutableRefObject<HTMLDivElement>; // 冠状图
  const wrapperRef = useRef() as React.MutableRefObject<HTMLDivElement>; // 包裹元素
  const [imgMetaData, setImgMetaData] = useState<any>({});
  const [renderingEngine, setRenderingEngine] = useState<IRenderingEngine>();

  // 测试用
  const [imageIds, setImageIds] = useState<string[]>([]);
  // const imageIds = [`wadouri:${CLOUD_API}/v1?Action=GetDicomFileInPrint&Version=20230101&InstanceId=37457`];

  //测试用检查studyId读取
  useEffect(() => {

  }, [studyId]);

  useEffect(() => {
    initCornerstone().then(() => {
      const renderingEngine: IRenderingEngine = new RenderingEngine(renderingEngineId);
      setRenderingEngine(renderingEngine);
    });

    dispatch({
      type: 'views/fetchSeries',
      payload: { StudyId: storeUtil.get('studyId').value },
    });
  }, []);

  // 模拟点击第一个Series
  useEffect(() => {
    if (seriesLists) {

      setCurSeries(seriesLists[0].Id);
      dispatch({
        type: 'views/fetchInstances',
        payload: seriesLists[0].Id,
      });
    }
  }, [seriesLists]);

  // 根据InstanceLists获取图片url
  useEffect(() => {

    if (instanceLists) {
      const imageIds = instanceLists.map((instance: any) => {
        return `wadouri:${CLOUD_API}/public/getInstanceDicom?InstanceId=${instance.Id}`;
      });
      setImageIds(imageIds);
    }
  }, [instanceLists]);

  const loadImages = async () => {
    if (!imageIds || !imageIds.length || !renderingEngine) return;


    // 监听加载完成事件
    eventTarget.addEventListener(Events.IMAGE_VOLUME_LOADING_COMPLETED, (event: any) => {

    });

    const volume = await volumeLoader.createAndCacheVolume(volumeId, {
      imageIds,
    });
    volume.load();

    const viewportInput = [
      {
        viewportId: viewportId1,
        element: axialEleRef.current,
        type: ViewportType.ORTHOGRAPHIC,
        defaultOptions: {
          orientation: Enums.OrientationAxis.AXIAL,
        },
      },
      {
        viewportId: viewportId2,
        element: sagiEleRef.current,
        type: ViewportType.ORTHOGRAPHIC,
        defaultOptions: {
          orientation: Enums.OrientationAxis.SAGITTAL,
        },
      },
      {
        viewportId: viewportId3,
        element: coronEleRef.current,
        type: ViewportType.ORTHOGRAPHIC,
        defaultOptions: {
          orientation: Enums.OrientationAxis.CORONAL,
        },
      },
    ];

    renderingEngine?.setViewports(viewportInput);

    await setVolumesForViewports(renderingEngine, [{ volumeId }], [viewportId1, viewportId2, viewportId3]);

    renderingEngine?.renderViewports([viewportId1, viewportId2, viewportId3]);
  };

  useEffect(() => {
    loadImages();
  }, [imageIds, renderingEngine]);

  return (
    <>
      <div style={{ display: 'flex', flexDirection: 'row' }} ref={wrapperRef}>
        <Row>
          <Col span={8}>
            <div
              className='cornerstone-axial-element'
              style={{ width: 600, height: 1000 }}
              ref={axialEleRef}
              onContextMenu={(e) => e.preventDefault()}
            ></div>
          </Col>
          <Col span={8}>
            <div
              className='cornerstone-sagittal-element'
              style={{ width: 600, height: 1000 }}
              ref={sagiEleRef}
              onContextMenu={(e) => e.preventDefault()}
            ></div>
          </Col>
          <Col span={8}>
            <div
              className='cornerstone-coronal-element'
              style={{ width: 600, height: 1000 }}
              ref={coronEleRef}
              onContextMenu={(e) => e.preventDefault()}
            ></div>
          </Col>
        </Row>
        {}
      </div>
    </>
  );
};

const mapStateToProps = ({ views }: { views: ViewType }) => {
  return {
    // 阅片model存储的信息
    pid: views.patientId,
    studyLists: views.studyLists,
    seriesLists: views.seriesLists,
    instanceLists: views.instanceLists,
    curInstances: views.curInstances,
    studyId: views.studyId,
    seriesDescriptions: views.seriesDescriptions,
  };
};

export default connect(mapStateToProps)(DisplayArea);
