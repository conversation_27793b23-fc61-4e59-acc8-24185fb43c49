import { request } from "umi";
import { UpdateUserInfoType, AddUserInfoType, QueryUserListParamsType, NetworkConfigType } from "@/types/user";

// 账号登录
export async function accountLogin(params: any) {
  return request(`${CLOUD_API}/login`, {
    method: 'POST',
    params: params,
  });
}

// 获取用户信息
export async function FetchUserInfo(params: { Id: string }) {
  return request(`/api/public/getUser`, {
    method: 'get',
    params,
  });
}

// 删除用户
export async function DeleteUser(params: { Id: string }) {
  return request(`/api/public/deleteUser`, {
    method: 'get',
    params,
  });
}

// 新增用户
export async function AddUser(data: AddUserInfoType) {
  return request(`/api/public/createUser`, {
    method: 'POST',
    data,
  });
}

// 更新用户信息
export async function UpdateUserInfo(data: UpdateUserInfoType) {
  return request(`/api/public/updateUser`, {
    method: 'POST',
    data,
  });
}

// 用户列表
export async function FetchUserList(params: QueryUserListParamsType) {
  return request(`/api/public/getUserListByPage`, {
    method: 'get',
    params,
  });
}

// 查询网络配置
export async function FetchNetworkConfig() {
  return request(`/networkApi/public/getNetWork`, {
    method: 'get',
  });
}

// 保存网络配置
export async function SaveNetworkConfig(data: NetworkConfigType) {
  return request(`/networkApi/public/saveNetWork`, {
    method: 'POST',
    data,
  });
}

// 检验IP端口号
export async function CheckIpPort(data: Pick<NetworkConfigType, 'id'>) {
  return request(`/networkApi/public/testDicomScp`, {
    method: 'POST',
    data,
  });
}