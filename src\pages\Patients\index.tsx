import React, { useState, useEffect, useRef } from 'react';
import { type Dispatch, history } from 'umi';
import { connect } from 'dva';
import {
  ConfigProvider,
  Flex,
  Layout,
  Space,
  Input,
  Table,
  Tag,
  Button,
  Col,
  Row,
  Popover,
  Checkbox,
  Pagination,
  InputNumber,
  message,
} from 'antd';
import type { TableProps, CheckboxProps } from 'antd';
import withAuth from '@/hocs/withAuth';
import { FilterOutlined, SwapOutlined, SearchOutlined } from '@ant-design/icons';
import { FetchCheckList, FetchSeriesList } from '@/services/patients';
import { DEFAULT_PAGE_SIZE } from '@/types/index';
import { LoginType, UserInfoType } from '@/types/user';
import {
  CheckListItemType,
  QueryCheckListParamsType,
  QuerySeriesListParamsType,
  CheckListType,
  SeriesListItemType,
  SeriesListType,
} from '@/types/patients';
import UploadModal from '@/components/UploadModal';
import CustomIcon from '@/components/CustomIcon';
import Filter from './components/Filter';
import Dicom2D from './components/Dicom';
import styles from './index.less';
import './index.less';

const { Sider, Content } = Layout;
const CheckboxGroup = Checkbox.Group;

interface Props {
  dispatch: Dispatch;
  seriesLists?: any[];
  instanceLists?: any[];
  curInstances?: Map<string, any[]>;
  studyId?: any;
  userInfo?: UserInfoType;
}

const Patients: React.FC<Props> = ({ userInfo }: Props) => {
  // 检查列表列
  const checkColumns: TableProps<CheckListItemType>['columns'] = [
    {
      className: styles.checkColumns,
      title: '患者编号',
      dataIndex: 'Patient_Id',
      ellipsis: true,
    },
    {
      title: '姓名',
      dataIndex: 'Name',
      ellipsis: true,
    },
    {
      title: '检查号',
      dataIndex: 'Study_Id',
      ellipsis: true,
    },
    {
      title: '检查部位',
      dataIndex: 'Body_Part_Examined',
      ellipsis: true,
    },
    {
      title: '性别',
      dataIndex: 'Sex',
      width: 80,
      ellipsis: true,
    },
    {
      title: '年龄',
      dataIndex: 'Age',
      width: 80,
      ellipsis: true,
    },
    {
      title: '检查时间',
      dataIndex: 'Exam_Date',
      width: 180,
      ellipsis: true,
      filterMultiple: false,
      filtered: true,
      filterIcon: () => <FilterOutlined style={{ color: '#ffffff' }} />,
      filterDropdown: () => (
        <div>
          <Filter
            type='radio'
            options={[
              {
                label: '全部',
                value: '00',
              },
              {
                label: '今天',
                value: '01',
              },
              {
                label: '三天内',
                value: '02',
              },
              {
                label: '一周内',
                value: '03',
              },
              {
                label: '一月内',
                value: '04',
              },
            ]}
            onChange={(value) => {
              setQueryCheckParams({ ...queryCheckParams, CheckDate: value.length ? value[0] : '' });
            }}
          />
        </div>
      ),
    },
    {
      title: '报告状态',
      dataIndex: 'Status_of_Report',
      width: 180,
      ellipsis: true,
      filterIcon: () => <FilterOutlined style={{ color: '#ffffff' }} />,
      filterDropdown: () => (
        <div>
          <Filter
            type='checkbox'
            options={[
              {
                label: '无报告',
                value: '0',
              },
              {
                label: '待提交',
                value: '1,3',
              },
              {
                label: '审核中',
                value: '2',
              },
              {
                label: '已审核',
                value: '5',
              },
              {
                label: '已退回',
                value: '4',
              },
            ]}
            onChange={(value) => {
              setQueryCheckParams({ ...queryCheckParams, StatusOfReport: value.length ? value.join(',') : '' });
            }}
          />
        </div>
      ),
      render: (_, record) => {
        // 定义报告状态映射对象，使状态值与描述一一对应
        const statusMap = {
          0: '无报告',
          1: '待提交',
          2: '审核中',
          3: '待提交',
          4: '已退回',
          5: '已审核',
        };
        // 定义标签颜色映射对象，根据状态值设置不同颜色
        const colorMap = {
          0: '#AAA9A9',
          1: '#FFFFFF',
          2: '#EFB844',
          3: '#FFFFFF',
          4: '#A92929',
          5: '#39A570',
        };
        const status = statusMap[record?.Status_of_Report] || '未知状态';
        const color = colorMap[record?.Status_of_Report] || 'red';
        return (
          <Tag style={{ backgroundColor: '#262628', color: color, borderColor: color, height: '25px', paddingInline: '14px' }}>
            {status}
          </Tag>
        );
      },
    },
  ];
  // 序列列表列
  const sequenceColumns: TableProps<SeriesListItemType>['columns'] = [
    {
      title: '序列号',
      width: 80,
      ellipsis: true,
      render: (_, __, index) => index + 1,
    },
    {
      title: '模态',
      dataIndex: 'Modality',
      width: 80,
      ellipsis: true,
    },
    {
      title: '序列描述',
      dataIndex: 'SeriesDescription',
      ellipsis: true,
    },
    {
      title: '设备',
      dataIndex: 'StationName',
      ellipsis: true,
    },
    {
      title: '序列时间',
      dataIndex: 'SeriesDate',
      width: 180,
      ellipsis: true,
    },
    {
      title: '图像总数',
      dataIndex: 'NumberOfSlices',
      width: 100,
      ellipsis: true,
    },
  ];
  /* 
    检查列表 - start
  */
  // 检查列表数据
  const [checkData, setCheckData] = useState<CheckListItemType[]>([]); // 检查列表数据
  const [checkTotal, setCheckTotal] = useState(0); // 检查列表数据总条数
  const [checkCurrent, setCheckCurrent] = useState(1);
  const [checkLoading, setCheckLoading] = useState(false); // 检查列表数据加载状态
  // 选中检查Study_Id
  const [studyId, setStudyId] = useState<string[]>([]); // 选中 studyId
  const [studyRow, setStudyRow] = useState<CheckListItemType>(); // 当前选中的检查行
  // 模糊查询
  const [searchItem, setSearchItem] = useState('');
  const [popover, setPopover] = useState(false);
  const [checkedList, setCheckedList] = useState<string[]>([
    'Patient_Id',
    'Study_Id',
    'Name',
    'Sex',
    'Age',
    'Body_Part_Examined',
  ]);
  const plainOptions = [
    {
      label: '患者编号',
      value: 'Patient_Id',
    },
    {
      label: '检查号',
      value: 'Study_Id',
    },
    {
      label: '姓名',
      value: 'Name',
    },
    {
      label: '性别',
      value: 'Sex',
    },
    {
      label: '年龄',
      value: 'Age',
    },
    {
      label: '检查部位',
      value: 'Body_Part_Examined',
    },
  ];
  const checkAll = plainOptions.length === checkedList.length;
  const indeterminate = checkedList.length > 0 && checkedList.length < plainOptions.length;

  const onChange = (list: string[]) => {
    setCheckedList(list);
  };

  const onCheckAll: CheckboxProps['onChange'] = (e) => {
    setCheckedList(e.target.checked ? plainOptions.map((item) => item.value) : []);
  };

  // 检查列表查询
  const [queryCheckParams, setQueryCheckParams] = useState<QueryCheckListParamsType>({
    Page: 1,
    Limit: DEFAULT_PAGE_SIZE,
    SearchItem: '', // 模糊查找,
    SearchType: checkedList.join(','), // 查找类型,
    CheckDate: '00', // 检查日期,
    StatusOfReport: '', // 报告状态,
  });
  // 检查列表查询
  const getCheckListData = (params: QueryCheckListParamsType, refresh: boolean = false) => {
    setStudyId(['']);
    setStudyRow(undefined);
    setSeriesData([]);
    setSeriesTotal(0);
    setSeriesCurrentPage(1);
    setCheckLoading(true);
    FetchCheckList(params)
      .then((res: CheckListType) => {

        setCheckData(res.PatientList || []);
        setCheckTotal(res.Length || 0);
        if (res.PatientList && res.PatientList.length) {
          setStudyId([res.PatientList[0].Study_Id || '']);
          setStudyRow(res.PatientList[0]);
          setQuerySeriesParams({ ...querySeriesParams, StudyId: res.PatientList[0].Study_Id || '' });
        }
        if (refresh) message.success('刷新成功');
      })
      .finally(() => {
        setCheckLoading(false);
      });
  };
  /* 
    检查列表 - end
  */

  /* 
    序列列表 - start
  */
  const [querySeriesParams, setQuerySeriesParams] = useState<QuerySeriesListParamsType>({
    StudyId: '',
    Page: 1,
    Limit: DEFAULT_PAGE_SIZE,
  });
  const [seriesData, setSeriesData] = useState<SeriesListItemType[]>([]);
  const [seriesTotal, setSeriesTotal] = useState(0);
  const [seriesCurrentPage, setSeriesCurrentPage] = useState(1);
  const [seriesLoading, setSeriesLoading] = useState(false);
  const [seriesId, setSeriesId] = useState<string[]>([]);
  // 序列列表查询
  const getSeriesListData = async (params: QuerySeriesListParamsType) => {
    if(seriesLoading || !studyId[0] ) return
    setSeriesLoading(true);
    let res = await FetchSeriesList(params);
    setSeriesData([...seriesData, ...(res.List || [])]);
    setSeriesTotal(res.Total);
    if (res.List && res.List.length) {
      setSeriesId([res.List[0].SeriesId]);
    } else { 
      setSeriesId([]);
    }
    setSeriesLoading(false);
  };
  const scrollRef = useRef({
    left: 0,
    top: 0,
  });
  // 下拉加载
  const handleTableScroll = async (event: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, clientHeight, scrollHeight, scrollLeft } = event.target as HTMLDivElement;

    // 垂直滚动 & 到底了
    if (Math.abs(scrollTop - scrollRef.current.top) > 0 && scrollTop + clientHeight >= scrollHeight) {
      if (seriesData.length >= seriesTotal) return;
      getSeriesListData({ ...querySeriesParams, Page: seriesCurrentPage + 1 });
      setSeriesCurrentPage(seriesCurrentPage + 1);
    }
    // 记录当前滚动信息
    scrollRef.current = {
      left: scrollLeft,
      top: scrollTop,
    };
  };
  /* 
    序列列表 - end
  */

  /*
    actions - start
  */
  const handleActionsBtn = (type: 'report' | 'check' | 'view', record: CheckListItemType | undefined) => {
    switch (type) {
      case 'report': // 报告
        if (record?.Status_of_Report == 2 || record?.Status_of_Report == 5) {
          // 2审核中、5已审核
          return (
            <Button className={styles.iconStyle} disabled type='text' icon={<CustomIcon type='icon-baogao' />}>
              报告
            </Button>
          );
        } else {
          return (
            <Button
              className={styles.iconStyle}
              onClick={() => handleReport(record)}
              type='text'
              icon={<CustomIcon type='icon-baogao' />}
            >
              报告
            </Button>
          );
        }
        break;
      case 'check': // 审核
        // 诊断医生 权限
        if (record?.Status_of_Report == 0 || record?.Status_of_Report == 1 || record?.Status_of_Report == 3) {
          // 0无报告、1待提交、3已召回
          return (
            <Button className={styles.iconStyle} disabled type='text' icon={<CustomIcon type='icon-shenhe' />}>
              审核
            </Button>
          );
        } else {
          // 2审核中,4已退回,5已审核
          return (
            <Button
              className={styles.iconStyle}
              onClick={() => handleCheck(record)}
              type='text'
              icon={<CustomIcon type='icon-shenhe' />}
            >
              审核
            </Button>
          );
        }
        break;
      case 'view': // 阅片
        handleViewer(record);
        break;
    }
  };
  // 阅片
  const handleViewer = (record: CheckListItemType | undefined) => {
    // 使用 window.open 方法在新标签页打开 /viewer 页面
    window.open(`/viewer?studyId=${record?.Study_Id}`, '_blank');
  };

  // 序列阅片
  const handleSeriesViewer = (record: SeriesListItemType) => {
    if (!studyRow?.Study_Id) return;

    // 打开新标签页，传递studyId和seriesId参数
    window.open(`/viewer?studyId=${studyRow.Study_Id}&seriesId=${record.SeriesId}`, '_blank');
  };
  // 报告
  const handleReport = (record: CheckListItemType | undefined) => {
    if (!record) return;
    window.open(`/reports?type=report&studyId=${record.Study_Id}&statusOfReport=${record.Status_of_Report}`, '_blank');
  };
  // 审核
  const handleCheck = (record: CheckListItemType | undefined) => {
    if (!record) return;
    window.open(`/reports?type=check&studyId=${record.Study_Id}&statusOfReport=${record.Status_of_Report}`, '_blank');
  };
  /*
    actions - end
  */

  /* 
    高级工具栏 - start
  */
  const ToolbarOptions = [
    {
      label: '脑分析',
      icon: '/icon/advanced/ad_fun_01.png',
      route: '',
    },
    {
      label: '心分析',
      icon: '/icon/advanced/ad_fun_02.png',
      route: '',
    },
  ];
  /*
    高级工具栏 - end
  */

  useEffect(() => {
    getCheckListData(queryCheckParams);
  }, [queryCheckParams]);
  useEffect(() => {
    getSeriesListData(querySeriesParams);
  }, [querySeriesParams]);
  useEffect(() => {
    const socket = new WebSocket(`ws://${SOCKET_API}/ws`);
    socket.onopen = () => {
    };
    socket.onmessage = (event) => {
      getCheckListData(queryCheckParams, true);
    };
    socket.onclose = () => {
    };
    socket.onerror = (error) => {
    };
    // 组件卸载时关闭 WebSocket 连接
    return () => {
      if (socket.readyState === WebSocket.OPEN) {
        socket.close();
      }
    };
  }, []);

  // 上传
  const [uploadOpen, setUploadOpen] = useState<boolean>(false);
  useEffect(() => {
      const channel = new BroadcastChannel('change_report');

      channel.onmessage = (event) => {
          getCheckListData(queryCheckParams, true);
      };

      return () => {
          channel.close(); // 清理
      };
  }, []);
  return (
    <ConfigProvider
      theme={{
        components: {
          Pagination: {
            itemBg: 'transparent',
            itemActiveBg: '#1F69B4',
            itemSize: 24,
          },
        },
      }}
    >
      <UploadModal
        open={uploadOpen}
        setOpen={(e, type) => {
          setUploadOpen(e);
          if (type == 'success') getCheckListData(queryCheckParams, true);
        }}
      />
      <Layout>
        {/* 主体 */}
        <Content style={{ marginRight: '16px' }}>
          <Flex vertical gap={16}>
            {/* 检查列表 */}
            <div className={styles.tableBox}>
              <Flex style={{ marginBottom: '14px' }} justify='space-between' align='center'>
                <Flex className={styles.tableTitle} align='center' gap={60}>
                  <div>检查列表</div>
                  <div>
                    <Input
                      className={styles.searchInput}
                      placeholder='请输入关键字搜索'
                      prefix={
                        <SearchOutlined
                          className={styles.searchIcon}
                          onClick={() =>
                            setQueryCheckParams({
                              ...queryCheckParams,
                              SearchItem: searchItem,
                              SearchType: checkedList.join(','),
                            })
                          }
                        />
                      }
                      allowClear
                      value={searchItem}
                      maxLength={99}
                      suffix={
                        <Popover
                          style={{ padding: '0' }}
                          title=''
                          trigger='click'
                          arrow={false}
                          open={popover}
                          content={
                            <div className={styles.searchBox}>
                              <div className={styles.searchBox_title}>搜索范围设置</div>
                              <div className={styles.searchBox_checkbox}>
                                <Checkbox indeterminate={indeterminate} checked={checkAll} onChange={onCheckAll}>
                                  全选
                                </Checkbox>
                                <CheckboxGroup onChange={onChange} value={checkedList}>
                                  <Flex vertical>
                                    {plainOptions.map((item) => (
                                      <Checkbox style={{ margin: '4px 0' }} key={item.value} value={item.value}>
                                        {item.label}
                                      </Checkbox>
                                    ))}
                                  </Flex>
                                </CheckboxGroup>
                                <div className={styles.searchBox_action}>
                                  <Flex justify='space-between'>
                                    <Button
                                      onClick={() => {
                                        setPopover(false);
                                        setCheckedList(queryCheckParams?.SearchType?.split(',') || []);
                                      }}
                                    >
                                      取消
                                    </Button>
                                    <Button
                                      type='primary'
                                      onClick={() => {
                                        if (!checkedList.length) return message.error('至少勾选一个字段');
                                        message.success('设置成功');
                                        setQueryCheckParams({
                                          ...queryCheckParams,
                                          SearchItem: searchItem,
                                          SearchType: checkedList.join(','),
                                        });
                                        setPopover(false);
                                      }}
                                    >
                                      确定
                                    </Button>
                                  </Flex>
                                </div>
                              </div>
                            </div>
                          }
                          onOpenChange={(open) => {
                            setPopover(open);
                            if (!open) setCheckedList(queryCheckParams?.SearchType?.split(',') || []);
                          }}
                        >
                          <SwapOutlined className={`${styles.searchIcon} ${styles.cursor}`} onClick={() => setPopover(true)} />
                        </Popover>
                      }
                      onChange={(e) => setSearchItem(e.target.value)}
                      onClear={() => {
                        setSearchItem('');
                        setQueryCheckParams({ ...queryCheckParams, SearchItem: '', SearchType: checkedList.join(',') });
                      }}
                      onPressEnter={(e) => {
                        if (!searchItem) return message.error('请输入关键字搜索');
                        if (!checkedList.length) return message.error('至少勾选一个字段');
                        setQueryCheckParams({ ...queryCheckParams, SearchItem: searchItem, SearchType: checkedList.join(',') });
                        setPopover(false);
                      }}
                    />
                  </div>
                </Flex>
                <Space>
                  <Button
                    type='text'
                    icon={<CustomIcon type='icon-shuaxin-01' />}
                    style={{ color: '#ffffff', fontSize: '16px' }}
                    onClick={() => getCheckListData(queryCheckParams, true)}
                  >
                    刷新
                  </Button>
                  <Button
                    type='text'
                    icon={<CustomIcon type='icon-shangchuan-01' />}
                    style={{ color: '#ffffff', fontSize: '16px' }}
                    onClick={() => setUploadOpen(true)}
                  >
                    上传
                  </Button>
                </Space>
              </Flex>
              <Table<CheckListItemType>
                style={{ height: 'calc(50vh - 180px)' }}
                scroll={{ y: 'calc(50vh - 180px - 35px)' }}
                loading={checkLoading}
                columns={checkColumns}
                dataSource={checkData}
                pagination={false}
                rowKey='Study_Id'
                rowSelection={{
                  type: 'radio',
                  hideSelectAll: true,
                  selectedRowKeys: studyId,
                  renderCell: (value, record, index) => {
                    return <div></div>;
                  },
                }}
                onRow={(record) => {
                  return {
                    onClick: () => {
                      setStudyId([record?.Study_Id || '']);
                      setStudyRow(record);
                      setSeriesData([]);
                      setQuerySeriesParams({ ...querySeriesParams, StudyId: record?.Study_Id || '' });
                    },
                    onDoubleClick: () => {
                      switch (record.Status_of_Report) {
                        case 0: //无报告
                        case 1: //待提交
                        case 3: //已审核
                          window.open(
                            `/reports?type=report&studyId=${record.Study_Id}&statusOfReport=${record.Status_of_Report}`,
                            '_blank'
                          );
                          break;
                        case 2: //审核中
                          if (userInfo?.Role == 'check' || userInfo?.Role == 'admin') {
                            window.open(
                              `/reports?type=check&studyId=${record.Study_Id}&statusOfReport=${record.Status_of_Report}`,
                              '_blank'
                            );
                          }
                          break;
                        case 4: //已退回
                          window.open(
                            `/reports?type=report&studyId=${record.Study_Id}&statusOfReport=${record.Status_of_Report}`,
                            '_blank'
                          );
                          break;
                        default:
                          break;
                      }
                    },
                  };
                }}
              />
              <Flex style={{ margin: '16px 20px 0' }} justify='space-between' align='center'>
                <Flex align='center'>
                  <Pagination
                    current={checkCurrent}
                    total={checkTotal}
                    pageSize={DEFAULT_PAGE_SIZE}
                    showTotal={(total) => <div>共 {total} 条</div>}
                    itemRender={(page, type: 'page' | 'prev' | 'next' | 'jump-prev' | 'jump-next', originalElement) => {
                      if (type === 'page') {
                        return <div>{page}</div>;
                      }
                      return originalElement;
                    }}
                    onChange={(page) => {
                      setCheckCurrent(page);
                      setQueryCheckParams({
                        ...queryCheckParams,
                        Page: page,
                      });
                    }}
                  />
                  <Space style={{ marginLeft: '16px' }} size={12}>
                    前往
                    <InputNumber
                      style={{ width: 50, backgroundColor: '#262628' }}
                      min={1}
                      max={Math.ceil(checkTotal / DEFAULT_PAGE_SIZE)}
                      precision={0}
                      changeOnBlur
                      controls={false}
                      // 限制只能输入数字
                      onKeyPress={(e) => !/^\d$/.test(e.key) && e.preventDefault()}
                      onPressEnter={(e) => {
                        if (
                          (e?.target as HTMLInputElement)?.value &&
                          Number((e?.target as HTMLInputElement).value) > Math.ceil(checkTotal / DEFAULT_PAGE_SIZE)
                        )
                          return;
                        setCheckCurrent(
                          (e?.target as HTMLInputElement)?.value ? Number((e?.target as HTMLInputElement).value) : 1
                        );
                        setQueryCheckParams({
                          ...queryCheckParams,
                          Page: (e?.target as HTMLInputElement)?.value ? Number((e?.target as HTMLInputElement).value) : 1,
                        });
                      }}
                    />
                    页
                  </Space>
                </Flex>
                {/* 操作 */}
                <div>
                  <Space>
                    {studyRow?.Study_Id ? (
                      <a href={`/viewer?studyId=${studyRow.Study_Id}`} target='_blank'>
                        <Button className={styles.iconStyle} type='text' icon={<CustomIcon type='icon-yuepian' />}>
                          阅片
                        </Button>
                      </a>
                    ) : (
                      <Button className={styles.iconStyle} disabled type='text' icon={<CustomIcon type='icon-yuepian' />}>
                        阅片
                      </Button>
                    )}
                    {handleActionsBtn('report', studyRow)}
                    {handleActionsBtn('check', studyRow)}
                    {/* <Button type='text' icon={<CustomIcon type="icon-jiaopian" />} className={styles.iconStyle}>
                        胶片
                      </Button>
                      <Button type='text' icon={<CustomIcon type="icon-dayin" />} className={styles.iconStyle}>
                        打印
                      </Button>
                      <Button type='text' icon={<CustomIcon type="icon-daochu" />} className={styles.iconStyle}>
                        导出
                      </Button> */}
                  </Space>
                </div>
              </Flex>
            </div>
            {/* 序列列表 */}
            <Row gutter={16} className={styles.rowHeight}>
              <Col span={16}>
                <div className={styles.tableBox}>
                  <Flex style={{ marginBottom: '14px' }} justify='space-between' align='center'>
                    <Flex className={styles.tableTitle} align='center' gap={60}>
                      序列列表
                    </Flex>
                  </Flex>
                  <Table<SeriesListItemType>
                    style={{ height: 'calc(50vh - 120px)' }}
                    scroll={{ y: 'calc(50vh - 120px - 35px)' }}
                    loading={seriesLoading}
                    columns={sequenceColumns}
                    dataSource={seriesData}
                    pagination={false}
                    onScroll={handleTableScroll}
                    rowKey='SeriesId'
                    rowSelection={{
                      type: 'radio',
                      hideSelectAll: true,
                      selectedRowKeys: seriesId,
                      renderCell: (value, record, index) => {
                        return <div></div>;
                      },
                    }}
                    onRow={(record) => {
                      return {
                        onClick: () => {
                          setSeriesId([record?.SeriesId || '']);
                        },
                        onDoubleClick: () => {
                          if (record && studyRow?.Study_Id && record?.SeriesDescription != 'injector') {
                            handleSeriesViewer(record);
                          }
                        },
                      };
                    }}
                  />
                </div>
              </Col>
              <Col span={8}>
                {seriesId[0] ? (
                  <Dicom2D
                    seriesId={seriesId[0]}
                    index={seriesData.findIndex((item) => item.SeriesId === seriesId[0]) + 1}
                    time={seriesData.find((item) => item.SeriesId === seriesId[0])?.SeriesDate || ''}
                    onDoubleClick={() => {
                      const selectedSeries = seriesData.find((item) => item.SeriesId === seriesId[0]);
                      if (selectedSeries && studyRow?.Study_Id && selectedSeries?.SeriesDescription != 'injector') {
                        handleSeriesViewer(selectedSeries);
                      }
                    }}
                  />
                ) : (
                  <div className={styles.dicom_area}>
                    <span>暂无缩略图</span>
                  </div>
                )}
              </Col>
            </Row>
          </Flex>
        </Content>
        <Sider width='300px' style={{ borderRadius: '10px', overflow: 'hidden', background: '#191919' }}>
          <div className={styles.card}>
            {/* <div className={styles.cardTit}>高级功能</div> */}
            <div className={styles.cardBody}>
              {ToolbarOptions.map((item, index) => {
                return (
                  <div
                    className={styles.item}
                    key={index}
                    onClick={() => {
                      if (!item.route) return message.warning('功能开发中敬请期待！');
                      history.push(item.route);
                    }}
                  >
                    <div className={styles.imgBox}>
                      <img src={item.icon} alt='' />
                    </div>
                    <div>{item.label}</div>
                  </div>
                );
              })}
            </div>
          </div>
        </Sider>
      </Layout>
    </ConfigProvider>
  );
};

const mapStateToProps = ({ user }: { user: LoginType }) => {
  return {
    userInfo: user.userInfo,
  };
};

export default withAuth(connect(mapStateToProps)(Patients));
