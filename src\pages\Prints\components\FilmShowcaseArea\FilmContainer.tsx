import React, { useState, useEffect, useMemo, useRef, use<PERSON><PERSON>back, WheelEventHandler, WheelEvent } from 'react';
import { Row, Col } from 'antd';
import Dicom2DRender from './Dicom2DRender';
import type { PrintType } from '@/models/prints';
import { RenderingEngine, Enums, metaData } from '@cornerstonejs/core';
import styles from './index.less';
// @ts-ignore
import styled from 'styled-components';
// @ts-ignore
import { v4 as uuidv4 } from 'uuid';
import type { IRenderingEngine, IViewport } from '@cornerstonejs/core/dist/types/types';
import { layoutNumToArr } from '@/utils/utils';

interface ILayoutContainerProps {
  layout: [number, number]; // 行数和列数
  children?: React.ReactNode | React.ReactNode[];
  onCellSelect?: (index: string | null) => void;
  selectcolor?: string;
  keyPrefix?: string;
  target?: string | null;
  className?: any;
  onWheel?: WheelEventHandler<HTMLDivElement>;
}
interface IDicomLayoutContainerProps extends ILayoutContainerProps {
  onWheelHandler?: (seriesId: string, renderIndex: number, nextIndex: number) => void;
}

interface IFilmContainerProps {
  filmId: number;
  filmConfig: any;
  selectValue: PrintType['selectedDicomId'];
  onChange: (selectValue: string | null) => void;
  loadDicom?: (seriesId: number) => any[];
  curInstances: PrintType['curInstances'];
}

// 定义可选中的布局单元格样式
const StyledSelectableCell = styled(Col)`
  border: ${({ iselected, selectcolor }: { iselected: boolean; selectcolor: string }) =>
    `2px solid ${iselected ? selectcolor : 'none'}`};
  padding: 2px;
  box-sizing: border-box;
  cursor: pointer;
`;

const LayoutContainer: React.FC<ILayoutContainerProps> = ({
  layout,
  children,
  onCellSelect,
  selectcolor,
  keyPrefix,
  target,
  ...rest
}) => {
  // 将children与空白单元格合并
  const mergedChildren = (): React.ReactNode[] => {
    if (!Array.isArray(children)) {
      return React.Children.toArray(children);
    } else {
      const showNum = layout[0] * layout[1];
      if (children?.length > showNum) {
        return React.Children.toArray(children).slice(0, showNum);
      } else {
        return React.Children.toArray(children).concat(new Array(showNum - React.Children.count(children)).fill(null));
      }
    }
  };

  const handleCellClick = (index: number) => {
    onCellSelect && onCellSelect(`${keyPrefix}-${index}`);
  };

  // 生成网格布局
  const createGrid = (childrenNode: React.ReactNode[]): JSX.Element[] => {
    const grid: JSX.Element[] = [];
    let currentRow: JSX.Element[] = [];

    childrenNode.forEach((child, index) => {
      const isEmptyCell = child === null;

      currentRow.push(
        <StyledSelectableCell
          key={`${keyPrefix}-${index}`}
          span={Math.floor(24 / layout[1])}
          iselected={target === `${keyPrefix}-${index}`}
          selectcolor={selectcolor}
          onClick={(e: any) => {
            e.preventDefault();
            e.stopPropagation();
            onCellSelect && handleCellClick(index);
          }}
        >
          {isEmptyCell ? <div>空白</div> : child}
        </StyledSelectableCell>
      );

      if (
        // 当达到当前行最后一个单元格时，将当前行添加到网格，并重置当前行
        (index + 1) % layout[1] === 0 ||
        // 如果这是最后一个子元素并且当前行还有剩余空间，也将当前行添加到网格
        (index === childrenNode.length - 1 && currentRow.length > 0)
      ) {
        grid.push(<Row style={{ height: `${100 / layout[0]}%` }}>{currentRow}</Row>);
        currentRow = [];
      }
    });

    return grid;
  };

  return <div {...rest}>{createGrid(mergedChildren())}</div>;
};

const SeriesLayoutContainer: React.FC<ILayoutContainerProps> = LayoutContainer;
const DicomsLayoutContainer: React.FC<ILayoutContainerProps> = LayoutContainer;

// 每一个FilmContainer都对应一个Film胶片单位
const FilmContainer: React.FC<IFilmContainerProps> = React.memo((props) => {
  const { filmId, filmConfig, selectValue, onChange, curInstances } = props;
  // console.log('FilmContainer props', props);
  const { seriesLayout = 11, seriesList = [] } = filmConfig ?? {
    seriesLayout: 11,
    seriesList: [],
  };
  const [seriesLayoutArr, setSeriesLayoutArr] = useState<[number, number]>([1, 1]);
  // 对于每一个series创建一个独有的renderingEngineMapRef
  const renderingEngineMapRefs = useRef<Map<string, { uuid: string; renderingEngine: IRenderingEngine }>>(new Map());

  useEffect(() => {
    setSeriesLayoutArr(layoutNumToArr(seriesLayout));
  }, [seriesLayout]);

  const createRenderingEngine = (seriesId: string) => {
    const uuid = `renderingEngine${seriesId}_${uuidv4()}`;
    // 创建渲染引擎实例，并保存到映射中
    const renderingEngine = new RenderingEngine(uuid);
    renderingEngineMapRefs?.current.set(seriesId, {
      uuid,
      renderingEngine,
    });
  };
  const getRenderingEngine = (seriesId: string) => {
    // 从映射中获取去渲染引擎实例
    const engineData = renderingEngineMapRefs.current.get(seriesId);
    if (engineData) {
      return engineData.renderingEngine;
    }
  };
  const handleWheelOnSeries = (seriesId: string, renderIndex: number, nextIndex: number, space: number) => {
    const renderingEngine = renderingEngineMapRefs.current.get(seriesId)?.renderingEngine;
    if (renderingEngine) {
      // 获取对应的renderingEngine
      const viewportList: IViewport[] = renderingEngine?.getViewports();

      viewportList.forEach((viewport, index) => {
        viewport?.setImageIdIndex(nextIndex + (index - renderIndex) * space);
      });
    }
  };

  const adaptiveSeriesList = useMemo(() => {
    // 这个属性的作用是，当添加新的Series区域的时候暂时用空白区域填充
    const seriesShowNum = seriesLayoutArr[0] * seriesLayoutArr[1];
    if (seriesList.length > seriesShowNum) {
      return seriesList.splice(0, seriesShowNum);
    } else {
      return seriesList.concat(new Array(seriesShowNum - seriesList.length).fill(null));
    }
  }, [seriesLayoutArr, seriesList]);

  const renderSeriesContent = (series: any, seriesIndex: number) => {
    const { dicomsLayoutList, isInverseList, printSpaceList } = filmConfig;
    const gridLayout = layoutNumToArr(dicomsLayoutList?.[seriesIndex] || 11);
    // 一个Series只需要一个RenderEngine
    const hasEngineToSeries = renderingEngineMapRefs.current.has(series?.Id);
    !hasEngineToSeries && createRenderingEngine(series?.Id);

    return (
      <DicomsLayoutContainer
        key={seriesIndex}
        layout={gridLayout}
        onCellSelect={onChange}
        selectcolor={'red'}
        className={styles.imgsContainer}
        keyPrefix={`${filmId}-${seriesIndex}`}
        target={selectValue}
      >
        {Array.from({ length: gridLayout[0] * gridLayout[1] }, (_, index) => {
          return (
            <Dicom2DRender
              key={index}
              renderIndex={index}
              row={gridLayout[0]}
              col={gridLayout[1]}
              series={series}
              isInverse={isInverseList?.[seriesIndex]}
              space={printSpaceList?.[seriesIndex]}
              getRenderingEngine={getRenderingEngine}
              curInstances={curInstances}
              onWheelHandler={handleWheelOnSeries}
            />
          );
        })}
      </DicomsLayoutContainer>
    );
  };

  return (
    <>
      {seriesList.length ? (
        <SeriesLayoutContainer layout={seriesLayoutArr} className={styles.seriesContainer}>
          {adaptiveSeriesList.map(renderSeriesContent)}
        </SeriesLayoutContainer>
      ) : (
        <span>请选择序列</span>
      )}
    </>
  );
});

export default FilmContainer;
