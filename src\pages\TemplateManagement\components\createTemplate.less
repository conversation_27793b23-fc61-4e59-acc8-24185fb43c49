.quill {
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
    
    //样式穿透
    :global {
      .ql-container {
        border: none !important;
        border-radius: 0;
        flex: 1;
        display: flex;
        flex-direction: column;
      }
      
      .ql-editor {
        background-color: white;
        color: black;
        border: none !important;
        border-radius: 0;
        flex: 1;
        min-height: 0;
        // scrollbar - 使用全局样式
        overflow-y: auto !important;
        // &::-webkit-scrollbar {
        //   height: 0px;
        //   width: 10px;
        //   overflow-y: auto;
        // }
        // &::-webkit-scrollbar-thumb {
        //   border-radius: 5px;
        //   background: #595959;
        // }
        // &::-webkit-scrollbar-track {
        //   -webkit-box-shadow: 0;
        //   border-radius: 5px;
        //   background: #7f7f7f;
        // }
      }
      
      .ql-toolbar {
        background-color: #EDEDED !important;
        color: black;
        border: none !important;
        border-bottom: 1px solid #d9d9d9 !important;
        border-radius: 0;
        flex-shrink: 0;
        height: 33px;
        display: flex;
        align-items: center;
      }
      
      // 确保整个编辑器的背景和边框一致
      .ql-snow {
        border: none !important;
      }
  
      // 自定义按钮样式 - 使用自定义图标
      .ql-undo:before {
        content: "";
        background-image: url('/icon/report/recover.png');
        background-size: 16px 16px;
        background-repeat: no-repeat;
        background-position: center;
        display: inline-block;
        width: 16px;
        height: 16px;
      }
      
      // 隐藏默认的SVG图标
      .ql-undo svg,
      .ql-redo svg,
      .ql-clean svg,
      .ql-format-painter svg,
      .ql-image svg,
      .ql-symbols svg {
        display: none !important;
      }
      
      .ql-redo:before {
        content: "";
        background-image: url('/icon/report/recover.png');
        background-size: 16px 16px;
        background-repeat: no-repeat;
        background-position: center;
        display: inline-block;
        width: 16px;
        height: 16px;
        transform: scaleX(-1); // 水平翻转来表示重做
      }
      
      .ql-clean:before {
        content: "";
        background-image: url('/icon/report/quash.png');
        background-size: 16px 16px;
        background-repeat: no-repeat;
        background-position: center;
        display: inline-block;
        width: 16px;
        height: 16px;
      }
      
      .ql-format-painter:before {
        content: "";
        background-image: url('/icon/report/format_painter.png');
        background-size: 16px 16px;
        background-repeat: no-repeat;
        background-position: center;
        display: inline-block;
        width: 16px;
        height: 16px;
      }
      
      .ql-header:before {
        content: "T";
        font-size: 16px;
        font-weight: bold;
        color: inherit;
        display: inline-block;
        width: 16px;
        height: 16px;
        text-align: center;
        line-height: 16px;
      }
      
      .ql-header-toggle:before {
        content: "T";
        font-size: 16px;
        font-weight: bold;
        color: inherit;
        display: inline-block;
        width: 16px;
        height: 16px;
        text-align: center;
        line-height: 16px;
      }
      
      .ql-size-toggle:before {
        content: "";
        background-image: url('/icon/report/font_size.png');
        background-size: 16px 16px;
        background-repeat: no-repeat;
        background-position: center;
        display: inline-block;
        width: 16px;
        height: 16px;
      }
      
      .ql-image:before {
        content: "";
        background-image: url('/icon/report/image.png');
        background-size: 16px 16px;
        background-repeat: no-repeat;
        background-position: center;
        display: inline-block;
        width: 16px;
        height: 16px;
      }
      
      .ql-symbols:before {
        content: "";
        background-image: url('/icon/report/symbol.png');
        background-size: 16px 16px;
        background-repeat: no-repeat;
        background-position: center;
        display: inline-block;
        width: 16px;
        height: 16px;
      }
  
      // 撤销和恢复按钮移除视觉变化效果
      .ql-toolbar button.ql-undo:hover,
      .ql-toolbar button.ql-redo:hover {
        color: inherit !important;
        background-color: transparent !important;
      }
      
      .ql-toolbar button.ql-undo.ql-active,
      .ql-toolbar button.ql-redo.ql-active {
        color: inherit !important;
        background-color: transparent !important;
      }
      
      .ql-toolbar button.ql-undo:focus,
      .ql-toolbar button.ql-redo:focus {
        outline: none !important;
        color: inherit !important;
        background-color: transparent !important;
      }
  
      // 工具栏按钮通用样式
      .ql-toolbar .ql-formats {
        margin-right: 0px;
      }
      
      .ql-toolbar button {
        padding: 2px;
        margin: 2px 6px;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
      }
      
      // 第一个按钮左边距为0
      .ql-toolbar .ql-formats:first-child button:first-child {
        margin-left: 0;
      }
      
      // 最后一个按钮右边距为0
      .ql-toolbar .ql-formats:last-child button:last-child {
        margin-right: 0;
      }
      
      .ql-toolbar button:hover {
        color: #06c;
      }
      
      .ql-toolbar button.ql-active {
        color: #06c;
        background-color: #e6f7ff;
      }
  
      // 确保所有自定义图标按钮有正确的尺寸
      .ql-toolbar button {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
      }
  
      // 为自定义图标按钮设置统一的hover效果
      .ql-toolbar button:hover::before {
        opacity: 0.7;
      }
  
      .ql-toolbar button.ql-active::before {
        opacity: 1;
        filter: brightness(1.2);
      }
  
      // 确保自定义图标按钮覆盖默认样式
      .ql-toolbar button.ql-undo,
      .ql-toolbar button.ql-redo,
      .ql-toolbar button.ql-clean,
      .ql-toolbar button.ql-format-painter,
      .ql-toolbar button.ql-image,
      .ql-toolbar button.ql-symbols,
      .ql-toolbar button.ql-header-toggle,
      .ql-toolbar button.ql-size-toggle {
        background: none !important;
        border: none !important;
        overflow: hidden;
      }
  
      // 确保before伪元素正确定位
      .ql-toolbar button.ql-undo:before,
      .ql-toolbar button.ql-redo:before,
      .ql-toolbar button.ql-clean:before,
      .ql-toolbar button.ql-format-painter:before,
      .ql-toolbar button.ql-image:before,
      .ql-toolbar button.ql-symbols:before,
      .ql-toolbar button.ql-header-toggle:before,
      .ql-toolbar button.ql-size-toggle:before {
        position: relative;
        z-index: 1;
        width: 14px;
        height: 14px;
        background-size: 14px 14px !important;
      }
    }
  }