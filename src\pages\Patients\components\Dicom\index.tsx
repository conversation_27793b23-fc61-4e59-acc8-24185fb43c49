import React, { useEffect, useState, useRef, useMemo, use<PERSON><PERSON>back, WheelEventHandler } from 'react';
import { connect } from 'umi';
import { message } from 'antd';
import dayjs from 'dayjs';
import {
  RenderingEngine,
  Enums,
  setVolumesForViewports,
  volumeLoader,
  getRenderingEngine,
} from '@cornerstonejs/core';
import { initCornerstone } from '@/components/prints/cornerstone/index';
import { IRenderingEngine, IStackViewport, IVolumeViewport } from '@cornerstonejs/core/dist/types/types';
import { Enums as csToolsEnums, ToolGroupManager, addTool, removeTool, StackScrollMouseWheelTool } from '@cornerstonejs/tools';
import type { PrintType } from '@/models/prints';
import { IMouseLeftToolType } from '@/utils/enums';
import { QueryDicomListParamsType, DicomListType, DicomListItemType } from "@/types/patients";
import { FetchImg } from '@/services/patients';
import styles from './index.less';

interface IDicomRenderProps {
  index: number;
  seriesId: string | number;
  time: string,
  onDoubleClick: () => void;
}

const { ViewportType } = Enums;
const { MouseBindings } = csToolsEnums;

const Dicom2D: React.FC<IDicomRenderProps> = React.memo((props) => {
  const { seriesId, index, time, onDoubleClick } = props;
  const renderingEngineId = 'Patient_RenderingEngineId'
  const viewportId = 'Patient_CT_AXIAL_STACK';
  const toolGroupId = 'Patient_StackViewportToolGroup';

  const rendererRef = useRef<HTMLDivElement>(null); // 渲染容器
  const [renderingEngine, setRenderingEngine] = useState<IRenderingEngine>(); // 渲染引擎实例
  const [instanceList, setInstanceList] = useState<DicomListItemType[]>([]);  // 实例列表
  const [currentFrameIndex, setCurrentFrameIndex] = useState<number>(0); // 添加状态来保存当前帧的索引
//获取imageId
  const imageIds = useMemo(() => {
    if (!seriesId) return [];
    const originImageIds = instanceList.map((instance: DicomListItemType) => {
      return `wadouri:${CLOUD_API}/public/getInstanceDicom?InstanceId=${instance.Id}`;
    });
    return originImageIds;
  }, [instanceList]);
  const loadImages = async () => {
    if (!imageIds || !imageIds.length) return;
    if (rendererRef.current) {
      const viewportInput = {
        viewportId: viewportId,
        element: rendererRef.current,
        type: ViewportType.STACK, // 修改视口类型为 STACK
      };
      // 启用视口元素
      renderingEngine?.enableElement(viewportInput);
      
      // 获取 STACK 视口
      const viewport = renderingEngine ? (renderingEngine.getViewport(viewportId) as IStackViewport) : null;
      
      if (renderingEngine && viewport) {
        try {
          viewport.setStack(imageIds);
          console.log('imageIds', imageIds);
          // 设置默认中间帧
          const middleIndex = Math.floor(imageIds.length / 2);
          setCurrentFrameIndex(middleIndex);
          viewport.setImageIdIndex(middleIndex);
          viewport.render();
          // message.success('渲染成功');
        } catch (error) {
          message.error('视口渲染失败请重试');
          console.error('Error setting the stack:', error);
        }
      }
    }
  };

  // 获取dicom列表
  const getDicomList = async (queryParams: QueryDicomListParamsType) => { 
    const res:DicomListType = await FetchImg(queryParams)
    setInstanceList(res?.List || [])
  }
  // 初始化
  useEffect(() => {
    if (!renderingEngine) {
      initCornerstone().then(() => {
        // 创建渲染引擎实例，并保存到映射中
        const renderingEngineAxial = new RenderingEngine(renderingEngineId);
        setRenderingEngine(renderingEngineAxial);
        addTool(StackScrollMouseWheelTool);
        setUpToolGroups();
      });
    }
    return () => {
      renderingEngine?.destroy();
      // 移除工具 StackScrollMouseWheelTool
      removeTool(StackScrollMouseWheelTool);
    };
  }, []);
  // 获取dicom列表
  useEffect(() => {
   getDicomList({ SeriesId: seriesId })
  }, [seriesId]);
  // 加载dicom 设置工具组
  useEffect(() => {
    loadImages()
  }, [imageIds]);

  const setUpToolGroups = async () => {
    let toolGroup = ToolGroupManager.getToolGroup(toolGroupId);
    if (!toolGroup) {
       toolGroup = ToolGroupManager.createToolGroup(toolGroupId);
       // 添加 StackScrollMouseWheelTool 到工具组
      if (toolGroup) {
        toolGroup.addTool(StackScrollMouseWheelTool.toolName, {
          configuration: {
            scrollSpeed: 1,
            scrollToCenter: true,
            smoothScroll: true,
            loop: true,
          }
        });

        // 设置工具的交互模式为鼠标中键
        toolGroup.setToolActive(StackScrollMouseWheelTool.toolName, {
          bindings: [
            {
              mouseButton: MouseBindings.Auxiliary,
            },
          ],
        });
      }
    }
    toolGroup?.addViewport(viewportId, renderingEngineId);
  };

  const handleWheel: WheelEventHandler<HTMLDivElement> = (event) => {
      const viewport = renderingEngine?.getViewport(viewportId) as IStackViewport;
      if (!viewport) return;
      const delta = event.deltaY;
      setCurrentFrameIndex(prev => {
        const nextIndex = delta < 0
          ? Math.min(prev + 1, imageIds.length - 1)
          : Math.max(prev - 1, 0);

        viewport.setImageIdIndex(nextIndex);
        viewport.render();
        return nextIndex;
      });
    }




  return seriesId ? (
    <div 
      className={styles.dicom_area}
      onDoubleClick={()=> {
        onDoubleClick()
      }} >
      <div className={styles.dirom} ref={rendererRef} onWheel={handleWheel} onContextMenu={(e) => e.preventDefault()} >
      </div>
      <div className={styles.front}>[A]</div>
      <div className={styles.right}>[R]</div>
      <div className={styles.left}>[L]</div>
      <div className={styles.queen}>[P]</div>
      <div className={styles.index}>{index || ''}</div>
      <div className={styles.time}>{ time ? dayjs(time).format('YYYY-MM-DD HH:mm') : ''}</div>
      <div className={styles.frame}>{  `${currentFrameIndex + 1 || ''}/${imageIds?.length || ''}` }</div>
    </div>
  ) : (
    <div className={styles.dicom_area}>
      <span>暂无缩略图</span>
    </div>
  );
});

const mapStateToProps = ({ prints }: { prints: PrintType }) => {
  return { };
};

export default connect(mapStateToProps)(Dicom2D);