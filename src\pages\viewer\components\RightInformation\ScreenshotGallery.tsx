import React, { useState, useEffect, useImperativeHandle, forwardRef, useMemo } from 'react';
import { Image, Checkbox, Button, Modal } from 'antd';
import { LeftOutlined, RightOutlined, CloseOutlined } from '@ant-design/icons';
import { connect } from 'umi';
import { ViewType } from '@/models/views';

interface ScreenshotGalleryProps {
  imgs?: string[];
  onInsertImages?: (imageUrls: string[]) => void;
  onDeleteImages?: (indices: number[]) => void;
  onSelectionChange?: (selectedCount: number) => void;
  screenshotInfos?: ViewType['screenshotInfos'];
}

// 暴露给父组件的方法
export interface ScreenshotGalleryRef {
  getSelectedImages: () => number[];
  clearSelection: () => void;
  previewImage: (index: number) => void;
}

const ScreenshotGallery = forwardRef<ScreenshotGalleryRef, ScreenshotGalleryProps>(
  ({ imgs = [], onInsertImages, onDeleteImages, onSelectionChange, screenshotInfos = [] }, ref) => {
    // 管理选中的图片索引
    const [selectedImages, setSelectedImages] = useState<number[]>([]);
    // 管理预览状态
    const [previewVisible, setPreviewVisible] = useState(false);
    const [previewImages, setPreviewImages] = useState<string[]>([]);
    const [previewCurrent, setPreviewCurrent] = useState(0);

    // 为每张图片生成并保存时间戳
    const [imageTimestamps, setImageTimestamps] = useState<Record<string, string>>({});

    // 自定义预览状态
    const [customPreviewVisible, setCustomPreviewVisible] = useState(false);
    const [currentPreviewIndex, setCurrentPreviewIndex] = useState(0);

    // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    // 获取当前选中的图片索引
    getSelectedImages: () => selectedImages,
    // 清除选择
    clearSelection: () => setSelectedImages([]),
    // 预览指定索引的图片
    previewImage: (index: number) => handlePreviewImage(index),
  }));

    // 监听imgs变化
    useEffect(() => {
      // console.log('ScreenshotGallery: imgs变化，数量:', imgs.length);
      // console.log('ScreenshotGallery: imgs内容:', imgs);

      // 预加载图片，确保能正确显示
      imgs.forEach((url) => {
        const img = document.createElement('img');
        img.src = url;
      });

      // 当图片列表变化时，清除选择状态
      setSelectedImages([]);
    }, [imgs]);

    // 当图片列表变化时，为新图片生成时间戳
    useEffect(() => {
      const newTimestamps = { ...imageTimestamps };
      let hasNewImages = false;

      imgs.forEach((url) => {
        if (!newTimestamps[url]) {
          newTimestamps[url] = new Date().toLocaleString();
          hasNewImages = true;
        }
      });

      if (hasNewImages) {
        setImageTimestamps(newTimestamps);
      }
    }, [imgs]);

    // 监听选中状态变化，通知父组件
    useEffect(() => {
      onSelectionChange?.(selectedImages.length);
    }, [selectedImages, onSelectionChange]);

    // 处理图片选择
    const handleImageSelect = (index: number, checked: boolean) => {
      if (checked) {
        setSelectedImages((prev) => [...prev, index]);
      } else {
        setSelectedImages((prev) => prev.filter((i) => i !== index));
      }
    };

    // 全选/取消全选
    const handleSelectAll = () => {
      if (selectedImages.length === imgs.length) {
        setSelectedImages([]);
      } else {
        setSelectedImages(imgs.map((_, index) => index));
      }
    };

    // 获取选中的图片URL
    const getSelectedImageUrls = () => {
      return selectedImages.map((index) => imgs[index]);
    };

    // 插入选中的图片
    const handleInsert = () => {
      if (onInsertImages && selectedImages.length > 0) {
        onInsertImages(getSelectedImageUrls());
      }
    };

    // 删除选中的图片
    const handleDelete = () => {
      if (onDeleteImages && selectedImages.length > 0) {
        onDeleteImages(selectedImages);
        setSelectedImages([]);
      }
    };

    // 获取图片信息
    const getImageInfo = (url: string, index: number) => {
      // 尝试从screenshotInfos中查找对应的信息
      const info = screenshotInfos?.find((info) => info.url === url);

      if (info) {
        // 获取图片名称和时间戳
        let displayName = info.imageName || '';
        let timestamp = info.timestamp || new Date().toLocaleString();
        
        // 如果有图片名称，优先显示图片名称
        if (displayName) {
          // 确保显示的是YYMMDDHHMMSS+序号格式
          if (displayName.match(/^\d{12}\d*$/)) {
            // 已经是正确格式，直接使用
          } else {
            // 尝试从时间戳生成格式化名称
            try {
              const date = new Date(timestamp);
              if (!isNaN(date.getTime())) {
                const year = date.getFullYear().toString().slice(2);
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const day = date.getDate().toString().padStart(2, '0');
                const hours = date.getHours().toString().padStart(2, '0');
                const minutes = date.getMinutes().toString().padStart(2, '0');
                const seconds = date.getSeconds().toString().padStart(2, '0');
                displayName = `${year}${month}${day}${hours}${minutes}${seconds}${index + 1}`;
              }
            } catch (e) {
              console.warn('格式化图片名称失败:', e);
            }
          }
        } else {
          // 如果没有图片名称，从时间戳生成
          try {
            const date = new Date(timestamp);
            if (!isNaN(date.getTime())) {
              const year = date.getFullYear().toString().slice(2);
              const month = (date.getMonth() + 1).toString().padStart(2, '0');
              const day = date.getDate().toString().padStart(2, '0');
              const hours = date.getHours().toString().padStart(2, '0');
              const minutes = date.getMinutes().toString().padStart(2, '0');
              const seconds = date.getSeconds().toString().padStart(2, '0');
              displayName = `${year}${month}${day}${hours}${minutes}${seconds}${index + 1}`;
            }
          } catch (e) {
            console.warn('格式化图片名称失败:', e);
            displayName = `截图${index + 1}`;
          }
        }
        
        return {
          timestamp: displayName, // 使用格式化的名称替代时间戳显示
          originalTimestamp: timestamp, // 保留原始时间戳
          size: info.size || '未知大小',
          width: info.width || 0,
          height: info.height || 0,
        };
      }

      // 如果没有找到，则使用之前保存的时间戳生成名称
      const timestamp = imageTimestamps[url] || new Date().toLocaleString();
      let displayName = '';
      
      try {
        const date = new Date(timestamp);
        if (!isNaN(date.getTime())) {
          const year = date.getFullYear().toString().slice(2);
          const month = (date.getMonth() + 1).toString().padStart(2, '0');
          const day = date.getDate().toString().padStart(2, '0');
          const hours = date.getHours().toString().padStart(2, '0');
          const minutes = date.getMinutes().toString().padStart(2, '0');
          const seconds = date.getSeconds().toString().padStart(2, '0');
          displayName = `${year}${month}${day}${hours}${minutes}${seconds}${index + 1}`;
        }
      } catch (e) {
        console.warn('格式化图片名称失败:', e);
        displayName = `截图${index + 1}`;
      }
      
      return {
        timestamp: displayName, // 使用格式化的名称替代时间戳显示
        originalTimestamp: timestamp, // 保留原始时间戳
        size: '未知大小',
        width: 0,
        height: 0,
      };
    };

    // 处理预览图片
    const handlePreviewImage = (index: number) => {
      setCurrentPreviewIndex(index);
      setCustomPreviewVisible(true);
    };

    // 切换到上一张图片
    const handlePrevImage = () => {
      setCurrentPreviewIndex((prev) => (prev > 0 ? prev - 1 : imgs.length - 1));
    };

    // 切换到下一张图片
    const handleNextImage = () => {
      setCurrentPreviewIndex((prev) => (prev < imgs.length - 1 ? prev + 1 : 0));
    };

    // 关闭预览
    const handleClosePreview = () => {
      setCustomPreviewVisible(false);
    };

    // 处理键盘事件
    useEffect(() => {
      const handleKeyDown = (e: KeyboardEvent) => {
        if (!customPreviewVisible) return;

        switch (e.key) {
          case 'ArrowLeft':
            handlePrevImage();
            break;
          case 'ArrowRight':
            handleNextImage();
            break;
          case 'Escape':
            handleClosePreview();
            break;
          default:
            break;
        }
      };

      window.addEventListener('keydown', handleKeyDown);
      return () => {
        window.removeEventListener('keydown', handleKeyDown);
      };
    }, [customPreviewVisible, imgs.length]);

    return (
      <>
        {/* 自定义预览模态框 */}
        <Modal
          visible={customPreviewVisible}
          footer={null}
          closable={false}
          centered
          width='80%'
          bodyStyle={{
            padding: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.85)',
            position: 'relative',
            height: '80vh',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
          wrapClassName='screenshot-preview-modal'
          onCancel={handleClosePreview}
          title='截图预览'
        >
          {imgs.length > 0 && customPreviewVisible && (
            <>
              {/* 关闭按钮 - 上移到与标题同一行 */}
              <div
                style={{
                  position: 'absolute',
                  top: '-45px', // 上移到标题栏位置
                  right: '20px',
                  zIndex: 10,
                  cursor: 'pointer',
                  color: '#fff',
                  fontSize: '24px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '45px', // 与标题栏高度一致
                }}
                onClick={handleClosePreview}
              >
                <CloseOutlined />
              </div>

              {/* 图片区域 */}
              <div
                style={{
                  position: 'relative',
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <img
                  src={imgs[currentPreviewIndex]}
                  style={{
                    maxWidth: '90%',
                    maxHeight: '90%',
                    objectFit: 'contain',
                    boxShadow: '0 0 20px rgba(0,0,0,0.5)',
                    userSelect: 'none',
                    WebkitUserSelect: 'none',
                    MozUserSelect: 'none',
                    msUserSelect: 'none',
                    pointerEvents: 'auto',
                  }}
                  alt='截图预览'
                  draggable={false}
                  onContextMenu={(e) => e.preventDefault()}
                />

                {/* 左右切换按钮 */}
                {imgs.length > 1 && (
                  <>
                    <div
                      className='preview-nav-button'
                      style={{
                        position: 'absolute',
                        left: '10px',
                        top: '50%',
                        transform: 'translateY(-50%)',
                        width: '40px',
                        height: '40px',
                        borderRadius: '50%',
                        backgroundColor: 'rgba(0,0,0,0.5)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        cursor: 'pointer',
                      }}
                      onClick={handlePrevImage}
                    >
                      <LeftOutlined style={{ color: '#fff', fontSize: '18px' }} />
                    </div>
                    <div
                      className='preview-nav-button'
                      style={{
                        position: 'absolute',
                        right: '10px',
                        top: '50%',
                        transform: 'translateY(-50%)',
                        width: '40px',
                        height: '40px',
                        borderRadius: '50%',
                        backgroundColor: 'rgba(0,0,0,0.5)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        cursor: 'pointer',
                      }}
                      onClick={handleNextImage}
                    >
                      <RightOutlined style={{ color: '#fff', fontSize: '18px' }} />
                    </div>
                  </>
                )}
              </div>

              {/* 图片信息 */}
              <div
                style={{
                  position: 'absolute',
                  bottom: '10px',
                  left: 0,
                  width: '100%',
                  textAlign: 'center',
                  color: '#fff',
                  fontSize: '12px',
                }}
              >
                {currentPreviewIndex + 1} / {imgs.length} -{' '}
                {getImageInfo(imgs[currentPreviewIndex], currentPreviewIndex).timestamp}
                {/* 显示原始时间戳作为辅助信息 */}
                <div style={{ fontSize: '10px', opacity: 0.8 }}>
                  {getImageInfo(imgs[currentPreviewIndex], currentPreviewIndex).originalTimestamp}
                </div>
              </div>
            </>
          )}
        </Modal>

        <div style={{ height: '100%', padding: '0', overflow: 'auto' }}>
          {imgs && imgs.length > 0 ? (
            <>
              {/* 全选按钮和操作按钮 */}
              <div style={{ marginBottom: '8px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Checkbox
                  indeterminate={selectedImages.length > 0 && selectedImages.length < imgs.length}
                  checked={selectedImages.length === imgs.length}
                  onChange={handleSelectAll}
                  style={{ color: '#fff', fontSize: '12px' }}
                >
                  全选 ({imgs.length}张)
                </Checkbox>
              </div>

              {/* 图片网格 */}
              <div
                style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(3, 1fr)',
                  gap: '8px',
                  width: '100%',
                }}
              >
                {imgs.map((item, index) => (
                  <div
                    key={index}
                    style={{
                      position: 'relative',
                      aspectRatio: '1',
                      borderRadius: '0',
                      overflow: 'hidden',
                      border: selectedImages.includes(index) ? '2px solid #0066ff' : '1px solid #444',
                      backgroundColor: '#222',
                    }}
                  >
                    {/* 勾选框 */}
                    <div
                      style={{
                        position: 'absolute',
                        top: '2px',
                        left: '2px',
                        zIndex: 2,
                      }}
                    >
                      <Checkbox
                        checked={selectedImages.includes(index)}
                        onChange={(e) => handleImageSelect(index, e.target.checked)}
                        style={{ transform: 'scale(0.8)' }}
                      />
                    </div>

                    {/* 图片 */}
                    <Image
                      src={item}
                      width='100%'
                      height='100%'
                      style={{ objectFit: 'cover', cursor: 'pointer' }}
                      preview={false}
                      onClick={() => handlePreviewImage(index)}
                    />

                    {/* 图片日期标签 - 使用保存的时间戳 */}
                    <div
                      style={{
                        position: 'absolute',
                        bottom: '0',
                        left: '0',
                        width: '100%',
                        backgroundColor: 'rgba(0,0,0,0.5)',
                        color: '#fff',
                        fontSize: '10px',
                        padding: '2px 4px',
                        textAlign: 'center',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                      }}
                    >
                      {getImageInfo(item, index).timestamp}
                    </div>
                  </div>
                ))}
              </div>
            </>
          ) : (
            <div style={{ textAlign: 'center', color: '#888', marginTop: '20px', fontSize: '14px' }}>暂无截图</div>
          )}
        </div>
      </>
    );
  }
);

export default ScreenshotGallery;
