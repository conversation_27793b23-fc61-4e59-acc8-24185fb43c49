.pageContainer {
  // width: 100%;
  // height: calc(100vh - 120px);
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  margin: -40px;
  padding: 8px;
  margin-top: -35px;
}
.contentContainer {
  flex: 1;
  display: flex;
  flex-direction: row;
}
.editSider {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  gap: 8px;
}
.printButtons {
  height: 100%;
  background-color: rgb(64, 64, 64);
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
}

::-webkit-scrollbar {
  height: 0px;
  width: 10px;
  overflow-y: auto;
}
::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background: #595959;
}
::-webkit-scrollbar-track {
  -webkit-box-shadow: 0;
  border-radius: 5px;
  background: #7f7f7f;
}
