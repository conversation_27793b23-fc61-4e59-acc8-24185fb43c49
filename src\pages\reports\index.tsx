import React, { useEffect, useMemo, useState, useRef, ReactNode } from 'react';
import { type Dispatch, useSearchParams } from 'umi';
import { connect } from 'dva';
import {
  Card,
  Row,
  Col,
  ConfigProvider,
  Typography,
  Radio,
  Space,
  Button,
  Form,
  Select,
  Modal,
  Input,
  Empty,
  message,
  List,
  Image,
  Tag,
  Collapse,
  Steps,
  Flex,
  type StepProps,
} from 'antd';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  SyncOutlined,
  ClockCircleOutlined,
  FileTextOutlined,
  PictureOutlined,
  DownOutlined,
  RightOutlined,
} from '@ant-design/icons';
import type { RadioChangeEvent } from 'antd';
import { ProDescriptions } from '@ant-design/pro-components';
import styles from './styles.less';
import './styles.less';
import withAuth from '@/hocs/withAuth';
import { ReportType } from '@/models/reports';
import ClickButton from '@/components/ClickButton';
import PrintView from '@/components/PrintView';
import printJS from 'print-js';
import ReportModel from './components/reportModel';
import RichTextEditor, { RichTextEditorRef } from '@/components/RichTextEditor';
import PatientInfoCard from './components/patientInfoCard';
import ChooseTemplate from './components/chooseTemplate';
import ScreenshotGallery from './components/screenshotGallery';
import RecallReport from './components/recallReport';
import { LoginType, UserInfoType } from '@/types/user';
import { CheckReportParams, CheckStepItemType } from '@/types/reports';
import { FetchCheckProcess, CheckReport, RecallReport as FetchRecallReport, getImage, saveTemplateImage } from '@/services/reports';

import { divide } from 'lodash';

const { Title, Paragraph } = Typography;
const { TextArea } = Input;

interface InfoProps {
  dispatch: Dispatch;
  radio?: any;
  clini?: any;
  reportLoading?: boolean;
  patientInfo?: any;
  studyId?: any;
  patientId?: any;
  isRepoSave?: boolean;
  templates?: any[];
  tempSaveMsg?: string;
  pubRepoMsg?: string;
  submitRepoMsg?: string;
  status?: number;
  checkMsg?: string;
  cancelCheckMsg?: string;
  imgs?: any[];
  video?: any;
  templateTypes?: any[];
  reportId?: number;
  userInfo: UserInfoType;
}
type CheckFlowItem = CheckStepItemType & {
  agree: number;
  reason: string;
};
/**
 * status:
 * 0：初始状态
 * 1：仅仅保存报告
 * 2：医生发布报告
 * 3：退回或报告审核不通过
 * 4：报告审核通过
 */

const Reports: React.FC<InfoProps> = ({
  radio, // 影像学研究
  clini, // 临床诊断
  patientInfo,
  isRepoSave, // 报告保存成功提示
  tempSaveMsg, // 模板保存成功提示
  submitRepoMsg, // 报告提交成功提示
  imgs, // 图片列表
  video, // 视频
  templateTypes,
  reportId,
  userInfo,
  dispatch,
}) => {
  // 从路由查询参数中获取 studyId
  const [searchParams] = useSearchParams();
  const studyId = searchParams.get('studyId') || '';
  const pageType = searchParams.get('type') || '';
  const [isChooseTemplateModalOpen, setChooseTemplateModalOpen] = useState(false);

  const [messageApi, contextHolder] = message.useMessage();

  // 控制右侧Collapse面板的展开状态
  const [activeKeys, setActiveKeys] = useState<string[]>(['screenshots']);

  // 富文本编辑器引用
  const radioEditorRef = useRef<RichTextEditorRef>(null);
  const cliniEditorRef = useRef<RichTextEditorRef>(null);

  // 处理富文本编辑器中的图片上传
  const handleImageUpload = async (file: File, callback: (imageName: string) => void) => {
    try {
      // 使用模板图片上传接口
      const response = await saveTemplateImage(file);

      // 添加调试信息
      console.log('图片上传响应:', response);
      console.log('响应类型:', typeof response);
      console.log('响应键:', response ? Object.keys(response) : 'response is null/undefined');

      // 从响应中获取图片名称，模板图片上传接口返回的是 Name 字段
      const imageName = response?.Name || response?.name || response?.Message || response?.message || response?.filename || response?.fileName;

      console.log('提取的图片名称:', imageName);

      if (imageName) {
        // 调用回调函数，传递图片名称
        callback(imageName);

        messageApi.success('图片上传成功');
      } else {
        console.error('无法从响应中获取图片名称，完整响应:', JSON.stringify(response, null, 2));
        messageApi.error('图片上传失败：未获取到图片名称');
      }
    } catch (error) {
      console.error('图片上传失败:', error);
      messageApi.error('图片上传失败');
    }
  };

  // 处理图片插入到富文本编辑器
  const handleInsertImages = async (imageNames: string[]) => {
    // 默认插入到影像所见编辑器
    const targetEditor = radioEditorRef.current;

    if (targetEditor) {
      const quillInstance = targetEditor.getEditor();
      if (quillInstance) {
        // 获取当前光标位置或在最后插入
        const range = quillInstance.getSelection() || { index: quillInstance.getLength(), length: 0 };

        // 逐个处理图片名称，获取blob数据并插入
        for (let i = 0; i < imageNames.length; i++) {
          const imageName = imageNames[i];
          const insertIndex = range.index + i * 2; // 每次插入后位置会变化

          try {
            // 通过API获取图片blob数据
            const blob = await getImage({ ImageName: imageName });
            if (blob) {
              // 创建blob URL
              const blobUrl = URL.createObjectURL(blob);

              // 获取富文本编辑器的图片映射引用
              if (targetEditor.imageMapRef?.current) {
                targetEditor.imageMapRef.current.set(blobUrl, imageName);
              }

              // 插入图片
              quillInstance.insertEmbed(insertIndex, 'image', blobUrl);
              // 在图片后添加换行
              quillInstance.insertText(insertIndex + 1, '\n');
            } else {
              console.error('获取图片失败:', imageName);
              messageApi.error(`图片 ${imageName} 加载失败`);
            }
          } catch (error) {
            console.error('获取图片失败:', imageName, error);
            messageApi.error(`图片 ${imageName} 加载失败`);
          }
        }

        // 移动光标到最后插入位置的下一行
        const finalIndex = range.index + imageNames.length * 2;
        quillInstance.setSelection(finalIndex, 0);
      }
    }
  };

  /**
   * 自动保存逻辑
   */
  const [lastSavedTime, setLastSavedTime] = useState<string>('');
  // 记录上次保存时的报告内容，用于判断是否发生了修改
  const savedContentRef = useRef<{ radio: string; clini: string }>({
    radio: radio || '',
    clini: clini || '',
  });

  const formatTime = (date: Date) => {
    const pad = (n: number) => (n < 10 ? `0${n}` : n);
    return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())}  ${pad(date.getHours())}:${pad(
      date.getMinutes()
    )}:${pad(date.getSeconds())}`;
  };

  const [checkFlow, setCheckFlow] = useState<CheckFlowItem[]>([]);

  // 利用useEffect来更新state，state初值取自props时存在同步问题
  useEffect(() => {
    if (isRepoSave) {
      messageApi.success('保存成功');
      // 更新最后保存时间与内容
      const now = new Date();
      setLastSavedTime(formatTime(now));
      savedContentRef.current = {
        radio: radio || '',
        clini: clini || '',
      };
      // 100ms后恢复false状态
      setTimeout(() => {
        dispatch({
          type: 'reports/save',
          payload: {
            isRepoSave: false,
          },
        });
      }, 100);
    }
  }, [isRepoSave]);

  // 每分钟检查一次内容是否修改，如已修改则自动保存
  useEffect(() => {
    const timer = setInterval(() => {
      const currentRadio = radio || '';
      const currentClini = clini || '';
      if ((currentRadio !== savedContentRef.current.radio || currentClini !== savedContentRef.current.clini) && pageType == 'report') {
        // 触发自动保存
        dispatch({
          type: 'reports/saveReport',
          payload: {
            StudyId: studyId,
            RadiographicFinding: currentRadio,
            ClinicalDiagnosis: currentClini,
          },
        });
      }
    }, 60 * 1000); // 60 秒

    return () => clearInterval(timer);
  }, [radio, clini, studyId]);

  // 模板保存成功提示
  useEffect(() => {
    if (tempSaveMsg) {
      messageApi.success(tempSaveMsg);
      // 重新获取模板列表
      dispatch({
        type: 'reports/fetchTemplates',
        payload: {
          Version: 20230101,
        },
      });
      // 100ms后恢复空状态
      setTimeout(() => {
        dispatch({
          type: 'reports/save',
          payload: {
            tempSaveMsg: '',
          },
        });
      }, 100);
    }
  }, [tempSaveMsg]);

  // 报告提交成功提示
  useEffect(() => {
    if (submitRepoMsg) {
      messageApi.success(submitRepoMsg);
      // 100ms后恢复空状态
      setTimeout(() => {
        dispatch({
          type: 'reports/save',
          payload: {
            submitRepoMsg: '',
          },
        });
      }, 100);
    }
  }, [submitRepoMsg]);

  /*
   审核 start
  */
  const [agree, setAgree] = useState<number>(1);
  const [reason, setReason] = useState<string>('');
  // 根据状态 显示审核流程
  const checkStepHtml = (item: CheckFlowItem) => {
    switch (item.Status_of_Report) {
      case 0:
      case 1:
        return;
      case 2:
        return (
          <Flex>
            <div className={styles.checkStepIcon}>
              <div className={styles.circle}></div>
              <div className={styles.line}></div>
            </div>
            <div className={styles.checkStep}>
              <Flex justify='space-between' align='end'>
                <Flex style={{ flex: 1 }} gap={6} vertical>
                  <span>提交审核</span>
                  <span className={styles.checkStep_name}>{item.Operator}</span>
                </Flex>
                <div className={styles.checkStep_time}>{item.Change_Date}</div>
              </Flex>
              <div></div>
            </div>
          </Flex>
        );
      case 3:
        return (
          <Flex>
            <div className={styles.checkStepIcon}>
              <div className={styles.circle}></div>
              <div className={styles.line}></div>
            </div>
            <div className={styles.checkStep}>
              <Flex justify='space-between' align='end'>
                <Flex style={{ flex: 1 }} gap={6} vertical>
                  <span>召回后待提交</span>
                  <span className={styles.checkStep_name}>{item.Operator}</span>
                </Flex>
                <div className={styles.checkStep_time}>{item.Change_Date}</div>
              </Flex>
              <div></div>
            </div>
          </Flex>
        );
      case 4:
        return (
          <Flex>
            <div className={styles.checkStepIcon}>
              <div className={styles.circle}></div>
              <div className={styles.line}></div>
            </div>
            <div className={styles.checkStep}>
              <Flex justify='space-between' align='end'>
                <Flex style={{ flex: 1 }} gap={6} vertical>
                  <span>审核不通过</span>
                  <span className={styles.checkStep_name}>{item.Operator}</span>
                </Flex>
                <div className={styles.checkStep_time}>{item.Change_Date}</div>
              </Flex>
              <div className={styles.checkStep_reason}>{item.RejectedReason}</div>
            </div>
          </Flex>
        );
      case 5:
        return (
          <Flex>
            <div className={styles.checkStepIcon}>
              <div className={styles.circle}></div>
              <div className={styles.line}></div>
            </div>
            <div className={styles.checkStep}>
              <Flex justify='space-between' align='end'>
                <Flex style={{ flex: 1 }} gap={6} vertical>
                  <span>审核通过</span>
                  {item.Operator && <span className={styles.checkStep_name}>{item.Operator}</span>}
                </Flex>
                <div className={styles.checkStep_time}>{item.Change_Date}</div>
              </Flex>
              <div></div>
            </div>
          </Flex>
        );
      default:
        return;
    }
  };
  // 审核流程list
  useEffect(() => {
    if (typeof pageType === 'string' && pageType === 'check' && reportId) {
      FetchCheckProcess(reportId).then((res) => {
        let arr = res?.Records.reverse().map((item: any) => {
          return {
            ...item,
            agree: 1,
            reason: '',
          };
        });
        setCheckFlow(arr);
      });
    }
  }, [reportId, patientInfo]);
  /*
    审核 end
   */

  /*
    召回 start
  */
  const [recallVisible, setRecallVisible] = useState(false);
  /*
    召回 end
  */

  useEffect(() => {
    if (!studyId) return; // 没有 studyId 时不执行后续逻辑
    // 重新打开页面时根据路由中的 studyId 更新报告
    dispatch({
      type: 'reports/getReport',
      payload: {
        StudyId: studyId,
      },
    });

    dispatch({
      type: 'reports/listTemplateType',
      payload: {
        Limit: -1,
        Offset: -1,
      },
    });

    // 获取团队模板分类
    dispatch({
      type: 'reports/listGroupTemplateType',
      payload: {
        Limit: -1,
        Offset: -1,
      },
    });

    // 获取截图路径列表
    dispatch({
      type: 'reports/listImages',
      payload: { StudyId: studyId },
    });
  }, [studyId]);

  // 页面加载时直接弹出模板选择窗口
  // useEffect(() => {
  //   setChooseTemplateModalOpen(true);
  // }, []);

  // 如果影像所见和诊断结论均为空，则弹出模板选择窗口
  useEffect(() => {
    const isRadioEmpty = !radio || (typeof radio === 'string' && radio.trim() === '');
    const isCliniEmpty = !clini || (typeof clini === 'string' && clini.trim() === '');
    // 只有在影像所见和诊断结论都为空，且状态不是1（待提交）、2（审核中）、4（已退回）或5（已审核）时弹出
    if (
      isRadioEmpty &&
      isCliniEmpty &&
      patientInfo &&
      patientInfo.StatusOfReport !== 1 &&
      patientInfo.StatusOfReport !== 2 && // 新增：审核中不弹窗
      patientInfo.StatusOfReport !== 4 &&
      patientInfo.StatusOfReport !== 5
    ) {
      setChooseTemplateModalOpen(true);
    } else {
      setChooseTemplateModalOpen(false);
    }
  }, [radio, clini, patientInfo]);

  const columns = [
    {
      label: (
        <span>
          姓<span style={{ display: 'inline-block', width: 16 }}></span>名
        </span>
      ),
      dataIndex: 'Name',
    },
    {
      label: (
        <span>
          性<span style={{ display: 'inline-block', width: 48 }}></span>别
        </span>
      ),
      dataIndex: 'Sex',
    },
    {
      label: (
        <span>
          年<span style={{ display: 'inline-block', width: 32 }}></span>龄
        </span>
      ),
      dataIndex: 'Age',
    },
    {
      label: (
        <span>
          体<span style={{ display: 'inline-block', width: 32 }}></span>重
        </span>
      ),
      dataIndex: 'Weight',
    },
    {
      label: (
        <span>
          血<span style={{ display: 'inline-block', width: 32 }}></span>糖
        </span>
      ),
    },
    {
      label: '检查号',
      dataIndex: 'StudyId',
    },
    {
      label: '放射性药物',
      dataIndex: 'Medicine',
    },
    {
      label: '检查项目',
      dataIndex: 'PartChecked',
    },
    {
      label: '检查时间',
      dataIndex: 'ExamDate', // 后端缺失
    },
    {
      label: '审核状态',
      dataIndex: 'StatusOfReport',
      render: (_: any, record: any) => {
        const status = record.StatusOfReport;
        const getTag = (text: string, color: string) => (
          <Tag style={{ color, borderColor: color, backgroundColor: 'transparent' }}>{text}</Tag>
        );
        switch (status) {
          case 0:
            return getTag('无报告', '#8c8c8c');
          case 1:
            return getTag('待提交', '#1677ff');
          case 2:
            return getTag('审核中', '#1f69b4');
          case 3:
            return getTag('待提交', '#1677ff'); // 召回后状态变为待提交
          case 4:
            return getTag('已退回', '#ff4d4f');
          case 5:
            return getTag('已审核', '#52c41a');
          default:
            return getTag('未知状态', '#8c8c8c');
        }
      },
    },
  ];

  // 审核报高
  const handleCheck = (params: CheckReportParams) => {
    CheckReport(params)
      .then((res) => {

        message.success(res.Message);
        // 更新 报告状态
        dispatch({
          type: 'reports/getReport',
          payload: {
            StudyId: studyId,
          },
        });
        channel.postMessage('刷新患者列表！')
        // 成功后跳转到患者列表页面
        setTimeout(() => {
          window.location.href = '/patients';
        }, 300);
      })
      .catch((err) => {

      });
  };

  // 审核召回
  const handleBackCheck = () => {
    FetchRecallReport({
      StudyId: studyId,
      Id: reportId,
    })
      .then((res) => {
        message.success(res.Message);
        // 获取报告
        dispatch({
          type: 'reports/getReport',
          payload: {
            StudyId: studyId,
          },
        });
        setRecallVisible(false);
        channel.postMessage('刷新患者列表！')
      })
      .catch((err) => {

      });
  };

  // 修改报告状态
  const channel = new BroadcastChannel('change_report');
  useEffect(() => {
    return () => {
      channel.close(); // 清理
    };
  }, []);

  return (
    <>
      {contextHolder}
      <ChooseTemplate
        visible={isChooseTemplateModalOpen}
        onClose={() => setChooseTemplateModalOpen(false)}
        onConfirm={(template) => {
          if (template && template.content) {
            dispatch({
              type: 'reports/save',
              payload: {
                radioFind: template.content.radiographicFinding,
                cliniDiag: template.content.clinicalDiagnosis,
              },
            });
          }
          setChooseTemplateModalOpen(false);
        }}
      />
      <style>{`
        .custom-scrollbar {
          scrollbar-width: thin;
          scrollbar-color: #666 transparent;
        }
        
        .custom-scrollbar::-webkit-scrollbar {
          width: 11.09px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-track {
          background: transparent;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background-color: #666;
          border-radius: 5.5px;
          min-height: 35.22px;
          height: 35.22px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background-color: #888;
        }
        
        .custom-scrollbar::-webkit-scrollbar-corner {
          background: transparent;
        }
      `}</style>
      <ConfigProvider
        theme={{
          token: {
            colorBgContainer: '#5b5b5b',
            // colorBgElevated: '#474747',
            colorBgBase: '#5b5b5b',
            colorTextBase: 'white',
          },
          components: {
            Card: {
              headerHeight: 40,
            },
            Button: {
              colorBgContainer: '#404040',
              primaryShadow: 'transparent',
            },
            Descriptions: {
              contentColor: 'white',
              titleColor: 'white',
              fontSize: 16,
              itemPaddingBottom: 8,
              titleMarginBottom: 10,
            },
            Select: {
              colorBgContainer: '#404040',
              colorBorder: 'white',
              optionSelectedBg: '#404040',
              optionActiveBg: '#2d2d2d',
              borderRadius: 14,
            },
            Input: {
              colorBorder: 'white',
            },
          },
        }}
      >
        <>
          {/* 整体左右分栏布局 */}
          <div
            style={{
              height: 'calc(100vh - 90px)',
              padding: '20px',
              display: 'flex',
              gap: '17px',
              minHeight: '600px',
              boxSizing: 'border-box',
              maxWidth: '100vw',
              overflow: 'hidden',
            }}
          >
            {/* 左侧：患者信息 + 编辑器 */}
            <div
              style={{
                flex: '1',
                minWidth: '800px',
                height: '100%',
                backgroundColor: '#262628',
                borderRadius: '8px',
                padding: '20px',
                display: 'flex',
                flexDirection: 'column',
                gap: '20px',
                position: 'relative',
                boxSizing: 'border-box',
              }}
            >
              {/* 患者信息和编辑器容器 */}
              <div
                style={{
                  flex: 1,
                  backgroundColor: 'transparent',
                  borderRadius: '8px',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '20px',
                  minHeight: 0,
                }}
              >
                {/* 患者信息卡片 */}
                <Card
                  style={{
                    flex: '0 0 auto',
                    width: '100%',
                    height: '108px',
                  }}
                  bodyStyle={{
                    padding: '12px',
                    height: '100%',
                    overflow: 'hidden',
                  }}
                >
                  <ProDescriptions
                    className={styles.description}
                    dataSource={patientInfo}
                    column={5}
                    columns={columns}
                    size='small'
                    labelStyle={{
                      fontFamily: 'Microsoft YaHei UI',
                      fontWeight: 400,
                      fontSize: '16px',
                      lineHeight: '20px',
                      width: '100px',
                      padding: '4px 8px',
                    }}
                    contentStyle={{
                      fontFamily: 'Microsoft YaHei UI',
                      fontWeight: 400,
                      fontSize: '16px',
                      lineHeight: '20px',
                      padding: '4px 8px',
                    }}
                  />
                </Card>

                {/* 编辑器区域 */}
                <div
                  style={{
                    height: 'calc(100% - 128px - 102px)',
                    display: 'flex',
                    gap: '20px',
                    minHeight: 0,
                  }}
                >
                  <ConfigProvider
                    theme={{
                      token: {
                        colorBgContainer: 'white',
                        colorBgBase: 'white',
                        colorTextBase: 'black',
                      },
                      components: {
                        Card: {
                          headerHeight: 40,
                        },
                      },
                    }}
                  >
                    <div
                      style={{
                        flex: '1 1 50%',
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        minHeight: 0,
                        minWidth: 0,
                      }}
                    >
                      {/* 影像所见标题 */}
                      <div
                        style={{
                          color: 'white',
                          fontSize: '18px',
                          fontWeight: 400,
                          marginBottom: '8px',
                          fontFamily: 'Microsoft YaHei UI',
                          flexShrink: 0,
                        }}
                      >
                        影像所见
                      </div>
                      <div style={{ flex: 1, minHeight: 0 }}>
                        <RichTextEditor
                          ref={radioEditorRef}
                          className={styles.quill}
                          value={radio}
                          readOnly={pageType == 'check'}
                          maxLength={9999}
                          onImageUpload={handleImageUpload}
                          onChange={(value) =>
                            dispatch({
                              type: 'reports/save',
                              payload: { radioFind: value },
                            })
                          }
                        />
                      </div>
                    </div>
                    <div
                      style={{
                        flex: '1 1 50%',
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        minHeight: 0,
                        minWidth: 0,
                      }}
                    >
                      {/* 诊断结论标题 */}
                      <div
                        style={{
                          color: 'white',
                          fontSize: '18px',
                          fontWeight: 400,
                          marginBottom: '8px',
                          fontFamily: 'Microsoft YaHei UI',
                          flexShrink: 0,
                        }}
                      >
                        诊断结论
                      </div>
                      <div style={{ flex: 1, minHeight: 0 }}>
                        <RichTextEditor
                          ref={cliniEditorRef}
                          className={styles.quill}
                          value={clini}
                          readOnly={pageType == 'check'}
                          maxLength={9999}
                          onImageUpload={handleImageUpload}
                          onChange={(value) =>
                            dispatch({
                              type: 'reports/save',
                              payload: { cliniDiag: value },
                            })
                          }
                        />
                      </div>
                    </div>
                  </ConfigProvider>
                </div>
              </div>

              {/* 左下角自动保存时间 */}
              {pageType == 'report' && (
                <div
                  style={{
                    position: 'absolute',
                    bottom: '37.5px',
                    left: '20px',
                    color: '#CCCCCC',
                    fontSize: '18px',
                    fontWeight: 400,
                    fontFamily: 'Microsoft YaHei UI',
                    zIndex: 10,
                  }}
                >
                  {lastSavedTime ? `自动保存于：${lastSavedTime}` : '尚未保存'}
                </div>
              )}

              {/* 右下角按钮组 */}
              {pageType == 'report' && (
                <div
                  style={{
                    position: 'absolute',
                    bottom: '37.5px',
                    right: '20px',
                    display: 'flex',
                    gap: '12px',
                    zIndex: 10,
                  }}
                >
                  <ConfigProvider
                    theme={{
                      components: {
                        Button: {
                          colorBgContainer: '#262628',
                          colorText: 'white',
                          colorBorder: '#666',
                          borderRadius: 6,
                          paddingInline: 16,
                          paddingBlock: 8,
                        },
                      },
                    }}
                  >
                    <Button
                      size='middle'
                      onClick={() => {
                        // 跳转到阅片页面
                        if (studyId) {
                          window.open(`/viewer?studyId=${studyId}`, '_blank');
                        } else {
                          messageApi.error('缺少检查号，无法跳转到阅片页面');
                        }
                      }}
                    >
                      影像
                    </Button>
                    <Button
                      size='middle'
                      onClick={async () => {
                        await dispatch({
                          type: 'reports/saveReport',
                          payload: {
                            Id: reportId,
                            StudyId: studyId,
                            RadiographicFinding: radio,
                            ClinicalDiagnosis: clini,
                            ...(patientInfo?.StatusOfReport === 0 ? { StatusOfReport: 1 } : {}),
                          },
                        });

                        // 保存后无论什么状态都刷新报告，确保状态及时更新
                        await dispatch({
                          type: 'reports/getReport',
                          payload: {
                            StudyId: studyId,
                          },
                        });

                        channel.postMessage('刷新患者列表！')
                      }}
                    >
                      保存
                    </Button>
                  </ConfigProvider>
                  <ConfigProvider
                    theme={{
                      components: {
                        Button: {
                          colorPrimary: '#1F69B4',
                          borderRadius: 6,
                          paddingInline: 16,
                          paddingBlock: 8,
                        },
                      },
                    }}
                  >
                    <Button
                      type='primary'
                      size='middle'
                      onClick={async () => {
                        channel.postMessage('刷新患者列表！')
                        await dispatch({
                          type: 'reports/submitReport',
                          payload: {
                            Id: reportId,
                            StudyId: studyId,
                            RadiographicFinding: radio,
                            ClinicalDiagnosis: clini,
                          },
                        });
                        await dispatch({
                          type: 'reports/getReport',
                          payload: {
                            StudyId: studyId,
                          },
                        });
                        // 提交后刷新审核流程（仅在审核页面时）
                        if (reportId) {
                          FetchCheckProcess(reportId).then((res) => {
                            let arr = res?.Records.reverse().map((item: any) => ({
                              ...item,
                              agree: 1,
                              reason: '',
                            }));
                            setCheckFlow(arr);
                          });
                        }
                        // 跳转到患者列表页面
                        setTimeout(() => {
                          window.location.href = '/patients';
                        }, 300);
                      }}
                    >
                      提交
                    </Button>
                  </ConfigProvider>
                </div>
              )}
            </div>

            {/* 右侧：全高度工具组件 */}
            <div
              style={{
                flex: '0 0 290px',
                minWidth: '290px',
                height: '100%',
                maxWidth: '350px',
              }}
            >
              {/* 报告 */}
              {pageType == 'report' && (
                <ConfigProvider
                  theme={{
                    token: {
                      colorBgContainer: '#4a4a4a',
                      colorBgBase: '#4a4a4a',
                      colorTextBase: 'white',
                    },
                    components: {
                      Card: {
                        colorBgContainer: '#4a4a4a',
                        colorTextHeading: 'white',
                      },
                      Select: {
                        colorBgContainer: '#404040',
                        colorBorder: '#666',
                        optionSelectedBg: '#404040',
                        optionActiveBg: '#2d2d2d',
                        colorText: 'white',
                      },
                      Button: {
                        colorBgContainer: '#404040',
                        colorText: 'white',
                        colorBorder: '#d9d9d9',
                      },
                      List: {
                        colorBgContainer: 'transparent',
                      },
                      Collapse: {
                        headerBg: '#404040',
                        contentBg: '#4a4a4a',
                      },
                    },
                  }}
                >
                  <div
                    style={{
                      height: '100%',
                      backgroundColor: '#4a4a4a',
                      borderRadius: '8px',
                    }}
                  >
                    <Collapse
                      size='large'
                      ghost
                      expandIconPosition='end'
                      activeKey={activeKeys}
                      className={styles.collapse}
                      onChange={(keys) => {
                        const keysArray = Array.isArray(keys) ? keys : [keys];

                        // 如果点击的是报告模板，则关闭截图库，只展开报告模板
                        if (keysArray.includes('template') && !activeKeys.includes('template')) {
                          setActiveKeys(['template']);
                        }
                        // 如果点击的是截图库，则关闭报告模板，只展开截图库
                        else if (keysArray.includes('screenshots') && !activeKeys.includes('screenshots')) {
                          setActiveKeys(['screenshots']);
                        }
                        // 其他情况（如关闭面板）正常处理
                        else {
                          setActiveKeys(keysArray as string[]);
                        }
                      }}
                      expandIcon={({ isActive }) => (
                        <img
                          src={isActive ? '/icon/report/unfold_on.png' : '/icon/report/unfold_off.png'}
                          alt={isActive ? 'fold' : 'unfold'}
                          style={{
                            width: '16px',
                            height: '16px',
                            filter: 'brightness(0) invert(1)', // 将图标变为白色
                          }}
                        />
                      )}
                      style={{ height: '100%' }}
                      items={[
                        {
                          key: 'template',
                          label: (
                            <div
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                color: 'white',
                                fontSize: '16px',
                                fontWeight: 'bold',
                              }}
                            >
                              <FileTextOutlined style={{ marginRight: '8px', fontSize: '18px' }} />
                              报告模板
                            </div>
                          ),
                          children: (
                            <div style={{ padding: '0 0 16px', height: 'calc(100vh - 268px)', overflow: 'auto' }}>
                              <ReportModel radioFind={radio} cliniDiag={clini} />
                            </div>
                          ),
                        },
                        {
                          key: 'screenshots',
                          label: (
                            <div
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                color: 'white',
                                fontSize: '16px',
                                fontWeight: 'bold',
                              }}
                            >
                              <PictureOutlined style={{ marginRight: '8px', fontSize: '18px' }} />
                              截图库
                            </div>
                          ),
                          children: <ScreenshotGallery imgs={imgs} onInsertImages={handleInsertImages} />,
                        },
                      ]}
                    />
                  </div>
                </ConfigProvider>
              )}
              {/* 审核 */}
              {pageType == 'check' && (
                <ConfigProvider
                  theme={{
                    components: {
                      Button: {
                        colorBorder: '#d9d9d9',
                      },
                    },
                  }}
                >
                  <Flex vertical className={styles.check}>
                    <div className={styles.check_name}>审核流程</div>
                    <div className={styles.check_flow}>
                      {/* 审核 */}
                      {userInfo.Role != 'doctor' &&
                        pageType == 'check' &&
                        patientInfo?.DoctorIdOfReport != userInfo?.Id &&
                        patientInfo?.StatusOfReport == 2 && (
                          <Flex>
                            <div className={styles.checkStepIcon}>
                              <div className={styles.circle}></div>
                              <div className={styles.line}></div>
                            </div>
                            <div className={styles.checkStep}>
                              <div>审核</div>
                              <Radio.Group
                                style={{ marginTop: '10px' }}
                                options={[
                                  { value: 1, label: '通过' },
                                  { value: 0, label: '不通过' },
                                ]}
                                value={agree}
                                onChange={(e) => setAgree(e.target.value)}
                              />
                              {/* 原因输入框，无论通过/不通过都显示 */}
                              <TextArea
                                style={{ marginTop: '10px' }}
                                placeholder={agree === 0 ? '请输入退回原因' : '（可选）填写审核备注'}
                                autoSize={{ minRows: 2, maxRows: 2 }}
                                value={reason}
                                maxLength={100}
                                onChange={(e) => setReason(e.target.value)}
                                allowClear
                              />
                              {/* 仅不通过且未填写时显示红色提示 */}
                              {agree === 0 && reason.trim() === '' && (
                                <div style={{ color: 'red', marginTop: 4, fontWeight: 'bold' }}>请填写退回原因</div>
                              )}
                            </div>
                          </Flex>
                        )}
                      {checkFlow.length && checkFlow.map((item, index) => checkStepHtml(item))}
                    </div>
                    <Flex gap={16} justify='end' className='check_action'>
                      <Button
                        type='default'
                        onClick={() => {
                          // 跳转到阅片页面
                          if (studyId) {
                            window.open(`/viewer?studyId=${studyId}`, '_blank');
                          } else {
                            messageApi.error('缺少检查号，无法跳转到阅片页面');
                          }
                        }}
                      >
                        影像
                      </Button>
                      {userInfo.Role != 'doctor' &&
                        pageType == 'check' &&
                        patientInfo?.DoctorIdOfReport != userInfo?.Id &&
                        patientInfo?.StatusOfReport == 2 && (
                          <Button
                            type='default'
                            disabled={agree === 0 && reason.trim() === ''}
                            onClick={() =>
                              handleCheck({
                                Id: reportId,
                                StudyId: studyId,
                                IsPass: agree,
                                RejectedReason: reason,
                              })
                            }
                          >
                            确定
                          </Button>
                        )}
                      {pageType == 'check' &&
                        patientInfo?.DoctorIdOfReport == userInfo?.Id &&
                        patientInfo?.StatusOfReport == 2 && (
                          <Button type='default' onClick={() => setRecallVisible(true)}>
                            召回
                          </Button>
                        )}
                    </Flex>
                  </Flex>
                </ConfigProvider>
              )}
            </div>
          </div>
          {/* 召回弹窗 */}
          <RecallReport visible={recallVisible} handleOk={() => handleBackCheck()} handleCancel={() => setRecallVisible(false)} />
        </>
      </ConfigProvider>
    </>
  );
};

const mapStateToProps = ({ reports, user }: { reports: ReportType; user: LoginType }) => {
  return {
    // 在patient页中dispatch以下信息
    reportId: reports.reportId,
    radio: reports.radioFind, // 影像学研究
    clini: reports.cliniDiag, // 临床诊断
    reportLoading: reports.reportLoading,
    patientInfo: reports.patientInfo,
    studyId: reports.studyId,
    patientId: reports.patientId,
    isRepoSave: reports.isRepoSave,
    templates: reports.templates,
    tempSaveMsg: reports.tempSaveMsg,
    pubRepoMsg: reports.pubRepoMsg,
    submitRepoMsg: reports.submitRepoMsg,
    status: reports.status,
    checkMsg: reports.checkMsg,
    cancelCheckMsg: reports.cancelCheckMsg,
    imgs: reports.imgs,
    video: reports.video,
    templateTypes: reports.templateTypes,
    userInfo: user.userInfo,
  };
};

export default withAuth(connect(mapStateToProps)(Reports));
