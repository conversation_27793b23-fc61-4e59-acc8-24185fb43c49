import axios from 'axios';
import storeUtil from '@/utils/store';

// 保存截图到服务器
export async function saveScreenshot(imageBlob: Blob, studyId: string) {
  const formData = new FormData();
  formData.append('image', imageBlob);
  formData.append('StudyId', studyId);

  try {
    const response = await axios.post(`${CLOUD_API}/public/saveImage`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        Authorization: storeUtil.get('token')?.value,
      },
    });
    return response.data;
  } catch (error) {
    console.error('保存截图失败:', error);
    throw error;
  }
}
