import { useState, useEffect } from 'react';
import type { Dispatch } from 'umi';
import dayjs from 'dayjs';
import { connect } from 'dva';
import { Button, Checkbox, Form, Input, Layout, ConfigProvider, Row, Col, Flex } from 'antd';
import { LockOutlined, UserOutlined, EyeOutlined , EyeInvisibleOutlined} from '@ant-design/icons';
import { LoginType } from '@/types/user';
import CustomIcon from '@/components/CustomIcon';
import storeUtil from '@/utils/store';
import style from './login.less';

interface LoginProps {
  dispatch: Dispatch;
  data?: any;
  correct: boolean;
}

const Login: React.FC<LoginProps> = (props) => {
  const { data, correct, dispatch } = props;
  const [form] = Form.useForm();
  const [isSave, setIsSave] = useState(storeUtil.get('isSave').value ? true : false); // 是否保存密码
  console.log(storeUtil.get('isSave').value);
  const [isForget, setIsForget] = useState(false); // 是否忘记密码

  const onFinish = (values: any) => {
    dispatch({
      type: 'user/login',
      payload: { ...values },
    });
    if (isSave) {
      storeUtil.set('loginAccount', values.Name);
      storeUtil.set('loginPwd', values.Pwd);
    } else {
      storeUtil.remove('loginAccount');
      storeUtil.remove('loginPwd');
    }
  };
  useEffect(() => {
    if(!isSave) form.resetFields(); 
    else {
      form.setFieldsValue({
        Name: storeUtil.get('loginAccount').value,
        Pwd: storeUtil.get('loginPwd').value,
      });
    }
  }, [dispatch]);

  return (
    <ConfigProvider
      theme={{
        token: { colorPrimary: '#1F69B4' },
        components: {
          Input: {
            inputFontSize: 12,
            paddingBlock: 8,
            paddingInline: 14,
            borderRadius: 10,
            colorBgContainer: '#ffffff',
            colorBorder: '#656464',
            colorTextPlaceholder: '#656464',
            hoverBorderColor: '#656464',
            activeBorderColor: '#656464',
          },
          Checkbox: {
            // colorBgContainer: '#57575C',
            colorBorder: '#57575C',
          },
          Form: {
            fontSize: 12,
            colorPrimary: '#656464',
            labelFontSize: 12,
            labelColor: '#656464',
            itemMarginBottom: 12,
            verticalLabelPadding: '0 0 4px',
          },
          Button: {
            fontSize: 16,
            paddingBlock: 12,
            borderRadius: 10,
            controlHeight: 36,
          },
        },
      }}
    >
      <div className={style.login_bg}>
        <Row className={style.center}>
          <Col span='24'>
            <Row className={style.login_form} justify={'center'} align={'middle'}>
              <Col span={12} className={style.left}>
                <img className={style.img} src='/icon/login_icon.png' />
              </Col>
              <Col span={12} className={style.right}>
                <img className={style.logo_img} src='/icon/login_form_icon.png' />
                <Form
                  form={form}
                  layout='vertical'
                  requiredMark={false}
                  onFinish={onFinish}
                >
                  <Form.Item label='用户名' name='Name' rules={[{ required: true, whitespace: true, message: '请输入账号!' }]}>
                    <Input
                      className={style.inputSty}
                      prefix={<CustomIcon type='icon-gerenzhongxin' style={{ color: '#656464', fontSize: 12 }} />}
                      placeholder='请输入账号'
                      allowClear
                    />
                  </Form.Item>
                  <Form.Item
                    label='密码'
                    name='Pwd'
                    rules={[
                      { required: true, whitespace: true, message: '请输入密码!' },
                      { len: 6, message: '密码格式不正确' },
                    ]}
                    style={{ marginBottom: 8 }}
                  >
                    <Input.Password
                      className={style.inputSty}
                      prefix={<LockOutlined style={{ color: '#656464', fontSize: 16 }} />}
                      iconRender={(visible) => (
                        visible ? <EyeInvisibleOutlined style={{ color: 'rgb(0,0,0,0.45)' }} /> : <EyeOutlined style={{ color: 'rgb(0,0,0,0.45)' }} />
                      )}
                      type='password'
                      placeholder='请输入密码'
                      visibilityToggle={true}
                      allowClear
                    />
                  </Form.Item>
                  <Flex justify='space-between' align='center' style={{ fontSize: 12 }}>
                    <Checkbox style={{ color: '#656464', fontSize: 12 }} checked={isSave} onChange={() => {
                      setIsSave(!isSave);
                      storeUtil.set('isSave', !isSave ? '1' : '');
                    }}>
                      记住账号
                    </Checkbox>
                    {!correct && (
                      <span style={{ color: 'red', fontWeight: 'bold', position: 'relative', right: -80 }}>
                        *账号或密码输入错误
                      </span>
                    )}
                    <div>
                      {isForget ? (
                        <a style={{ color: '#D92E2D' }} onClick={() => setIsForget(false)}>
                          请联系管理员
                        </a>
                      ) : (
                        <a style={{ color: '#656464' }} onClick={() => setIsForget(true)}>
                          忘记密码
                        </a>
                      )}
                    </div>
                  </Flex>
                  <Button type='primary' className={style.button} htmlType='submit'>
                    登录
                  </Button>
                </Form>
              </Col>
            </Row>
          </Col>
          <Col span='24'>
            <div className={style.copyright}>Copyright {dayjs().year()} RAYSOLUTION ©</div>
          </Col>
        </Row>
      </div>
    </ConfigProvider>
  );
};

const mapStateToProps = ({ user }: { user: LoginType }) => {
  return {
    data: user.data,
    correct: user.correct,
  };
};

export default connect(mapStateToProps)(Login);
