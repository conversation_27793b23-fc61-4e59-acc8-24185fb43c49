import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Tabs, Table, Checkbox, Button, message, Modal, Flex } from 'antd';
import type { TabsProps } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { connect, Dispatch } from 'umi';
import ScreenshotGallery, { ScreenshotGalleryRef } from './ScreenshotGallery';
import styles from './index.less';
import { ViewType } from '@/models/views';

// 定义序列数据类型
interface SeriesTableData {
  key: string;
  sequenceId: string;
  sequenceDesc: string;
  imageCount: string;
  seriesId: string; // 添加seriesId字段用于切换
}

interface RightInformationProps {
  dispatch: Dispatch;
  views: ViewType;
}

function RightInformation({ dispatch, views }: RightInformationProps) {
  console.log('RightInformation - 渲染', views);
  // 截图状态管理
  const [screenshots, setScreenshots] = useState<string[]>([]);
  const screenshotGalleryRef = useRef<ScreenshotGalleryRef>(null);

  // 标签页状态
  const [activeTab, setActiveTab] = useState('current');

  // 拖拽状态管理
  const [isDragging, setIsDragging] = useState(false);
  const [dragginRowKey, setDragginRowKey] = useState<string | null>(null);

  // 更新本地截图状态的函数
  const updateScreenshotState = useCallback(() => {
    if (views.screenshots) {
      setScreenshots([...views.screenshots]); // 创建新数组以确保状态更新
    } else {
      setScreenshots([]);
    }
  }, [views.screenshots]);

  // 监听截图更新
  useEffect(() => {
    // 更新本地状态
    updateScreenshotState();

    // 监听新的截图事件
    const handleNewScreenshot = (event: CustomEvent) => {
      const { imageUrl } = event.detail;

      setScreenshots((prev) => [...prev, imageUrl]);
    };

    window.addEventListener('newScreenshot', handleNewScreenshot as EventListener);

    return () => {
      window.removeEventListener('newScreenshot', handleNewScreenshot as EventListener);
    };
  }, [views.screenshots, updateScreenshotState]);

  // 加载历史截图
  useEffect(() => {
    // 只有在有studyId且没有截图时才加载历史截图
    // 避免在删除操作期间或之后自动重新加载
    if (views.studyId && (!views.screenshots || views.screenshots.length === 0) && !views.isDeletingScreenshots) {
      // 检查是否是首次加载（而不是删除后的状态）
      // 使用sessionStorage记录删除操作
      const hasDeletedScreenshots = sessionStorage.getItem(`deleted_screenshots_${views.studyId}`);

      if (!hasDeletedScreenshots) {
        // 添加延迟，避免在删除操作期间触发
        const timer = setTimeout(() => {
          dispatch({
            type: 'views/fetchHistoryScreenshots',
            payload: { studyId: views.studyId },
          });
        }, 1000); // 延迟1秒，给删除操作足够时间完成

        return () => clearTimeout(timer);
      }
    }
  }, [views.studyId, views.screenshots, views.isDeletingScreenshots, dispatch]);

  // 监听views.screenshots变化，同步到本地状态
  useEffect(() => {
    if (views.screenshots !== undefined) {
      updateScreenshotState();
    }
  }, [views.screenshots, updateScreenshotState]);

  // 将seriesLists转换为表格数据格式
  const seriesTableData: SeriesTableData[] = React.useMemo(() => {
    if (!views.seriesLists || views.seriesLists.length === 0) {
      return [];
    }
    let newArr = views.seriesLists.filter(item => item.SeriesDescription !== 'injector')
    return newArr.map((series: any, index: number) => {
      // 获取对应的instances数量
      let instanceCount = 0;

      // 确保 curInstances 是 Map 对象
      if (views.curInstances && typeof views.curInstances.get === 'function') {
        const instances = views.curInstances.get(series.SeriesId);
        instanceCount = instances?.length || 0;
      }

      return {
        key: series.SeriesId,
        sequenceId: series.SeriesNumber || `${index + 1}`,
        sequenceDesc: series.SeriesDescription || series.ProtocolName || '未知序列',
        imageCount: instanceCount.toString(),
        seriesId: series.SeriesId,
      };
    });
  }, [views.seriesLists, views.curInstances]);

  // 处理序列点击事件 - 注释掉，改为拖拽功能
  // const handleSeriesClick = (record: SeriesTableData) => {
  //   console.log('点击序列:', record);
  //   dispatch({
  //     type: 'views/selectSeries',
  //     payload: { seriesId: record.seriesId },
  //   });
  // };

  // 处理序列拖拽开始
  const handleDragStart = (e: React.DragEvent, record: SeriesTableData) => {
    e.dataTransfer.setData(
      'application/json',
      JSON.stringify({
        type: 'series',
        seriesId: record.seriesId,
        seriesData: record,
      })
    );
    e.dataTransfer.effectAllowed = 'copy';
    setIsDragging(true);
    setDragginRowKey(record.key);
  };

  // 处理拖拽结束
  const handleDragEnd = (e: React.DragEvent, record: SeriesTableData) => {
    setIsDragging(false);
    setDragginRowKey(null);
  };

  // 序列表格列定义
  const columns: ColumnsType<SeriesTableData> = [
    {
      title: '序列号',
      dataIndex: 'sequenceId',
      key: 'sequenceId',
      width: 80,
      align: 'center',
    },
    {
      title: '序列描述',
      dataIndex: 'sequenceDesc',
      key: 'sequenceDesc',
      align: 'center',
    },
    {
      title: '图像数',
      dataIndex: 'imageCount',
      key: 'imageCount',
      width: 80,
      align: 'center',
    },
  ];

  // 标签页配置
  const tabItems: TabsProps['items'] = [
    {
      key: 'current',
      label: '当前检查',
      children: (
        <div >
          <Table
            columns={columns}
            dataSource={seriesTableData}
            pagination={false}
            size='small'
            scroll={{ y: 200 }}
            rowClassName={(record) => {
              // 高亮显示当前选中的序列
              const isSelected = record.seriesId === views.curSeriesId;
              const isDraggingThis = isDragging && dragginRowKey === record.key;
              let className = styles.tableRow;
              if (isSelected) className += ` ${styles.selectedRow}`;
              if (isDraggingThis) className += ` ${styles.draggingRow}`;
              return className;
            }}
            bordered={false}
            onRow={(record) => ({
              draggable: true,
              onDragStart: (e) => handleDragStart(e, record),
              onDragEnd: (e) => handleDragEnd(e, record),
              style: { cursor: 'grab' },
            })}
          />
        </div>
      ),
    },
    {
      key: 'history',
      label: '历史检查',
      children: (
        <div >
          <div style={{ textAlign: 'center', color: '#888', marginTop: '20px', fontSize: '14px' }}>暂无历史检查</div>
        </div>
      ),
    },
  ];

  // 处理插入截图的回调
  const handleInsertImages = (imageUrls: string[]) => {
    // 如果有图片，则预览第一张图片
    if (imageUrls && imageUrls.length > 0 && screenshotGalleryRef.current) {
      // 获取第一张选中图片的索引，如果没有选中则使用第一张图片
      const selectedIndices = screenshotGalleryRef.current.getSelectedImages();
      const indexToShow = selectedIndices.length > 0 ? selectedIndices[0] : 0;

      // 调用ScreenshotGallery组件的预览方法
      screenshotGalleryRef.current.previewImage(indexToShow);
    }
  };

  // 处理删除截图
  const handleDeleteScreenshots = (indices: number[]) => {
    if (!indices.length) return;

    // 显示确认对话框
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${indices.length} 张截图吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        // 设置删除标记，防止自动重新加载截图
        if (views.studyId) {
          sessionStorage.setItem(`deleted_screenshots_${views.studyId}`, 'true');
        }

        // 使用批量删除API，让model处理所有删除逻辑
        dispatch({
          type: 'views/batchDeleteScreenshots',
          payload: { indices },
        });
      },
    });
  };

  // 处理删除选中的图片
  const handleDeleteSelected = () => {
    // 获取当前选中的图片索引
    const selectedIndices = screenshotGalleryRef.current?.getSelectedImages() || [];

    if (selectedIndices.length > 0) {
      // 显示确认对话框
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除选中的 ${selectedIndices.length} 张截图吗？`,
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          // 用户确认后，清除选择状态并删除截图
          screenshotGalleryRef.current?.clearSelection();

          // 设置删除标记，防止自动重新加载截图
          if (views.studyId) {
            sessionStorage.setItem(`deleted_screenshots_${views.studyId}`, 'true');
          }

          // 直接调用dispatch，不再调用handleDeleteScreenshots避免二次确认
          dispatch({
            type: 'views/batchDeleteScreenshots',
            payload: { indices: selectedIndices },
          });
        },
      });
    } else {
      // 如果没有选中图片，显示提示
      message.info('请先选择要删除的图片');
    }
  };

  // 跟踪选中的图片数量
  const [selectedCount, setSelectedCount] = useState(0);

  // 更新选中图片数量的回调函数
  const updateSelectedCount = (count: number) => {
    setSelectedCount(count);
  };

  return (
    <div className={styles.rightPart}>
      {/* 患者信息区域 */}
      <div className={styles.patientInfo}>
        <div className={styles.patientName}>
          {(views.InstanceTag && views.InstanceTag['(0010,0010)']?.Value?.[0]) || '未知患者'}
        </div>
        <div className={styles.patientId}>{views.studyId || '无检查号'}</div>
      </div>

      {/* 检查信息标签页 */}
      <Tabs items={tabItems} activeKey={activeTab} onChange={setActiveTab} type='card' className={styles.tabs} />

      {/* 截图库区域 */}
      <div className={styles.screenshotSection}>
        <div className={styles.sectionTitle}>截图库 ({screenshots.length})</div>
        <div className={styles.screenshotGalleryWrapper}>
          <ScreenshotGallery
            ref={screenshotGalleryRef}
            imgs={screenshots}
            onInsertImages={handleInsertImages}
            onDeleteImages={handleDeleteScreenshots}
            onSelectionChange={updateSelectedCount}
            screenshotInfos={views.screenshotInfos}
          />
        </div>
        {/* 操作按钮区域 - 移到页面最底部 */}
        <Flex justify='end'>
          <Button
            size='small'
            type='primary'
            className={styles.actionButton}
            onClick={() => handleInsertImages(screenshots)}
            disabled={screenshots.length === 0 || selectedCount === 0}
            style={
              screenshots.length === 0 || selectedCount === 0
                ? {
                    backgroundColor: 'rgba(0, 0, 0, 0.04)',
                    borderColor: 'rgb(126, 126, 127)',
                    color: 'rgb(126, 126, 127)',
                    textShadow: 'none',
                    boxShadow: 'none',
                    cursor: 'not-allowed',
                  }
                : undefined
            }
          >
            查看
          </Button>
          <Button
            size='small'
            type='primary'
            danger
            className={styles.actionButton}
            onClick={handleDeleteSelected}
            disabled={screenshots.length === 0 || selectedCount === 0}
            style={
              screenshots.length === 0 || selectedCount === 0
                ? {
                    backgroundColor: 'rgba(0, 0, 0, 0.04)',
                    borderColor: 'rgb(126, 126, 127)',
                    color: 'rgb(126, 126, 127)',
                    textShadow: 'none',
                    boxShadow: 'none',
                    cursor: 'not-allowed',
                  }
                : undefined
            }
          >
            删除{selectedCount > 0 ? `(${selectedCount})` : ''}
          </Button>
        </Flex>
      </div>
    </div>
  );
}

const mapStateToProps = ({ views }: { views: ViewType }) => {
  return {
    views,
  };
};

export default connect(mapStateToProps)(RightInformation);
