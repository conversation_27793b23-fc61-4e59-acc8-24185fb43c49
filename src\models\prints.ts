import { stringify } from 'querystring';
import { getPatient, listStudy, listSeries, listInstance, getSeries, getDicomFile } from '@/services/prints';
import { Effect, Reducer } from 'umi';
import { VERSION } from '@/utils/consts';
import { IMouseLeftToolType } from '@/utils/enums';

export interface PrintType {
  filmNum: number;
  // selectedSeries: any; // SeriesType
  curInstances: Map<string, any[]>; // InstancesType[]
  // curDicomFiles: Map<string, any[]>;
  studyList: any[]; // StudyType[]
  partSeries?: any[]; // SeriesType[]
  Offset: number;
  Limit: number;
  selectedFilmId: number | number[] | null;
  selectedDicomId: string | null;
  filmsConfig?: {
    seriesLayout?: number; // 11代表1×1
    seriesList?: any[]; // SeriesType[]
    printSpaceList?: number[]; // 打印间隔
    dicomsLayoutList?: number[]; // 同上
    isInverseList?: boolean[]; // 是否反序展示
  }[];
  mouseLeftTool: IMouseLeftToolType;
  patientName: string;
}

export interface ModelType {
  state: PrintType;
  effects: {
    getCurrentPatient: Effect;
    getStudys: Effect;
    getSeriesByStudyId: Effect;
    getInstancesBySeriesId: Effect;
    saveSelectSeries: Effect;
    // getDicomFile: Effect;
  };
  reducers: {
    stateChangeHandler: Reducer;
    selectedFilmStateHandler: Reducer;
    addInstanceMap: Reducer;
    // addDicomMap: Reducer;
  };
}

const Model: ModelType = {
  state: {
    filmNum: 1, // 当前展示区胶片数量，最多为4
    studyList: [], // 序列列表study项列表
    curInstances: new Map(), // 选中series后当前的Instance列表
    // curDicomFiles: new Map(),
    partSeries: [], // 用户展开特定study节点时，根据后台返回的系列数据动态构建该研究下的子节点数据
    selectedFilmId: 1, // 当前选中胶片区域id，目前为1|2|3|4或包含1|2|3|4的数组
    selectedDicomId: null, // 当前选中Dicom图像的区域id,film-series-dicom
    filmsConfig: [{}, {}, {}, {}], // 对于每一张胶片的内容数据
    Offset: 1,
    Limit: -1,
    mouseLeftTool: IMouseLeftToolType.PanTool,
    patientName: '',
  },

  effects: {
    *getCurrentPatient({ payload }, { call, put }): any {
      const data = yield call(getPatient, payload);
      // console.log('getCurrentPatient', data);
    },
    *getStudys({ payload }, { call, put, select }): any {
      const curState = yield select((state: any) => state.prints);
      const {
        Data: { List, Name, Total },
      } = yield call(listStudy, {
        ...payload,
        Offset: curState.Offset,
        Limit: curState.Limit,
      });
      yield put({
        type: 'stateChangeHandler',
        payload: {
          studyList: List,
          patientName: Name,
        },
      });
    },
    *getSeriesByStudyId({ payload }, { call, put, select }): any {
      const curState = yield select((state: any) => state.prints);
      const {
        Data: { List, Total },
      } = yield call(listSeries, {
        ...payload,
        Offset: curState.Offset,
        Limit: curState.Limit,
      });
      yield put({
        type: 'stateChangeHandler',
        payload: {
          partSeries: List,
        },
      });
    },
    *getInstancesBySeriesId({ payload }, { call, put, select }): any {
      const curState = yield select((state: any) => state.prints);
      const {
        Data: { List, Total },
      } = yield call(listInstance, {
        ...payload,
        Offset: curState.Offset,
        Limit: curState.Limit,
      });
      yield put({
        type: 'addInstanceMap',
        payload: {
          seriesId: payload.SeriesId,
          curInstances: List,
        },
      });
    },
    *saveSelectSeries({ payload }, { call, put, select }): any {
      // 获取当前选中的Series实例
      const {
        Data: { Series },
      } = yield call(getSeries, { ...payload });

      // 更新对应的filmConfig中的seriesList
      const curState = yield select((state: any) => state.prints);
      if (!curState.selectedDicomId) {
        // 选中Dicom为null，为第一次选择序列
        yield put({
          type: 'selectedFilmStateHandler',
          payload: {
            seriesList: [Series],
          },
        });
        yield put({
          type: 'stateChangeHandler',
          payload: {
            selectedDicomId: '1-0-0',
          },
        });
      } else {
        const [filmId, seriesIndex, _] = curState.selectedDicomId.split('-');
        const updatedList: any[] = [...curState.filmsConfig[filmId - 1].seriesList];
        if (updatedList[seriesIndex] || updatedList[seriesIndex] === undefined) {
          updatedList.splice(seriesIndex, 1, Series);
        } else {
          updatedList.push(Series);
        }
        yield put({
          type: 'selectedFilmStateHandler',
          payload: {
            seriesList: updatedList,
          },
        });
      }

      // 获取当前Series的Instence
      const {
        Data: { List, Total },
      } = yield call(listInstance, {
        Version: VERSION,
        SeriesId: Series.Id,
        Offset: curState.Offset,
        Limit: curState.Limit,
      });
      yield put({
        type: 'addInstanceMap',
        payload: {
          seriesId: payload.SeriesId,
          curInstances: List,
        },
      });
    },
    // *getDicomFile({ payload }, { call, put }): any {
    //   const res = yield call(getDicomFile, { ...payload });
    //   // console.log('res', res);
    //   yield put({
    //     type: 'addDicomMap',
    //     payload: {
    //       seriesId: payload.InstanceId,
    //       curDicomFiles: res,
    //     },
    //   });
    // },
  },

  reducers: {
    stateChangeHandler(state, { payload }) {
      return { ...state, ...payload };
    },
    selectedFilmStateHandler(state, { payload }) {
      const nextState = { ...state };
      nextState.filmsConfig = [...state.filmsConfig];
      const oldTarget = nextState.filmsConfig[state.selectedFilmId - 1];
      nextState.filmsConfig[state.selectedFilmId - 1] = {
        ...oldTarget,
        ...payload,
      };
      return nextState;
    },
    addInstanceMap(state, { payload: { seriesId, curInstances } }) {
      const newInstances = new Map(state.curInstances);
      newInstances.set(seriesId, curInstances);
      return {
        ...state,
        curInstances: newInstances,
      };
    },
    // addDicomMap(state, { payload: { seriesId, curDicomFiles } }) {
    //   const newDicomFiles = new Map(state.curDicomFiles);
    //   newDicomFiles.set(seriesId, [
    //     ...(state.curDicomFiles.get(seriesId) || []),
    //     curDicomFiles,
    //   ]);
    //   console.log(newDicomFiles);
    //   return {
    //     ...state,
    //     newDicomFiles: newDicomFiles,
    //   };
    // },
  },
};
export default Model;
