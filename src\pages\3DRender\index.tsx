import React, { Dispatch, useEffect, useRef, useState } from 'react';
import { initCornerstone } from '@/components/prints/cornerstone';
import { useLocation, connect } from 'umi';
import type { PatientType } from '@/models/patients';
import { Dicom3DRender } from './Dicom3DRender';
import { RenderingEngine } from '@cornerstonejs/core';
import { IRenderingEngine } from '@cornerstonejs/core/dist/types/types';
import { addTool, TrackballRotateTool } from '@cornerstonejs/tools';

interface IVolumeRendering3DProps {
  dispatch: Dispatch<any>;
  cornerstoneInited: boolean;
}

const VolumeRendering3D: React.FC<IVolumeRendering3DProps> = (props) => {
  const { cornerstoneInited, dispatch } = props;
  // const { pathname } = useLocation();
  const [renderingEngine, setRenderingEngine] = useState<IRenderingEngine | null>(null);

  useEffect(() => {
    // 初始3D渲染部分所需的ToolGroups
    if (!localStorage.getItem('3DrenderInited')) {
      addTool(TrackballRotateTool);
      localStorage.setItem('3DrenderInited', 'true');
    }
  }, []);

  useEffect(() => {
    if (cornerstoneInited) {
      const renderingEngine: IRenderingEngine = new RenderingEngine('3DRenderEngine');
      setRenderingEngine(renderingEngine);
      return;
    }
    const init = async () => {
      await initCornerstone();
      dispatch({
        type: 'patients/save',
        payload: {
          cornerstoneInited: true,
        },
      });
      const renderingEngine: IRenderingEngine = new RenderingEngine('3DRenderEngine');
      setRenderingEngine(renderingEngine);
    };
    init();
  }, []);

  return <Dicom3DRender renderingEngine={renderingEngine} />;
};

const mapStateToProps = ({ patients }: { patients: PatientType }) => {
  const { cornerstoneInited } = patients;
  return {
    cornerstoneInited,
  };
};

export default connect(mapStateToProps)(VolumeRendering3D);
