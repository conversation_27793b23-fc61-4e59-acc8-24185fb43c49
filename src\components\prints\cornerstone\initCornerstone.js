import { Enums as csEnums, init as csRenderInit, setUseSharedArrayBuffer } from '@cornerstonejs/core';
import { init as csToolsInit } from '@cornerstonejs/tools';
import initCornerstoneImageLoader from './initCornerstoneImageLoader';
import initVolumeLoader from './initVolumeLoader';
import initProviders from './initProviders';

export default async function initCornerstone() {
  // 初始化 - 元数据提供者
  initProviders();

  // 初始化 - Dicom文件加载器,即imageLoader
  initCornerstoneImageLoader();

  // 初始化 - 3D体积加载器
  initVolumeLoader();

  // 初始化 - CornerStone
  await csRenderInit({
    gpuTier: true,
  });

  // 初始化 - CornerStone/tool
  await csToolsInit();

  // 配置项 - 是否使用SharedArray
  setUseSharedArrayBuffer(csEnums.SharedArrayBufferModes.AUTO);
}
