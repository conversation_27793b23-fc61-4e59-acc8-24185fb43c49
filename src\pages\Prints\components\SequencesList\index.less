.sequencesList {
  height: 100%;
  border-radius: 0;
  border: 1px solid gray;
  background-color: rgb(64, 64, 64);
  // overflow-y: auto;
}

// .siderItemTitle {
//   width: 100%;
//   border-bottom: rgba(128, 128, 128, 0.5) 1px solid;
//   line-height: 35px;
//   font-size: 18px;
//   color: white;
//   padding: 0 10px;
// }

.treeContainer {
  :global {
    .ant-tree {
      background-color: #404040;
      color: white;
      font-size: 16px;
    }

    .ant-tree-show-line .ant-tree-switcher {
      background-color: #404040;
    }

    .ant-tree-title:hover {
      color: white;
    }

    .ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
      background-color: #747d93;
    }
  }
  height: 100%;
  overflow-y: scroll;
}
