import {
  // FetchReports,
  // SaveReport,
  // SaveTemplate,
  // FetchTemplates,
  // PublishReport,
  // FetchImages,
  // FetchImage,
  // CheckReport,
  // CancelCheck,
  // FetchVideo,
  ListTemplateType,
  ListTemplateByType,
  getTemplateContent,
  getReport,
  listImage,
  getImage,
  saveReport,
  submitReport,
  listGroupTemplateType,
  listGroupTemplateByType,
  getGroupTemplateContent,
  updateImageUseStatus,
  deleteImage,
} from '@/services/reports';
import { Effect, Reducer } from 'umi';
import { all } from 'redux-saga/effects';
import storeUtil from '@/utils/store';
import { ApiTemplateItem, TemplateContent } from '@/pages/reports/data';

export interface ReportType {
  studyId?: any;
  patientId?: any;
  radioFind?: any;
  cliniDiag?: any;
  reportLoading?: boolean;
  patientInfo?: any;
  isRepoSave?: boolean;
  templates?: any[]; // 模板列表，包含从后端获取的所有模板信息
  // templates: Id、Create_time、Update_time、Status
  // RadiographicFindingTemplate、ClinicalDiagnosisTemplate
  tempSaveMsg?: string;
  pubRepoMsg?: string;
  submitRepoMsg?: string;
  imgNames?: any;
  imgs?: { name: string; url: string }[];
  status?: number; // 报告的状态
  checkMsg?: string;
  cancelCheckMsg?: string;
  video?: any; // 视频
  templateTypes?: any[];
  templatesInType?: Record<string, ApiTemplateItem[]>;
  selectedTemplateContent?: TemplateContent | null;
  reportId?: number;
  // 团队模板相关状态
  groupTemplateTypes?: any[];
  groupTemplatesInType?: Record<string, ApiTemplateItem[]>;
  selectedGroupTemplateContent?: TemplateContent | null;
}

export interface ModelType {
  state: ReportType;
  effects: {
    getReport: Effect;
    listImages: Effect;
    listTemplateType: Effect;
    listTemplateByType: Effect;
    getTemplateContent: Effect;
    saveReport: Effect;
    submitReport: Effect;
    listGroupTemplateType: Effect;
    listGroupTemplateByType: Effect;
    getGroupTemplateContent: Effect;
    updateImageUseStatus: Effect;
    deleteImage: Effect;
  };
  reducers: {
    save: Reducer<ReportType>;
  };
}

const Model: ModelType = {
  state: {
    studyId: undefined,
    patientId: undefined,
    radioFind: '',
    cliniDiag: '',
    reportLoading: false,
    patientInfo: undefined,
    isRepoSave: false,
    templates: [],
    tempSaveMsg: '',
    pubRepoMsg: '',
    submitRepoMsg: '',
    status: 0,
    checkMsg: '',
    cancelCheckMsg: '',
    templateTypes: [],
    templatesInType: {},
    selectedTemplateContent: null,
    imgs: [],
    reportId: undefined,
    // 团队模板初始状态
    groupTemplateTypes: [],
    groupTemplatesInType: {},
    selectedGroupTemplateContent: null,
  },
  effects: {
    *getReport({ payload }, { call, put }): any {
      yield put({
        type: 'save',
        payload: {
          reportLoading: true,
        },
      });

      const responseData = yield call(getReport, payload);

      if (responseData) {
        yield put({
          type: 'save',
          payload: {
            reportId: responseData.Id,
            patientInfo: responseData,
            radioFind: responseData.RadiographicFinding || '',
            cliniDiag: responseData.ClinicalDiagnosis || '',
            status: responseData.StatusOfReport,
            reportLoading: false,
          },
        });
      }
    },
    // *fetchReports({ payload }, { call, put }): any {
    //   yield put({
    //     type: 'save',
    //     payload: {
    //       reportLoading: true,
    //       studyId: payload.StudyId, // 从patient页中获取当前选择的患者信息
    //       patientId: payload.PatientId,
    //     },
    //   });
    //   const data = yield call(FetchReports, payload);
    //   if (data) {
    //     // 获取报告页信息后存储当前sid与pid
    //     storeUtil.set('sid', payload.StudyId);
    //     storeUtil.set('pid', payload.PatientId);
    //     yield put({
    //       type: 'save',
    //       payload: {
    //         radioFind: data.Data.Report.RadiographicFinding,
    //         cliniDiag: data.Data.Report.ClinicalDiagnosis,
    //         status: data.Data.Report.StatusOfPatient,
    //         patientInfo: data.Data.PatientInfoInReport,
    //         reportLoading: false,
    //       },
    //     });
    //   }
    // },
    // *saveReport({ payload }, { call, put }): any {
    //   yield put({
    //     type: 'save',
    //     payload: {
    //       radioFind: payload.RadiographicFinding,
    //       cliniDiag: payload.ClinicalDiagnosis,
    //     },
    //   });
    //   const data = yield call(SaveReport, payload);
    //   if (data) {
    //     // console.log(data.Data.Message);
    //     yield put({
    //       type: 'save',
    //       payload: {
    //         isRepoSave: true,
    //       },
    //     });
    //   }
    // },
    // *saveTemplate({ payload }, { call, put }): any {
    //   const data = yield call(SaveTemplate, payload);
    //   if (data) {
    //     yield put({
    //       type: 'save',
    //       payload: {
    //         tempSaveMsg: data.Data.Message,
    //       },
    //     });
    //   }
    // },
    // *fetchTemplates({ payload }, { call, put }): any {
    //   const data = yield call(FetchTemplates, payload);
    //   if (data) {
    //     yield put({
    //       type: 'save',
    //       payload: {
    //         templates: data.Data.Templates,
    //       },
    //     });
    //   }
    // },
    // *publishReport({ payload }, { call, put, select }): any {
    //   const data = yield call(PublishReport, payload);
    //   if (data) {
    //     yield put({
    //       type: 'save',
    //       payload: {
    //         pubRepoMsg: data.Data.Message,
    //       },
    //     });
    //   }
    //   // 更新数据
    //   yield put({
    //     type: 'fetchReports',
    //     payload: {
    //       Version: 20230101,
    //       StudyId: storeUtil.get('sid').value,
    //       PatientId: storeUtil.get('pid').value,
    //     },
    //   });
    // },
    // *fetchImages({ payload }, { call, put }): any {
    //   const data = yield call(FetchImages, payload);
    //   if (data) {
    //     const imgNames = data.Data?.ImageNames ?? [];
    //     const imgs = [];
    //     for (let name of imgNames) {
    //       const img = yield call(FetchImage, { ImageName: name, ...payload });
    //       if (img) {
    //         imgs.push(window.URL.createObjectURL(new Blob([img], { type: 'image/png' })));
    //       }
    //     }
    //     yield put({
    //       type: 'save',
    //       payload: {
    //         imgNames: imgNames,
    //         imgs: imgs,
    //       },
    //     });
    //   }
    // },
    // *checkReport({ payload }, { call, put }): any {
    //   const data = yield call(CheckReport, payload);
    //   if (data) {
    //     yield put({
    //       type: 'save',
    //       payload: {
    //         checkMsg: data.Data.Message,
    //       },
    //     });
    //   }
    //   // 更新数据
    //   yield put({
    //     type: 'fetchReports',
    //     payload: {
    //       Version: 20230101,
    //       StudyId: storeUtil.get('sid').value,
    //       PatientId: storeUtil.get('pid').value,
    //     },
    //   });
    // },
    // *cancelCheck({ payload }, { call, put }): any {
    //   const data = yield call(CancelCheck, payload);
    //   if (data) {
    //     yield put({
    //       type: 'save',
    //       payload: {
    //         cancelCheckMsg: data.Data.Message,
    //       },
    //     });
    //   }
    //   // 更新数据
    //   yield put({
    //     type: 'fetchReports',
    //     payload: {
    //       Version: 20230101,
    //       StudyId: storeUtil.get('sid').value,
    //       PatientId: storeUtil.get('pid').value,
    //     },
    //   });
    // },
    // *fetchVideo({ payload }, { call, put }): any {
    //   const data = yield call(FetchVideo, payload);
    //   if (data) {
    //     console.log('data', data);
    //     yield put({
    //       type: 'save',
    //       payload: {
    //         video: window.URL.createObjectURL(new Blob([data], { type: 'video/mp4' })),
    //       },
    //     });
    //   }
    // },
    *listTemplateType({ payload }, { call, put }): any {
      const data = yield call(ListTemplateType, payload);
      if (data && data.TemplateTypeList) {
        const types = data.TemplateTypeList;
        yield put({
          type: 'save',
          payload: {
            templateTypes: types,
          },
        });
        if (types.length > 0) {
          yield put({
            type: 'listTemplateByType',
            payload: { TypeId: types[0].Id },
          });
        }
      }
    },
    *listTemplateByType({ payload }, { call, put, select }): any {
      const { templatesInType } = yield select((state: any) => state.reports);
      // 确保 templatesInType 存在且指定 TypeId 的数据不存在时才调用接口
      if (templatesInType && templatesInType[payload.TypeId]) {
        return;
      }
      const res = yield call(ListTemplateByType, {
        TypeId: payload.TypeId,
        Offset: -1,
        Limit: 100,
      });
      if (res && res.TemplateList) {
        yield put({
          type: 'save',
          payload: {
            templatesInType: {
              ...(templatesInType || {}),
              [payload.TypeId]: res.TemplateList,
            },
          },
        });
      }
    },
    *getTemplateContent({ payload }, { call, put }): any {
      const res = yield call(getTemplateContent, payload);
      const templateData = res?.data || res?.Template || res;

      if (templateData) {
        const finding = templateData.RadiographicFindingTemplate || templateData.RadioFind || '';
        const conclusion = templateData.ClinicalDiagnosisTemplate || templateData.CliniDiag || '';
        yield put({
          type: 'save',
          payload: {
            selectedTemplateContent: {
              RadiographicFindingTemplate: finding,
              ClinicalDiagnosisTemplate: conclusion,
            },
          },
        });
      } else {
        yield put({
          type: 'save',
          payload: {
            selectedTemplateContent: null,
          },
        });
      }
    },
    *listImages({ payload }, { call, put, all }): any {
      try {
        // 第一步：获取图片路径列表
        console.log('开始获取图片路径列表，payload:', payload);
        const res = yield call(listImage, payload);
        console.log('获取图片路径列表结果:', res);

        if (res && (res.Paths || res.Data?.Paths)) {
          const imagePaths = res.Paths || res.Data.Paths;
          console.log('图片路径列表:', imagePaths);

          // 第二步：逐个获取图片数据
          const imageCalls = imagePaths.map((imageName: string) =>
            call(getImage, { StudyId: payload.StudyId, ImageName: imageName })
          );

          console.log('开始并行获取图片数据，数量:', imageCalls.length);
          const imageBlobs = yield all(imageCalls);
          console.log('获取图片数据结果:', imageBlobs);

          // 第三步：将blob转换为可显示的URL，并保留图片名
          const imageObjs = imageBlobs.map((blob: Blob, index: number) => {
            if (blob && blob instanceof Blob) {
              const url = window.URL.createObjectURL(blob);
              return { name: imagePaths[index], url };
            }
            console.log(`第${index + 1}张图片处理失败`);
            return null;
          }).filter(Boolean);

          console.log('最终图片对象:', imageObjs);

          yield put({
            type: 'save',
            payload: { imgs: imageObjs },
          });
        } else {
          console.log('没有图片路径数据');
          // 如果没有图片，设置空数组
          yield put({
            type: 'save',
            payload: { imgs: [] },
          });
        }
      } catch (error) {
        console.error('获取图片失败:', error);
        yield put({
          type: 'save',
          payload: { imgs: [] },
        });
      }
    },
    *saveReport({ payload }, { call, put, select }): any {
      const { reportId } = yield select((state: any) => state.reports);
      try {
        yield call(saveReport, { ...payload, Id: reportId });
        yield put({ type: 'save', payload: { isRepoSave: true } });
      } catch (e) {
        console.error('save report error', e);
      }
    },
    *submitReport({ payload }, { call, put, select }): any {
      const { reportId } = yield select((state: any) => state.reports);
      try {
        const result = yield call(submitReport, { ...payload, Id: reportId });
        yield put({
          type: 'save',
          payload: {
            submitRepoMsg: '报告提交成功',
            status: 2 // 更新状态为已提交
          }
        });
      } catch (e) {
        console.error('submit report error', e);
      }
    },
    *listGroupTemplateType({ payload }, { call, put }): any {
      const data = yield call(listGroupTemplateType, payload);
      if (data && data.TemplateTypeList) {
        const types = data.TemplateTypeList;
        yield put({
          type: 'save',
          payload: {
            groupTemplateTypes: types,
          },
        });
        if (types.length > 0) {
          yield put({
            type: 'listGroupTemplateByType',
            payload: { TypeId: types[0].Id },
          });
        }
      }
    },
    *listGroupTemplateByType({ payload }, { call, put, select }): any {
      const { groupTemplatesInType } = yield select((state: any) => state.reports);
      // 确保 groupTemplatesInType 存在且指定 TypeId 的数据不存在时才调用接口
      if (groupTemplatesInType && groupTemplatesInType[payload.TypeId]) {
        return;
      }
      const res = yield call(listGroupTemplateByType, {
        TypeId: payload.TypeId,
        Offset: -1,
        Limit: 100,
      });
      if (res && res.TemplateList) {
        yield put({
          type: 'save',
          payload: {
            groupTemplatesInType: {
              ...(groupTemplatesInType || {}),
              [payload.TypeId]: res.TemplateList,
            },
          },
        });
      }
    },
    *getGroupTemplateContent({ payload }, { call, put }): any {
      const res = yield call(getGroupTemplateContent, payload);
      const templateData = res?.data || res?.Template || res;

      if (templateData) {
        const finding = templateData.RadiographicFindingTemplate || templateData.RadioFind || '';
        const conclusion = templateData.ClinicalDiagnosisTemplate || templateData.CliniDiag || '';
        yield put({
          type: 'save',
          payload: {
            selectedGroupTemplateContent: {
              RadiographicFindingTemplate: finding,
              ClinicalDiagnosisTemplate: conclusion,
            },
          },
        });
      } else {
        yield put({
          type: 'save',
          payload: {
            selectedGroupTemplateContent: null,
          },
        });
      }
    },
    *updateImageUseStatus({ payload }, { call }) {
      // payload: { Name, Use }
      try {
        yield call(updateImageUseStatus, payload);
      } catch (e) {
        console.error('updateImageUseStatus error', e);
      }
    },
    *deleteImage({ payload }, { call, put, select }) {
      // payload: { Name }
      try {
        yield call(deleteImage, payload);
        // 删除成功后刷新图片列表
        const { studyId } = yield select((state: any) => state.reports);
        if (studyId) {
          yield put({ type: 'listImages', payload: { StudyId: studyId } });
        }
      } catch (e) {
        console.error('deleteImage error', e);
      }
    },
  },
  reducers: {
    save(state, { payload }) {
      return { ...state, ...payload };
    },
  },
};

export default Model;
