import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Button, ConfigProvider, message } from 'antd';
import { connect, Dispatch } from 'umi';
import { ApiCategory } from '../data';
import { createGroupTemplateCategory } from '@/services/reports';

interface CreateCategoryModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  dispatch: Dispatch;
  editingCategory: ApiCategory | null;
  isGroupCategory?: boolean;
}

const CreateCategoryModal: React.FC<CreateCategoryModalProps> = ({
  visible,
  onClose,
  onSuccess,
  dispatch,
  editingCategory,
  isGroupCategory = false,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const isEditing = !!editingCategory;

  useEffect(() => {
    if (visible) {
      if (isEditing) {
        form.setFieldsValue({ name: editingCategory.Name });
      } else {
        form.resetFields();
      }
    }
  }, [visible, isEditing, editingCategory, form]);

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const commonSuccess = () => {
        setLoading(false);
        form.resetFields();
        onSuccess();
      };
      if (isEditing) {
        if (isGroupCategory) {
          dispatch({
            type: 'templateManagement/updateGroupCategory',
            payload: {
              id: editingCategory.Id,
              name: values.name?.trim(),
              onSuccess: commonSuccess,
              onFailure: () => setLoading(false),
            },
          });
        } else {
          dispatch({
            type: 'templateManagement/updateCategory',
            payload: {
              id: editingCategory.Id,
              name: values.name?.trim(),
              onSuccess: commonSuccess,
              onFailure: () => setLoading(false),
            },
          });
        }
      } else {
        if (isGroupCategory) {
          try {
            await createGroupTemplateCategory({ Name: values.name?.trim() });
            message.success('创建分类成功');
            commonSuccess();
          } catch (e) {
            // message.error('创建分类失败');
            setLoading(false);
          }
        } else {
          dispatch({
            type: 'templateManagement/createCategory',
            payload: {
              name: values.name?.trim(),
              onSuccess: commonSuccess,
              onFailure: () => setLoading(false),
            },
          });
        }
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <Modal
      title={isEditing ? '编辑模板分类' : '新建模板分类'}
      open={visible}
      onCancel={onClose}
      width={600}
      centered
      styles={{
        body: { padding: 0 },
        content: { padding: 0, borderRadius: '8px', overflow: 'hidden', backgroundColor: '#333233' },
        header: {
          padding: '16px 24px',
          margin: 0,
          borderBottom: '1px solid #424244',
          backgroundColor: '#4C4C4C',
        },
      }}
      destroyOnClose
      closeIcon={<img src="/icon/report/close.png" alt="close" style={{ width: 24, height: 24 }} />}
      footer={(
        <div style={{ padding: '12px 24px', textAlign: 'right' }}>
          <Button key="cancel" onClick={onClose} style={{ marginRight: 8 }}>取消</Button>
          <Button key="confirm" type="primary" loading={loading} onClick={handleSave} style={{ background: '#1F69B4' }}>保存</Button>
        </div>
      )}
    >
      <ConfigProvider
        theme={{
          components: {
            // Input: {
            //   colorBgContainer: '#333233',
            //   colorText: 'white',
            //   colorTextPlaceholder: 'rgba(255, 255, 255, 0.45)',
            //   colorBorder: '#555555',
            //   colorTextDescription: 'rgba(255, 255, 255, 0.65)',
            // },
          },
        }}
      >
        <div style={{ padding: '24px', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <Form form={form} layout="vertical" style={{ width: '100%' }}>
            <Form.Item
              name="name"
              label="模板分类名称"
              rules={[
                { required: true, message: '请输入分类名称' },
                {
                  validator: (_, value) => {
                    if (!value || value.trim() === '') {
                      return Promise.reject(new Error('分类名称不能为空或纯空格'));
                    }
                    return Promise.resolve();
                  }
                }
              ]}
            >
              <Input
                placeholder="请输入分类名称"
                maxLength={99}
                showCount={{
                  formatter: ({ count, maxLength }) => count <= 99 ? `${count}/99` : '',
                }}
              />
            </Form.Item>
          </Form>
        </div>
      </ConfigProvider>
    </Modal>
  );
};

export default connect()(CreateCategoryModal); 