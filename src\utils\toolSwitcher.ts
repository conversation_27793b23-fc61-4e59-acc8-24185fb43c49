// utils/toolSwitcher.ts
import { ToolGroupManager } from '@cornerstonejs/tools';
import * as cornerstoneTools from '@cornerstonejs/tools';
import type { Types } from '@cornerstonejs/tools';
import { getRenderingEngine } from '@cornerstonejs/core';
import { Dispatch } from 'umi';
import TextTool from './TextTool';
import { message } from 'antd';

// 在文件开头添加一个调试函数，暴露到全局
(window as any).debugToolSwitcher = () => {
  console.log('=== 工具切换器调试信息 ===');

  try {
    const allToolGroups = ToolGroupManager.getAllToolGroups();
    console.log(`找到 ${allToolGroups.length} 个工具组:`);

    allToolGroups.forEach((toolGroup, index) => {
      console.log(`\n工具组 ${index + 1}:`);
      console.log(`  ID: ${toolGroup.id}`);

      const viewports = toolGroup.getViewportsInfo();
      console.log(`  绑定的视口数量: ${viewports.length}`);

      if (viewports.length > 0) {
        console.log(
          '  视口详情:',
          viewports.map((vp) => ({
            viewportId: vp.viewportId,
            renderingEngineId: vp.renderingEngineId,
          }))
        );
      }

      // 检查常见工具是否存在
      const commonTools = [
        'Crosshairs',
        'Length',
        'Angle',
        'Probe',
        'RectangleROI',
        'EllipticalROI',
        'PlanarFreehandROI',
        'ArrowAnnotate',
        'Pan',
        'Zoom',
      ];

      const availableTools = commonTools.filter((toolName) => {
        try {
          return toolGroup.hasTool(toolName);
        } catch (e: any) {
          return false;
        }
      });

      console.log(`  可用工具: ${availableTools.join(', ') || '无'}`);

      // 尝试获取当前激活的工具
      try {
        const activeTool = toolGroup.getActivePrimaryMouseButtonTool();
        console.log(`  当前激活的工具: ${activeTool || '无'}`);
      } catch (e) {
        console.log(`  无法获取激活工具: ${(e as any)?.message || '未知错误'}`);
      }
    });
  } catch (error) {
    console.error('调试工具组时出错:', error);
  }

  console.log('\n=== 调试信息结束 ===');
};

// 添加安全检查，确保 cornerstone 库已正确加载
const isCornerstoneAvailable = () => {
  try {
    return (
      typeof ToolGroupManager !== 'undefined' &&
      typeof cornerstoneTools !== 'undefined' &&
      typeof getRenderingEngine !== 'undefined'
    );
  } catch (error) {
    console.error('Cornerstone 库未正确加载:', error);
    return false;
  }
};

const {
  LengthTool,
  AngleTool,
  ProbeTool,
  AnnotationTool,
  WindowLevelTool,
  StackScrollMouseWheelTool,
  StackScrollTool,
  PanTool,
  ZoomTool,
  TrackballRotateTool,
  CrosshairsTool,
  ArrowAnnotateTool,
  BidirectionalTool,
  RectangleROITool,
  CircleROITool,
  EllipticalROITool,
  PlanarFreehandROITool,
  EraserTool,
  Enums: csToolsEnums,
  annotation,
} = cornerstoneTools;

const { MouseBindings } = csToolsEnums;
const { selection } = annotation;

// 工具组前缀，用于查找所有序列的工具组
const toolGroupPrefix = 'TOOLGROUP_';

// 工具名称映射：左侧工具栏使用的简化名称 -> cornerstone实际工具名称
const toolNameMap: Record<string, string> = {
  Length: LengthTool.toolName,
  Angle: AngleTool.toolName,
  Probe: ProbeTool.toolName,
  RectangleROI: RectangleROITool.toolName,
  EllipticalROI: EllipticalROITool.toolName,
  PlanarFreehandROI: PlanarFreehandROITool.toolName,
  ArrowAnnotate: ArrowAnnotateTool.toolName,
  Crosshairs: CrosshairsTool.toolName,
  WindowLevel: WindowLevelTool.toolName,
  Pan: PanTool.toolName,
  Zoom: ZoomTool.toolName,
  Text: 'Text', // 特殊工具，保持原名
};

// 将简化名称转换为实际工具名称
const getActualToolName = (toolName: string): string => {
  return toolNameMap[toolName] || toolName;
};

// 创建全局的TextTool实例
const textToolInstance = new TextTool();

// 跟踪当前是否启用了Text工具
let isTextToolActive = false;

export const changeTool = (currentToolName: string, newToolName: string) => {
  console.log(`工具切换: ${currentToolName} -> ${newToolName}`);

  // 首先检查 cornerstone 库是否可用
  if (!isCornerstoneAvailable()) {
    console.error('无法切换工具: Cornerstone 库未正确加载');
    message.error('工具切换失败: 图像渲染引擎未正确初始化');
    return;
  }

  // 转换为实际的工具名称
  const actualCurrentTool = getActualToolName(currentToolName);
  const actualNewTool = getActualToolName(newToolName);

  console.log(`实际工具名称: ${actualCurrentTool} -> ${actualNewTool}`);

  // 获取所有活跃的工具组 - 优先查找序列级别的工具组
  const allToolGroups = ToolGroupManager.getAllToolGroups();
  let seriesToolGroups = allToolGroups.filter((toolGroup) => {
    // 过滤出有效的工具组（有绑定视口的）
    const viewports = toolGroup.getViewportsInfo();
    const isSeriesToolGroup = toolGroup.id.startsWith('toolGroup-'); // 查找序列级别的工具组
    return viewports.length > 0 && isSeriesToolGroup;
  });

  // 如果没找到序列级别的工具组，则查找所有有效的工具组
  if (seriesToolGroups.length === 0) {
    console.warn(`没有找到序列级别的工具组，查找所有活跃工具组`);
    seriesToolGroups = allToolGroups.filter((toolGroup) => {
      const viewports = toolGroup.getViewportsInfo();
      return viewports.length > 0;
    });
  }

  if (seriesToolGroups.length === 0) {
    console.warn(`没有找到活跃的工具组`);

    // 尝试查找特定前缀的工具组作为备选方案
    const toolGroupIds = ['TOOLGROUP_CT', 'TOOLGROUP_MR', 'TOOLGROUP_default', 'TOOLGROUP_0', 'TOOLGROUP_1'];
    for (const toolGroupId of toolGroupIds) {
      try {
        const toolGroup = ToolGroupManager.getToolGroup(toolGroupId);
        if (toolGroup) {
          const viewports = toolGroup.getViewportsInfo();
          if (viewports.length > 0) {
            seriesToolGroups.push(toolGroup);
            console.log(`找到备选工具组: ${toolGroupId}`);
          }
        }
      } catch (error) {
        // 忽略错误，继续查找
      }
    }

    if (seriesToolGroups.length === 0) {
      console.error('无法找到任何有效的工具组');
      message.error('工具切换失败: 没有找到有效的工具组');
      return;
    }
  }

  console.log(`找到 ${seriesToolGroups.length} 个活跃工具组`);

  // 对每个序列工具组执行工具切换
  seriesToolGroups.forEach((toolGroup, index) => {
    console.log(`处理工具组 ${index + 1}/${seriesToolGroups.length}: ${toolGroup.id}`);

    // 验证工具组是否有绑定的视口
    const viewports = toolGroup.getViewportsInfo();
    if (viewports.length === 0) {
      console.warn(`工具组 ${toolGroup.id} 没有绑定任何视口`);
      return;
    }

    console.log(`工具组 ${toolGroup.id} 当前绑定的视口数量: ${viewports.length}`);

    // 验证视口是否真正启用
    for (const viewport of viewports) {
      try {
        const renderingEngine = getRenderingEngine(viewport.renderingEngineId);
        if (!renderingEngine) {
          console.warn(`渲染引擎未找到: ${viewport.renderingEngineId}`);
          continue;
        }

        try {
          // 尝试获取视口并确保它已启用
          const vp = renderingEngine.getViewport(viewport.viewportId);
          if (!vp) {
            console.warn(`视口未找到: ${viewport.viewportId}`);
            continue;
          }

          // 确保视口已启用
          const element = vp.element;
          if (!element) {
            console.warn(`视口元素未找到: ${viewport.viewportId}`);
            continue;
          }

          console.log(`视口已验证: ${viewport.viewportId}`);
        } catch (e) {
          console.error(`验证视口时出错: ${viewport.viewportId}`, e);
        }
      } catch (e) {
        console.error(`验证渲染引擎时出错: ${viewport.renderingEngineId}`, e);
      }
    }

    // 验证所需的工具是否存在于工具组中
    const requiredTools = [actualCurrentTool, actualNewTool].filter(Boolean);
    for (const tool of requiredTools) {
      if (tool !== 'Text' && !toolGroup.hasTool(tool)) {
        console.warn(`工具 ${tool} 不存在于工具组 ${toolGroup.id} 中，尝试添加该工具`);
        try {
          // 尝试添加缺失的工具
          toolGroup.addTool(tool);
          console.log(`成功添加工具 ${tool} 到工具组 ${toolGroup.id}`);
        } catch (error) {
          console.error(`无法添加工具 ${tool} 到工具组 ${toolGroup.id}:`, error);
          return;
        }
      }
    }

    // 禁用当前工具
    if (actualCurrentTool && actualCurrentTool !== actualNewTool) {
      console.log(`禁用当前工具: ${actualCurrentTool}`);

      // 如果当前是Text工具，清理它并移除事件监听器
      if (currentToolName === 'Text') {
        textToolInstance.cleanup();
        removeTextToolEventListeners();
        isTextToolActive = false;
      }

      try {
        switch (actualCurrentTool) {
          case CrosshairsTool.toolName:
            toolGroup.setToolDisabled(actualCurrentTool);
            console.log(`成功禁用十字线工具`);
            break;
          case ArrowAnnotateTool.toolName:
            toolGroup.setToolPassive(actualCurrentTool);
            console.log(`成功设置箭头工具为被动`);
            break;
          case LengthTool.toolName:
          case ProbeTool.toolName:
          case BidirectionalTool.toolName:
          case AngleTool.toolName:
          case RectangleROITool.toolName:
          case CircleROITool.toolName:
          case EllipticalROITool.toolName:
          case PlanarFreehandROITool.toolName:
          case EraserTool.toolName:
            toolGroup.setToolPassive(actualCurrentTool);
            console.log(`成功设置工具 ${actualCurrentTool} 为被动`);
            break;
          case 'Text':
            // Text工具的特殊处理已在上面完成
            break;
          default:
            toolGroup.setToolPassive(actualCurrentTool);
            console.log(`成功设置工具 ${actualCurrentTool} 为被动`);
            break;
        }
      } catch (error) {
        console.error(`禁用工具 ${actualCurrentTool} 时出错:`, error);
        // 尝试强制设置为被动
        try {
          toolGroup.setToolPassive(actualCurrentTool);
          console.log(`强制设置工具 ${actualCurrentTool} 为被动成功`);
        } catch (retryError) {
          console.error(`强制设置工具 ${actualCurrentTool} 为被动也失败:`, retryError);
        }
      }
    }

    // 启用新工具
    if (actualNewTool) {
      console.log(`启用新工具: ${actualNewTool}`);

      if (newToolName === 'Text') {
        // Text工具激活时，禁用所有其他工具
        toolGroup.setToolDisabled(CrosshairsTool.toolName);
        toolGroup.setToolPassive(WindowLevelTool.toolName);
        toolGroup.setToolPassive(PanTool.toolName);
        toolGroup.setToolPassive(ZoomTool.toolName);
        toolGroup.setToolPassive(LengthTool.toolName);
        toolGroup.setToolPassive(AngleTool.toolName);
        toolGroup.setToolPassive(ProbeTool.toolName);
        toolGroup.setToolPassive(RectangleROITool.toolName);
        toolGroup.setToolPassive(CircleROITool.toolName);
        toolGroup.setToolPassive(EllipticalROITool.toolName);
        toolGroup.setToolPassive(PlanarFreehandROITool.toolName);
        toolGroup.setToolPassive(ArrowAnnotateTool.toolName);

        // Text工具的特殊处理 - 直接绑定事件
        setupTextTool();
        isTextToolActive = true;
      } else if (newToolName === 'Crosshairs') {
        // 如果要切换到十字线工具，确保Text工具被清理
        if (isTextToolActive) {
          textToolInstance.cleanup();
          removeTextToolEventListeners();
          isTextToolActive = false;
        }

        // 激活十字线工具，禁用其他标注工具
        toolGroup.setToolActive(CrosshairsTool.toolName, {
          bindings: [
            {
              mouseButton: MouseBindings.Primary,
            },
          ],
        });
        toolGroup.setToolPassive(LengthTool.toolName);
        toolGroup.setToolPassive(AngleTool.toolName);
        toolGroup.setToolPassive(ProbeTool.toolName);
        toolGroup.setToolPassive(RectangleROITool.toolName);
        toolGroup.setToolPassive(CircleROITool.toolName);
        toolGroup.setToolPassive(EllipticalROITool.toolName);
        toolGroup.setToolPassive(PlanarFreehandROITool.toolName);
        toolGroup.setToolPassive(ArrowAnnotateTool.toolName);
      } else {
        // 如果要切换到其他工具，确保Text工具被清理
        if (isTextToolActive) {
          textToolInstance.cleanup();
          removeTextToolEventListeners();
          isTextToolActive = false;
        }

        // 禁用十字线工具，激活选择的新工具
        toolGroup.setToolDisabled(CrosshairsTool.toolName);

        try {
          // 确保工具存在于工具组中
          if (!toolGroup.hasTool(actualNewTool)) {
            console.log(`工具 ${actualNewTool} 不在工具组中，尝试添加`);

            // 获取工具类并添加到工具组
            const toolClassMap: Record<string, any> = {
              [LengthTool.toolName]: LengthTool,
              [AngleTool.toolName]: AngleTool,
              [ProbeTool.toolName]: ProbeTool,
              [RectangleROITool.toolName]: RectangleROITool,
              [EllipticalROITool.toolName]: EllipticalROITool,
              [PlanarFreehandROITool.toolName]: PlanarFreehandROITool,
              [ArrowAnnotateTool.toolName]: ArrowAnnotateTool,
              [CrosshairsTool.toolName]: CrosshairsTool,
              [PanTool.toolName]: PanTool,
              [ZoomTool.toolName]: ZoomTool,
              [WindowLevelTool.toolName]: WindowLevelTool,
            };

            const ToolClass = toolClassMap[actualNewTool];
            if (ToolClass) {
              try {
                // 先确保工具被全局注册
                cornerstoneTools.addTool(ToolClass);
                // 然后添加到工具组
                toolGroup.addTool(ToolClass);
                console.log(`成功添加工具 ${actualNewTool} 到工具组`);
              } catch (addError) {
                console.error(`添加工具 ${actualNewTool} 到工具组失败:`, addError);
              }
            } else {
              console.error(`找不到工具类: ${actualNewTool}`);
            }
          }

          toolGroup.setToolActive(actualNewTool, {
            bindings: [
              {
                mouseButton: MouseBindings.Primary, // 左键触发
              },
            ],
          });
          console.log(`成功激活工具: ${actualNewTool}`);
        } catch (error) {
          console.error(`激活工具 ${actualNewTool} 时出错:`, error);
        }
      }
    } else {
      // 当newToolName为空时，表示要关闭当前工具
      console.log(`关闭当前工具: ${actualCurrentTool || currentToolName}`);

      // 如果Text工具处于激活状态，先清理它
      if (isTextToolActive) {
        textToolInstance.cleanup();
        removeTextToolEventListeners();
        isTextToolActive = false;
        console.log('已清理Text工具');
      }

      // 对于十字线工具，关闭时应该重新激活基本工具
      if (currentToolName === 'Crosshairs') {
        try {
          toolGroup.setToolDisabled(CrosshairsTool.toolName);
          console.log('十字线工具已禁用');

          // 可以选择激活平移工具作为默认
          if (toolGroup.hasTool(PanTool.toolName)) {
            toolGroup.setToolActive(PanTool.toolName, {
              bindings: [{ mouseButton: MouseBindings.Primary }],
            });
            console.log('已激活平移工具作为默认');
          }
        } catch (error) {
          console.error('关闭十字线工具时出错:', error);
        }
      } else {
        // 对于其他工具，直接设为被动
        try {
          if (actualCurrentTool && toolGroup.hasTool(actualCurrentTool)) {
            toolGroup.setToolPassive(actualCurrentTool);
            console.log(`工具 ${actualCurrentTool} 已设为被动`);
          }

          // 重新激活十字线工具作为默认
          if (toolGroup.hasTool(CrosshairsTool.toolName)) {
            toolGroup.setToolActive(CrosshairsTool.toolName, {
              bindings: [{ mouseButton: MouseBindings.Primary }],
            });
            console.log('已重新激活十字线工具作为默认');
          }
        } catch (error) {
          console.error('关闭工具时出错:', error);
        }
      }
    }

    // 强制刷新当前工具组的所有绑定的视口
    setTimeout(() => {
      const viewports = toolGroup.getViewportsInfo();
      for (const viewport of viewports) {
        try {
          const renderingEngine = getRenderingEngine(viewport.renderingEngineId);
          if (renderingEngine) {
            const vp = renderingEngine.getViewport(viewport.viewportId);
            if (vp) {
              vp.render();
              console.log(`强制刷新视口: ${viewport.viewportId}`);
            }
          }
        } catch (error) {
          console.warn(`刷新视口失败: ${viewport.viewportId}`, error);
        }
      }
    }, 100);
  }); // 结束forEach循环

  console.log(`工具切换完成: ${currentToolName} -> ${newToolName}`);
};

// 设置文本工具的事件监听
function setupTextTool() {
  // 先移除可能存在的旧事件监听器
  removeTextToolEventListeners();

  // 获取所有的canvas元素（DICOM渲染区域）
  const canvases = document.querySelectorAll('canvas');

  canvases.forEach((canvas) => {
    const element = canvas.parentElement;
    if (element) {
      // 添加新的事件监听器
      element.addEventListener('mousedown', handleTextToolMouseDown);
    }
  });
}

// 移除文本工具的事件监听器
function removeTextToolEventListeners() {
  const canvases = document.querySelectorAll('canvas');

  canvases.forEach((canvas) => {
    const element = canvas.parentElement;
    if (element) {
      element.removeEventListener('mousedown', handleTextToolMouseDown);
    }
  });

  // 也移除可能存在的document级别的事件监听器
  if (handleMouseMove) {
    document.removeEventListener('mousemove', handleMouseMove);
    handleMouseMove = null;
  }
  if (handleMouseUp) {
    document.removeEventListener('mouseup', handleMouseUp);
    handleMouseUp = null;
  }
}

// 全局变量用于存储事件处理函数
let handleMouseMove: ((evt: MouseEvent) => void) | null = null;
let handleMouseUp: ((evt: MouseEvent) => void) | null = null;

// 处理文本工具的mousedown事件
function handleTextToolMouseDown(evt: MouseEvent) {
  // 防止事件冒泡，避免触发其他工具
  evt.preventDefault();
  evt.stopPropagation();

  // 调用TextTool的mousedown处理
  textToolInstance.handleMouseDown(evt);

  // 如果开始绘制，添加mousemove和mouseup监听
  handleMouseMove = (moveEvt: MouseEvent) => {
    textToolInstance.handleMouseDrag(moveEvt);
  };

  handleMouseUp = (upEvt: MouseEvent) => {
    textToolInstance.handleMouseUp(upEvt);

    // 移除临时的事件监听器
    if (handleMouseMove) {
      document.removeEventListener('mousemove', handleMouseMove);
      handleMouseMove = null;
    }
    if (handleMouseUp) {
      document.removeEventListener('mouseup', handleMouseUp);
      handleMouseUp = null;
    }
  };

  // 在document上添加临时的事件监听器，这样即使鼠标移出元素也能继续跟踪
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
}

/**
 * 清除最后一个测量或标注
 */
export const clearLastAnnotation = (dispatch: Dispatch) => {
  try {
    // 首先检查 cornerstone 库是否可用
    if (!isCornerstoneAvailable()) {
      console.error('无法清除标注: Cornerstone 库未正确加载');
      message.error('清除标注失败: 图像渲染引擎未正确初始化');
      return;
    }

    console.log('clearLastAnnotation');
    const { annotation } = cornerstoneTools;
    const { state, selection } = annotation;
    const allAnnotations = state.getAllAnnotations();
    console.log('allAnnotations', allAnnotations);

    console.log('selection', selection);

    // 先处理cornerstone的标注
    if (allAnnotations.length > 0) {
      // 删除最后一个标注
      const lastAnnotation = allAnnotations[allAnnotations.length - 1];
      const annotationUID = lastAnnotation.annotationUID;

      console.log('annotationUID', annotationUID);
      console.log('Before remove:', allAnnotations);

      // ✅ 删除标注
      state.removeAnnotation(String(annotationUID));

      console.log('After remove:', state.getAllAnnotations());

      // ✅ 触发 Redux 更新
      dispatch({
        type: 'views/save',
        payload: { annotationDeletedTrigger: true },
      });

      setTimeout(() => {
        dispatch({
          type: 'views/save',
          payload: { annotationDeletedTrigger: false },
        });
      }, 0);
    } else {
      // 如果没有cornerstone标注，尝试删除最后一个文本标注
      const removed = textToolInstance.removeLastAnnotation();
      if (removed) {
        console.log('Removed last text annotation');
      }
    }
  } catch (error) {
    console.error('清除最近标注失败:', error);
    message.error('清除标注失败，请重试');
  }
};

/**
 * 清除所有测量和标注
 */
export const clearAllAnnotations = (dispatch: Dispatch) => {
  try {
    // 首先检查 cornerstone 库是否可用
    if (!isCornerstoneAvailable()) {
      console.error('无法清除所有标注: Cornerstone 库未正确加载');
      message.error('清除所有标注失败: 图像渲染引擎未正确初始化');
      return;
    }

    // 清除cornerstone的标注
    const { annotation } = cornerstoneTools;
    const { state } = annotation;
    state.removeAllAnnotations();

    // 清除文本标注
    textToolInstance.clearAllAnnotations();

    // ✅ 触发 Redux 更新
    dispatch({
      type: 'views/save',
      payload: { annotationDeletedTrigger: true },
    });

    setTimeout(() => {
      dispatch({
        type: 'views/save',
        payload: { annotationDeletedTrigger: false },
      });
    }, 0);
  } catch (error) {
    console.error('清除所有标注失败:', error);
    message.error('清除所有标注失败，请重试');
  }
};

// 强制初始化工具组的函数
export const forceInitializeToolGroup = (toolGroupId: string): boolean => {
  console.log(`🔧 开始强制初始化工具组: ${toolGroupId}`);

  const win = window as any;
  if (!win.cornerstone || !win.cornerstoneTools) {
    console.error('❌ Cornerstone 库未加载');
    return false;
  }

  try {
    const { ToolGroupManager, addTool } = win.cornerstoneTools;
    const toolGroup = ToolGroupManager.getToolGroup(toolGroupId);

    if (!toolGroup) {
      console.error(`❌ 工具组不存在: ${toolGroupId}`);
      return false;
    }

    console.log(`📋 当前工具组状态:`, {
      id: toolGroup.id,
      viewportIds: toolGroup.viewportIds,
      toolOptions: Object.keys(toolGroup.toolOptions || {}),
    });

    // 要注册和添加的工具列表
    const toolsToRegister = [
      { name: 'CrosshairsTool', class: win.cornerstoneTools.CrosshairsTool },
      { name: 'LengthTool', class: win.cornerstoneTools.LengthTool },
      { name: 'AngleTool', class: win.cornerstoneTools.AngleTool },
      { name: 'ProbeTool', class: win.cornerstoneTools.ProbeTool },
      { name: 'RectangleROITool', class: win.cornerstoneTools.RectangleROITool },
      { name: 'CircleROITool', class: win.cornerstoneTools.CircleROITool },
      { name: 'EllipticalROITool', class: win.cornerstoneTools.EllipticalROITool },
      { name: 'PlanarFreehandROITool', class: win.cornerstoneTools.PlanarFreehandROITool },
      { name: 'ArrowAnnotateTool', class: win.cornerstoneTools.ArrowAnnotateTool },
      { name: 'ZoomTool', class: win.cornerstoneTools.ZoomTool },
      { name: 'StackScrollMouseWheelTool', class: win.cornerstoneTools.StackScrollMouseWheelTool },
      { name: 'WindowLevelTool', class: win.cornerstoneTools.WindowLevelTool },
      { name: 'PanTool', class: win.cornerstoneTools.PanTool },
    ];

    let globalRegisteredCount = 0;
    let toolGroupAddedCount = 0;
    let errorCount = 0;

    // 第一步：全局注册所有工具
    console.log('🔧 第一步：全局注册工具到Cornerstone库...');
    for (const { name, class: ToolClass } of toolsToRegister) {
      if (!ToolClass) {
        console.warn(`⚠️ 工具类不存在: ${name}`);
        continue;
      }

      try {
        // 全局注册工具
        addTool(ToolClass);
        console.log(`✅ 全局注册成功: ${name}`);
        globalRegisteredCount++;
      } catch (error: any) {
        // 如果已经注册过了，这是正常的
        if (error.message && error.message.includes('already been added globally')) {
          console.log(`✅ ${name} 已经全局注册过了`);
          globalRegisteredCount++;
        } else {
          console.error(`❌ 全局注册失败 ${name}:`, error);
          errorCount++;
        }
      }
    }

    // 第二步：添加工具到工具组
    console.log('🔧 第二步：添加工具到工具组...');
    for (const { name, class: ToolClass } of toolsToRegister) {
      if (!ToolClass) continue;

      try {
        // 检查工具是否已经在工具组中
        const toolName = ToolClass.toolName || name;
        const isAlreadyAdded = toolGroup.hasTool(toolName);

        if (isAlreadyAdded) {
          console.log(`✅ 工具 ${name} 已在工具组中`);
          toolGroupAddedCount++;
          continue;
        }

        // 添加到工具组
        toolGroup.addTool(ToolClass);
        console.log(`✅ 添加到工具组成功: ${name} (${toolName})`);
        toolGroupAddedCount++;
      } catch (error) {
        console.error(`❌ 添加到工具组失败 ${name}:`, error);
        errorCount++;
      }
    }

    // 验证最终结果
    const finalTools = Object.keys(toolGroup.toolOptions || {});
    console.log(`📊 工具注册完成:`);
    console.log(`   - 全局注册: ${globalRegisteredCount}/${toolsToRegister.length}`);
    console.log(`   - 工具组添加: ${toolGroupAddedCount}/${toolsToRegister.length}`);
    console.log(`   - 错误: ${errorCount}`);
    console.log(`📋 最终可用工具:`, finalTools);

    // 第三步：设置工具状态
    if (toolGroupAddedCount > 0) {
      console.log('🔧 第三步：设置工具状态...');
      try {
        const { MouseBindings } = win.cornerstoneTools.Enums;

        // 设置基本激活工具
        if (toolGroup.hasTool('CrosshairsTool')) {
          toolGroup.setToolActive('CrosshairsTool', {
            bindings: [{ mouseButton: MouseBindings.Primary }],
          });
          console.log('✅ CrosshairsTool 设置为激活状态');
        }

        if (toolGroup.hasTool('ZoomTool')) {
          toolGroup.setToolActive('ZoomTool', {
            bindings: [{ mouseButton: MouseBindings.Secondary }],
          });
          console.log('✅ ZoomTool 设置为激活状态');
        }

        if (toolGroup.hasTool('StackScrollMouseWheelTool')) {
          toolGroup.setToolActive('StackScrollMouseWheelTool');
          console.log('✅ StackScrollMouseWheelTool 设置为激活状态');
        }

        if (toolGroup.hasTool('WindowLevelTool')) {
          toolGroup.setToolActive('WindowLevelTool', {
            bindings: [{ mouseButton: MouseBindings.Auxiliary }],
          });
          console.log('✅ WindowLevelTool 设置为激活状态');
        }

        // 设置测量工具为被动状态
        const measurementTools = ['LengthTool', 'AngleTool', 'ProbeTool', 'RectangleROITool', 'CircleROITool'];
        measurementTools.forEach((toolName) => {
          if (toolGroup.hasTool(toolName)) {
            toolGroup.setToolPassive(toolName);
            console.log(`✅ ${toolName} 设置为被动状态`);
          }
        });
      } catch (error) {
        console.error('❌ 设置工具状态时出错:', error);
      }
    }

    const success = toolGroupAddedCount > 0;
    console.log(`🎯 工具组初始化${success ? '成功' : '失败'}`);

    // 如果成功，也要更新当前工具状态
    if (success) {
      console.log('🔄 工具组初始化成功，可以开始使用工具了！');
    }

    return success;
  } catch (error) {
    console.error('❌ 强制初始化工具组失败:', error);
    return false;
  }
};

// 暴露到全局供调试使用
(window as any).forceInitToolGroup = (toolGroupId?: string) => {
  const allToolGroups = ToolGroupManager.getAllToolGroups();
  if (toolGroupId) {
    return forceInitializeToolGroup(toolGroupId);
  } else if (allToolGroups.length > 0) {
    // 如果没有指定工具组ID，则初始化第一个工具组
    return forceInitializeToolGroup(allToolGroups[0].id);
  } else {
    console.error('没有找到任何工具组');
    return false;
  }
};
