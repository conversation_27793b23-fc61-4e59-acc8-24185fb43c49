import React, { useState, useEffect } from 'react';
import withAuth from '@/hocs/withAuth';
import { connect } from 'dva';
import dayjs from 'dayjs';
import {
  Flex,
  Input,
  Button,
  Table,
  Tag,
  Tooltip,
  Modal,
  Pagination,
  ConfigProvider,
  Switch,
  InputNumber,
  message,
  Space,
} from 'antd';
import type { TableProps } from 'antd';
import { SearchOutlined, EditOutlined, DeleteOutlined, UserAddOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { UserInfoType, QueryUserListParamsType, UserListType, LoginType } from '@/types/user';
import { DEFAULT_PAGE_SIZE } from '@/types/index';
import { FetchUserList, UpdateUserInfo, DeleteUser } from '@/services/user';
import UserInfoModal from './components/UserInfoModal';
import DeleteConfirmModal from '@/pages/TemplateManagement/components/DeleteConfirmModal';
import CustomIcon from '@/components/CustomIcon';
import storeUtil from '@/utils/store';
import styles from './index.less';
import './index.less';

const UserManage: React.FC = (props) => {
  const [delectVisible, setDelectVisible] = useState(false);
  const [delectId, setDelectId] = useState('');

  const { userInfo } = props;
  const userColumns: TableProps<UserInfoType>['columns'] = [
    {
      title: '姓名',
      dataIndex: 'Name',
      ellipsis: true,
    },
    {
      title: '员工编号/工号',
      dataIndex: 'Number',
      ellipsis: true,
    },
    {
      title: '手机号',
      dataIndex: 'Phone',
      ellipsis: true,
    },
    {
      title: '角色',
      dataIndex: 'Role',
      ellipsis: true,
      render: (_, record) => {
        const colorMap = {
          doctor: 'blue',
          check: 'green',
        };
        const RoleMap = {
          doctor: '诊断医生',
          check: '主任医生',
        };
        return <Tag color={colorMap[record.Role]}>{RoleMap[record.Role]}</Tag>;
      },
    },
    {
      title: '创建人',
      dataIndex: 'CreateUser',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'CreateTime',
      ellipsis: true,
      render: (_, record) => {
        return dayjs(record.CreateTime).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    // {
    //   title: '状态',
    //   dataIndex: 'Status',
    //   render: (_, record) => {
    //     return (
    //       <Switch
    //         loading={visible}
    //         checked={!record.Status}
    //         checkedChildren="开启"
    //         unCheckedChildren="禁用"
    //         onChange={(checked) => {
    //         setVisible(true);
    //         UpdateUserInfo({
    //           Id: record.Id,
    //           Status: checked ? 0 : 1,
    //         }).then(() => {
    //           message.success(`${checked ? '禁用' : '启用'}成功`);
    //           getListData(queryParams);
    //         }).finally(() => {
    //           setVisible(false);
    //         });
    //       }} />
    //     )
    //   }
    // },
    {
      title: '操作',
      ellipsis: true,
      render: (_, record) => {
        return (
          <Flex justify='space-evenly'>
            <Button
              type='text'
              icon={<EditOutlined />}
              style={{ color: '#1F69B4' }}
              disabled={record.Id == userInfo?.Id}
              onClick={(e) => {
                setFormInitial({
                  ...record,
                  Pwd: '',
                });
                setModalVisible(true);
              }}
            >
              编辑
            </Button>
            <Button
              type='text'
              style={{ color: '#1F69B4' }}
              icon={<DeleteOutlined />}
              disabled={record.Id == userInfo?.Id}
              onClick={(e) => {
                setDelectId(record.Id);
                setDelectVisible(true);
              }}
            >
              删除
            </Button>
          </Flex>
        );
      },
    },
  ];
  //
  // const [visible, setVisible] = useState(false);
  const [searchItem, setSearchItem] = useState('');
  // 查询参数
  const [queryParams, setQueryParams] = useState<QueryUserListParamsType>({
    Page: 1,
    Limit: DEFAULT_PAGE_SIZE,
    SearchItem: '', // 模糊查找,
  });
  const [list, setList] = useState<UserInfoType[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [current, setCurrent] = useState<number>(1);

  // 分页请求
  const getListData = async (params: QueryUserListParamsType, refresh: boolean = false) => {
    if (refresh) setSearchItem(''), setQueryParams({ ...queryParams, Page: 1, SearchItem: '' });
    try {
      let res: UserListType = await FetchUserList(params);
      setList(res.UserList || []);
      setTotal(res.Length || 0);
      // message.success('获取用户列表成功');
    } catch (error) {}
  };

  // 新增、编辑
  const [modalVisible, setModalVisible] = useState(false);
  const [formInitial, setFormInitial] = useState<UserInfoType & { Pwd: string }>({
    Id: '',
    Name: '',
    Number: '',
    Role: 'doctor',
    Email: '',
    Phone: '',
    Pwd: '',
  });
  const changeModal = (value: boolean, refresh: boolean = false) => {
    if (!value)
      setFormInitial({
        Id: '',
        Name: '',
        Number: '',
        Role: 'doctor',
        Email: '',
        Phone: '',
        Pwd: '',
      });
    setModalVisible(value);
    if (refresh) getListData(queryParams);
  };

  useEffect(() => {
    getListData(queryParams);
  }, [queryParams]);
  return (
    <ConfigProvider
      theme={{
        components: {
          Input: {
            activeBg: '#262628',
            hoverBg: '#262628',
          },
          Pagination: {
            itemBg: 'transparent',
            itemActiveBg: '#1F69B4',
            itemSize: 24,
          },
          Modal: {
            contentBg: '#262628',
            titleColor: '#ffffff'
          },
        },
      }}
    >
      <div>
        <UserInfoModal
          visible={modalVisible}
          initial={formInitial}
          creater={userInfo?.Name || ''}
          createrId={userInfo?.Id || ''}
          handleOk={(e) => changeModal(e, true)}
          handleCancel={changeModal}
        />

        <div className={styles.wrapper}>
          <div className={styles.title}>用户管理</div>
          <div className={styles.content}>
            <div className={styles.tableBox}>
              <Flex style={{ marginBottom: '20px' }} justify='space-between' align='center'>
                <div>
                  <Input
                    className={styles.searchInput}
                    placeholder='请输入关键字搜索'
                    prefix={
                      <SearchOutlined
                        onClick={() =>
                          setQueryParams({
                            ...queryParams,
                            SearchItem: searchItem,
                          })
                        }
                      />
                    }
                    allowClear
                    onChange={(e) => {
                      setSearchItem(e.target.value);
                    }}
                    onPressEnter={() => {
                      if (!searchItem) return;
                      setQueryParams({
                        ...queryParams,
                        SearchItem: searchItem,
                      });
                    }}
                    onClear={() => {
                      setSearchItem('');
                      setQueryParams({
                        ...queryParams,
                        SearchItem: '',
                      });
                    }}
                  />
                </div>
                <Button
                  type='text'
                  icon={<CustomIcon type='icon-xinjianmoban-01' />}
                  style={{ color: '#ffffff' }}
                  onClick={() => setModalVisible(true)}
                >
                  新增用户
                </Button>
              </Flex>
              <Table<UserInfoType> columns={userColumns} dataSource={list} pagination={false} rowHoverable={false} />
              <Flex style={{ marginTop: '18px' }} align='center'>
                <Pagination
                  current={current}
                  total={total}
                  pageSize={DEFAULT_PAGE_SIZE}
                  showTotal={(total) => <div>共 {total} 条</div>}
                  itemRender={(page, type: 'page' | 'prev' | 'next' | 'jump-prev' | 'jump-next', originalElement) => {
                    if (type === 'page') {
                      return <div>{page}</div>;
                    }
                    return originalElement;
                  }}
                  onChange={(page) => {
                    setCurrent(page || 1);
                    setQueryParams({
                      ...queryParams,
                      Page: page || 1,
                    });
                  }}
                />
                <Space style={{ marginLeft: '16px' }} size={12}>
                  前往
                  <InputNumber
                    style={{ width: 50, backgroundColor: '#262628' }}
                    min={1}
                    max={Math.ceil(total / DEFAULT_PAGE_SIZE)}
                    precision={0}
                    changeOnBlur
                    controls={false}
                    // 限制只能输入数字
                    onKeyPress={(e) => !/^\d$/.test(e.key) && e.preventDefault()} 
                    onPressEnter={(e) => {
                      if (
                        (e?.target as HTMLInputElement)?.value &&
                        Number((e?.target as HTMLInputElement).value) > Math.ceil(total / DEFAULT_PAGE_SIZE)
                      )
                        return;
                      setCurrent((e?.target as HTMLInputElement)?.value ? Number((e?.target as HTMLInputElement).value) : 1);
                      setQueryParams({
                        ...queryParams,
                        Page: (e?.target as HTMLInputElement)?.value ? Number((e?.target as HTMLInputElement).value) : 1,
                      });
                    }}
                  />
                  页
                </Space>
              </Flex>
            </div>
          </div>
        </div>
        <DeleteConfirmModal
          visible={delectVisible}
          onConfirm={() => {
            DeleteUser({
              Id: delectId || '',
            }).then(() => {
              message.success(`删除成功`);
              setDelectId('');
              setDelectVisible(false);
              getListData(queryParams, true);
            });
          }}
          onCancel={() => setDelectVisible(false)}
          message='确认删除该用户吗？'
        />
      </div>
    </ConfigProvider>
  );
};

// 定义 mapStateToProps 函数，从 dva 的 login 模块中获取 userInfo
const mapStateToProps = ({ user }: { user: LoginType }) => {
  return {
    userInfo: user.userInfo,
  };
};

// 使用 connect 连接组件和状态
const UserManageWithUserInfo = connect(mapStateToProps)(UserManage);

export default withAuth(UserManageWithUserInfo);
