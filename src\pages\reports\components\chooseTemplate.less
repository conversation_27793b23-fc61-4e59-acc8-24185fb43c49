.templateList {
  :global {
    .ant-list-item {
      padding: 8px 16px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        background-color: #404040;
      }
    }
  }
}

.previewArea {
  :global {
    .ant-typography {
      color: white;
      margin-bottom: 16px;
    }
  }
}

.ellipsis100 {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
  display: inline-block;
}
.ellipsis120 {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
  display: inline-block;
}

// 自定义滚动条样式
:global {
  .custom-scrollbar {
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #666;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #888;
    }

    &::-webkit-scrollbar-corner {
      background: transparent;
    }
  }
} 