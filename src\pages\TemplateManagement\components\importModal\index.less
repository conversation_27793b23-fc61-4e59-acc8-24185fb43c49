.closeIcon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 28px;
  height: 28px;
  color: #ffffff;
  font-weight: 600;
  border-radius: 50%;
  background-color: #D92E2D;
}
// 弹出框 样式重新
.modalHeader {
  padding: 12px 20px!important;
  font-weight: 400!important;
  background-color: #4C4C4C!important;
}
.modalContent {
  color: #ffffff;
  padding: 0 0 20px!important;
  background-color: #333233!important;
}
.modalBody {
  padding:20px 20px 0!important;
}
.modalFooter {
  border-radius: 10px!important;
  margin: 0px 20px;
  background-color: #333233!important;
}
.title {
  margin-bottom: 16px;
  font-size: 16px;
  color: #ffffff;
  font-weight: 600;
}
.info {
  margin-left: 14px;
  &_name {
    font-size: 16px;
  }
  &-t {
    font-size: 14px;
  }
}

.patient {
  border-radius: 8px;
  padding: 10px;
  background-color: #262628;
}
.htmlBox {
  margin-bottom: 15px;
  &_t {
    font-size: 14px;
    color: #ffffff;
    margin-bottom: 10px;
  }
  &_con {
    border-radius: 8px;
    padding: 10px;
    height: 250px;
    overflow-y: scroll;
    color: #000000;
    background-color: #ffffff;
  }
}

.rowSelected {
  background-color: #1F69B4;
}

.search {
  margin-bottom: 10px;
  width: 200px;
}