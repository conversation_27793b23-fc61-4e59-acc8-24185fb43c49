import React, { useState, useEffect } from 'react';
import { Space, Image, Empty, Button, message, Checkbox, Modal } from 'antd';
import { LeftOutlined, RightOutlined, CloseOutlined } from '@ant-design/icons';
import { useDispatch } from 'umi';

interface ScreenshotGalleryProps {
  imgs?: { name: string; url: string }[];
  onInsertImages?: (imageNames: string[]) => void; // 修改为传递图片名称数组
}

const ScreenshotGallery: React.FC<ScreenshotGalleryProps> = ({ imgs = [], onInsertImages }) => {
  const dispatch = useDispatch();
  // 管理选中的图片索引
  const [selectedImages, setSelectedImages] = useState<number[]>([]);
  // 管理自定义预览状态
  const [customPreviewVisible, setCustomPreviewVisible] = useState(false);
  const [currentPreviewIndex, setCurrentPreviewIndex] = useState(0);

  // 为每张图片生成并保存时间戳
  const [imageTimestamps, setImageTimestamps] = useState<Record<string, string>>({});

  // 当图片列表变化时，为新图片生成时间戳
  useEffect(() => {
    const newTimestamps = { ...imageTimestamps };
    let hasNewImages = false;

    imgs.forEach((item) => {
      if (!newTimestamps[item.url]) {
        newTimestamps[item.url] = new Date().toLocaleString();
        hasNewImages = true;
      }
    });

    if (hasNewImages) {
      setImageTimestamps(newTimestamps);
    }
  }, [imgs]);

  // 处理图片选择
  const handleImageSelect = (index: number, checked: boolean) => {
    if (checked) {
      setSelectedImages(prev => [...prev, index]);
    } else {
      setSelectedImages(prev => prev.filter(i => i !== index));
    }
  };

  // 全选/取消全选
  const handleSelectAll = () => {
    if (selectedImages.length === imgs.length) {
      setSelectedImages([]);
    } else {
      setSelectedImages(imgs.map((_, index) => index));
    }
  };

  // 获取图片信息
  const getImageInfo = (item: { name: string; url: string }, index: number) => {
    const timestamp = imageTimestamps[item.url] || new Date().toLocaleString();
    let displayName = item.name || '';

    if (!displayName) {
      try {
        const date = new Date(timestamp);
        if (!isNaN(date.getTime())) {
          const year = date.getFullYear().toString().slice(2);
          const month = (date.getMonth() + 1).toString().padStart(2, '0');
          const day = date.getDate().toString().padStart(2, '0');
          const hours = date.getHours().toString().padStart(2, '0');
          const minutes = date.getMinutes().toString().padStart(2, '0');
          const seconds = date.getSeconds().toString().padStart(2, '0');
          displayName = `${year}${month}${day}${hours}${minutes}${seconds}${index + 1}`;
        }
      } catch (e) {
        console.warn('格式化图片名称失败:', e);
        displayName = `截图${index + 1}`;
      }
    }

    return {
      timestamp: displayName,
      originalTimestamp: timestamp,
    };
  };

  // 处理预览图片
  const handlePreviewImage = (index: number) => {
    setCurrentPreviewIndex(index);
    setCustomPreviewVisible(true);
  };

  // 切换到上一张图片
  const handlePrevImage = () => {
    setCurrentPreviewIndex((prev) => (prev > 0 ? prev - 1 : imgs.length - 1));
  };

  // 切换到下一张图片
  const handleNextImage = () => {
    setCurrentPreviewIndex((prev) => (prev < imgs.length - 1 ? prev + 1 : 0));
  };

  // 关闭预览
  const handleClosePreview = () => {
    setCustomPreviewVisible(false);
  };

  // 处理键盘事件
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!customPreviewVisible) return;

      switch (e.key) {
        case 'ArrowLeft':
          handlePrevImage();
          break;
        case 'ArrowRight':
          handleNextImage();
          break;
        case 'Escape':
          handleClosePreview();
          break;
        default:
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [customPreviewVisible, imgs.length]);

  const handleAction = (action: string) => {
    if (action === '插入') {
      if (selectedImages.length === 0) {
        message.warning('请先选择图片');
        return;
      }
      // 获取选中图片的名称，传递给富文本编辑器
      const selectedImageObjs = selectedImages.map(index => imgs[index]);
      const selectedImageNames = selectedImageObjs.map(img => img.name);
      // 标记为使用中
      selectedImageNames.forEach(name => {
        dispatch({
          type: 'reports/updateImageUseStatus',
          payload: { Name: name, Use: 1 },
        });
      });
      if (onInsertImages) {
        onInsertImages(selectedImageObjs.map(img => img.name));
        message.success(`插入 ${selectedImages.length} 张图片`);
      } else {
        message.error('插入功能未配置');
      }
    } else if (action === '删除') {
      if (selectedImages.length === 0) {
        message.warning('请先选择图片');
        return;
      }
      // 直接用 imgs[index].name
      const selectedImageObjs = selectedImages.map(index => imgs[index]);
      const selectedImageNames = selectedImageObjs.map(img => img.name);
      // 依次删除
      selectedImageNames.forEach(name => {
        dispatch({
          type: 'reports/deleteImage',
          payload: { Name: name },
        });
      });
      message.info(`删除 ${selectedImages.length} 张图片`);
      setSelectedImages([]);
    } else if (action === '查看') {
      if (selectedImages.length === 0) {
        message.warning('请先选择图片');
        return;
      }
      // 预览第一个选中的图片
      handlePreviewImage(selectedImages[0]);
    } else {
      message.info(`${action} 功能`);
    }
  };

  return (
    <>
      {/* 自定义预览模态框 */}
      <Modal
        visible={customPreviewVisible}
        footer={null}
        closable={false}
        centered
        width='80%'
        bodyStyle={{
          padding: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.85)',
          position: 'relative',
          height: '80vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
        wrapClassName='screenshot-preview-modal'
        onCancel={handleClosePreview}
        title='截图预览'
      >
        {imgs.length > 0 && customPreviewVisible && (
          <>
            {/* 关闭按钮 - 上移到与标题同一行 */}
            <div
              style={{
                position: 'absolute',
                top: '-45px', // 上移到标题栏位置
                right: '20px',
                zIndex: 10,
                cursor: 'pointer',
                color: '#fff',
                fontSize: '24px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: '45px', // 与标题栏高度一致
              }}
              onClick={handleClosePreview}
            >
              <CloseOutlined />
            </div>

            {/* 图片区域 */}
            <div
              style={{
                position: 'relative',
                width: '100%',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <img
                src={imgs[currentPreviewIndex].url}
                style={{
                  maxWidth: '90%',
                  maxHeight: '90%',
                  objectFit: 'contain',
                  boxShadow: '0 0 20px rgba(0,0,0,0.5)',
                  userSelect: 'none',
                  WebkitUserSelect: 'none',
                  MozUserSelect: 'none',
                  msUserSelect: 'none',
                  pointerEvents: 'auto',
                }}
                alt='截图预览'
                draggable={false}
                onContextMenu={(e) => e.preventDefault()}
              />

              {/* 左右切换按钮 */}
              {imgs.length > 1 && (
                <>
                  <div
                    className='preview-nav-button'
                    style={{
                      position: 'absolute',
                      left: '10px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      width: '40px',
                      height: '40px',
                      borderRadius: '50%',
                      backgroundColor: 'rgba(0,0,0,0.5)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      cursor: 'pointer',
                    }}
                    onClick={handlePrevImage}
                  >
                    <LeftOutlined style={{ color: '#fff', fontSize: '18px' }} />
                  </div>
                  <div
                    className='preview-nav-button'
                    style={{
                      position: 'absolute',
                      right: '10px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      width: '40px',
                      height: '40px',
                      borderRadius: '50%',
                      backgroundColor: 'rgba(0,0,0,0.5)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      cursor: 'pointer',
                    }}
                    onClick={handleNextImage}
                  >
                    <RightOutlined style={{ color: '#fff', fontSize: '18px' }} />
                  </div>
                </>
              )}
            </div>

            {/* 图片信息 */}
            <div
              style={{
                position: 'absolute',
                bottom: '10px',
                left: 0,
                width: '100%',
                textAlign: 'center',
                color: '#fff',
                fontSize: '12px',
              }}
            >
              {currentPreviewIndex + 1} / {imgs.length} -{' '}
              {getImageInfo(imgs[currentPreviewIndex], currentPreviewIndex).timestamp}
              {/* 显示原始时间戳作为辅助信息 */}
              <div style={{ fontSize: '10px', opacity: 0.8 }}>
                {getImageInfo(imgs[currentPreviewIndex], currentPreviewIndex).originalTimestamp}
              </div>
            </div>
          </>
        )}
      </Modal>

      <div
        style={{
          padding: '15px',
          height: 'calc(100vh - 268px)',
          overflowY: 'auto',
          overflowX: 'hidden',
          width: '100%',
          boxSizing: 'border-box',
        }}
      >
        <div
          style={{
            backgroundColor: '#191919',
            borderRadius: '8px',
            height: '100%',
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <div style={{ flex: 1, overflowY: imgs.length > 0 ? 'auto' : 'hidden', overflowX: 'hidden', padding: '8px' }}>
            {imgs.length > 0 ? (
              <>
                {/* 全选按钮 */}
                <div style={{ marginBottom: '12px', paddingLeft: '4px' }}>
                  <Checkbox
                    indeterminate={selectedImages.length > 0 && selectedImages.length < imgs.length}
                    checked={selectedImages.length === imgs.length}
                    onChange={handleSelectAll}
                    style={{ color: '#fff' }}
                  >
                    <span style={{ color: '#fff', fontSize: '12px' }}>
                      全选 ({selectedImages.length}/{imgs.length})
                    </span>
                  </Checkbox>
                </div>

                {/* 图片网格 */}
                <div
                  style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(3, 1fr)',
                    gap: '8px',
                    width: '100%',
                  }}
                >
                  {imgs.map((item, index) => (
                    <div
                      key={item.name || index}
                      style={{
                        position: 'relative',
                        aspectRatio: '1',
                        borderRadius: '4px',
                        overflow: 'hidden',
                        border: selectedImages.includes(index) ? '2px solid #1677ff' : '2px solid transparent',
                        transition: 'border-color 0.2s',
                      }}
                    >
                      {/* 勾选框 */}
                      <div
                        style={{
                          position: 'absolute',
                          top: '4px',
                          left: '4px',
                          zIndex: 2,
                        }}
                      >
                        <Checkbox
                          checked={selectedImages.includes(index)}
                          onChange={(e) => handleImageSelect(index, e.target.checked)}
                          style={{
                            transform: 'scale(0.8)',
                          }}
                        />
                      </div>

                      {/* 图片 */}
                      <Image
                        src={item.url}
                        width="100%"
                        height="100%"
                        style={{
                          objectFit: 'cover',
                          cursor: 'pointer',
                          display: 'block',
                        }}
                        preview={false}
                        onClick={() => handlePreviewImage(index)}
                      />

                      {/* 图片日期标签 */}
                      <div
                        style={{
                          position: 'absolute',
                          bottom: '0',
                          left: '0',
                          width: '100%',
                          backgroundColor: 'rgba(0,0,0,0.5)',
                          color: '#fff',
                          fontSize: '10px',
                          padding: '2px 4px',
                          textAlign: 'center',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                        }}
                      >
                        {getImageInfo(item, index).timestamp}
                      </div>
                    </div>
                  ))}
                </div>
              </>
            ) : (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description="暂无截图"
                style={{ color: '#888', marginTop: '40px' }}
              />
            )}
          </div>

          <div
            style={{
              display: 'flex',
              justifyContent: 'flex-end',
              gap: '7px',
              marginTop: 'auto',
              paddingTop: '12px',
              marginBottom: '46px',
              paddingRight: '18px',
            }}
          >
            <Button
              size="small"
              disabled={selectedImages.length === 0}
              style={{
                width: '60px',
                height: '22.5px',
                backgroundColor: selectedImages.length > 0 ? '#1677ff' : '#262628',
                borderRadius: '5.11px',
                fontFamily: 'Microsoft YaHei UI',
                fontWeight: 400,
                fontSize: '10.5px',
                border: selectedImages.length > 0 ? '1px solid #1677ff' : '1px solid #666',
                color: selectedImages.length > 0 ? '#fff' : '#ccc',
                opacity: selectedImages.length === 0 ? 0.5 : 1,
              }}
              onClick={() => handleAction('插入')}
            >
              插入
            </Button>
            <Button
              size="small"
              disabled={selectedImages.length === 0}
              style={{
                width: '60px',
                height: '22.5px',
                backgroundColor: selectedImages.length > 0 ? '#262628' : '#262628',
                borderRadius: '5.11px',
                fontFamily: 'Microsoft YaHei UI',
                fontWeight: 400,
                fontSize: '10.5px',
                border: '1px solid #666',
                color: selectedImages.length > 0 ? '#fff' : '#ccc',
                opacity: selectedImages.length === 0 ? 0.5 : 1,
              }}
              onClick={() => handleAction('查看')}
            >
              查看
            </Button>
            <Button
              size="small"
              disabled={selectedImages.length === 0}
              style={{
                width: '60px',
                height: '22.5px',
                backgroundColor: selectedImages.length > 0 ? '#ff4d4f' : '#262628',
                borderRadius: '5.11px',
                fontFamily: 'Microsoft YaHei UI',
                fontWeight: 400,
                fontSize: '10.5px',
                border: selectedImages.length > 0 ? '1px solid #ff4d4f' : '1px solid #666',
                color: selectedImages.length > 0 ? '#fff' : '#ccc',
                opacity: selectedImages.length === 0 ? 0.5 : 1,
              }}
              onClick={() => handleAction('删除')}
            >
              删除
            </Button>
          </div>
        </div>
      </div>
    </>
  );
};

export default ScreenshotGallery;
