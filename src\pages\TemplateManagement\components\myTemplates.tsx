import React, { useMemo, useState, useEffect } from 'react';
import { Input, Button, Table, Space, Pagination, InputNumber, Flex, ConfigProvider } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import CustomIcon from '@/components/CustomIcon';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import dayjs from 'dayjs';
import type { ApiTemplate } from '../data';
import DeleteConfirmModal from './DeleteConfirmModal';
import { DEFAULT_PAGE_SIZE } from '@/types/index';
import styles from './styles.less'

interface MyTemplatesProps {
  onNewTemplate: () => void;
  onImport: () => void;
  dataSource: ApiTemplate[];
  loading: boolean;
  pagination: TablePaginationConfig;
  onTableChange: (pagination: TablePaginationConfig & { SearchItem?: string }) => void;
  onEdit: (template: ApiTemplate, readOnly: boolean) => void;
  onDelete: (id: number) => void;
}

const MyTemplates: React.FC<MyTemplatesProps> = ({
  onNewTemplate,
  onImport,
  dataSource,
  loading,
  pagination,
  onTableChange,
  onEdit,
  onDelete
}) => {
  const [deleteId, setDeleteId] = useState<number | null>(null);
  const [current, setCurrent] = useState<number>(1);
  const [searchItem, setSearchItem] = useState('');

  useEffect(() => {
    setCurrent(1);
  }, [dataSource]);

  // 判断是否为默认模板的辅助函数
  const isDefaultTemplate = (value: string) => {
    return value === 'default' || value.includes('默认') || value.includes('default');
  };

  // 判断模板是否为默认模板（只和模板本身有关，和分类无关）
  const isDefaultRecord = (record: ApiTemplate) => {
    return isDefaultTemplate(record.Name); // 只判断模板名
  };

  const showDelete = (id: number) => {
    setDeleteId(id);
  };

  const handleDeleteConfirm = () => {
    if (deleteId !== null) {
      onDelete(deleteId);
    }
    setDeleteId(null);
  };

  const handleDeleteCancel = () => {
    setDeleteId(null);
  };

  const handleTablePaginationChange = (page: number) => {
    setCurrent(page);
  };

  const paginatedDataSource = useMemo(() => {
    if (!dataSource) return [];
    const start = (current - 1) * DEFAULT_PAGE_SIZE;
    const end = start + DEFAULT_PAGE_SIZE;
    return dataSource.slice(start, end);
  }, [dataSource, current]);

  // 设置表格行高为 50px
  const tableComponents = {
    header: {
      row: (props: any) => <tr {...props} style={{ height: 50 }} />,
    },
    body: {
      row: (props: any) => <tr {...props} style={{ height: 50 }} />,
    },
  };

  const columns: ColumnsType<ApiTemplate> = [
    {
      title: '报告模板名称',
      dataIndex: 'Name',
      key: 'Name',
      width: '20%',
      ellipsis: true,
    },
    {
      title: '模板分类',
      dataIndex: 'TypeName',
      key: 'TypeName',
      width: '20%',
      ellipsis: true,
    },
    {
      title: '创建人',
      dataIndex: 'Creator',
      key: 'Creator',
      width: '20%',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'CreateTime',
      key: 'CreateTime',
      width: '20%',
      ellipsis: true,
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      key: 'action',
      width: '20%',
      align: 'center',
      render: (_, record) => (
        <Space size="middle">
          <a style={{ color: '#1F69B4' }} onClick={() => onEdit(record, isDefaultRecord(record))}><CustomIcon type="icon-bianji" style={{ marginRight: 4 }} /> 编辑</a>
          <a
            style={{
              color: isDefaultRecord(record) ? '#999' : '#1F69B4',
              cursor: isDefaultRecord(record) ? 'not-allowed' : 'pointer',
            }}
            onClick={(e) => e.preventDefault()}
          >
            {isDefaultRecord(record) ? (
              <><CustomIcon type="icon-shanchu" style={{ marginRight: 4 }} /> 删除</>
            ) : (
              <span onClick={() => showDelete(record.Id)}><CustomIcon type="icon-shanchu2" style={{ marginRight: 4 }} /> 删除</span>
            )}
          </a>
        </Space>
      ),
    },
  ];

  return (
    <ConfigProvider
      theme={{
        components: {
          // Input: {
          //   activeBg: '#262628',
          //   hoverBg: '#262628',
          // },
          Pagination: {
            itemBg: 'transparent',
            itemActiveBg: '#1F69B4',
            itemSize: 24,
          },
        },
      }}
    >
      <div style={{ paddingTop: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 24 }}>
          <Input
            className={styles.searchInput}
            value={searchItem}
            placeholder="请输入关键词搜索"
            allowClear
            prefix={
              <SearchOutlined
                onClick={() => onTableChange({ ...pagination, SearchItem: searchItem })}
              />
            }
            onChange={(e) => setSearchItem(e.target.value)}
            onPressEnter={() => {
              if (!searchItem) return;
              onTableChange({ ...pagination, SearchItem: searchItem });
            }}
            onClear={() => {
              setSearchItem('');
              onTableChange({ ...pagination, SearchItem: '' });
            }}
          />
          <Space>
            <Button onClick={onImport} icon={<CustomIcon type='icon-daorumoban' />}>导入模板</Button>
            <Button type="primary" onClick={onNewTemplate} icon={<CustomIcon type='icon-xinjianmoban-01' />}>新建模板</Button>
          </Space>
        </div>
        <Table
          columns={columns}
          dataSource={paginatedDataSource}
          rowKey="Id"
          pagination={false}
          loading={loading}
          components={tableComponents}
        />
        <Flex style={{ marginTop: '18px' }} align='center'>
          <Pagination
            current={current}
            total={dataSource.length}
            pageSize={DEFAULT_PAGE_SIZE}
            showTotal={(total) => <div>共 {total} 条</div>}
            itemRender={(page, type: 'page' | 'prev' | 'next' | 'jump-prev' | 'jump-next', originalElement) => {
              if (type === 'page') {
                return <div>{page}</div>;
              }
              return originalElement;
            }}
            onChange={handleTablePaginationChange}
          />
          <Space style={{ marginLeft: '16px' }} size={12}>
            前往
            <InputNumber
              style={{ width: 50, backgroundColor: '#262628' }}
              min={1}
              max={Math.ceil(dataSource.length / DEFAULT_PAGE_SIZE)}
              precision={0}
              changeOnBlur
              controls={false}
              onKeyPress={(e) => !/^\d$/.test(e.key) && e.preventDefault()}
              onPressEnter={(e) => {
                const value = (e?.target as HTMLInputElement)?.value;
                const pageNum = value ? Number(value) : 1;
                if (pageNum > Math.ceil(dataSource.length / DEFAULT_PAGE_SIZE)) return;
                handleTablePaginationChange(pageNum);
              }}
            />
            页
          </Space>
        </Flex>
        <DeleteConfirmModal
          visible={deleteId !== null}
          onConfirm={handleDeleteConfirm}
          onCancel={handleDeleteCancel}
        />
      </div>
    </ConfigProvider>
  );
};

export default MyTemplates; 