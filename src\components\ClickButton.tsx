/**
 * 图片与文字组合而成的可点击按钮
 *
 */
import React from 'react';
import { Image } from 'antd';

interface ClickButton {
  text: string;
  img: string;
  img_width: string | number;
  // 点击事件
  onClick: () => void;
}

const ClickButton: React.FC<ClickButton> = ({
  text,
  img,
  img_width,
  onClick,
}) => {
  return (
    <div
      style={{ userSelect: 'none', height: img_width }}
      onMouseOver={(e) => {
        // 使鼠标变为选择状态
        (e.target as HTMLElement).style.cursor = 'pointer';
      }}
      onClick={onClick}
    >
      <Image src={img} preview={false} width={img_width} />
      <span
        style={{
          color: 'white',
          marginLeft: 10,
          fontWeight: 'bold',
          fontSize: 16,
          height: img_width,
        }}
      >
        {text}
      </span>
    </div>
  );
};

export default ClickButton;
