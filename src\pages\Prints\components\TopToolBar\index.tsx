import { Dispatch, useEffect, useState } from 'react';
import { Select, Slider, Dropdown, ConfigProvider } from 'antd';
import { connect } from 'umi';
import { PrintType } from '@/models/prints';
import styles from './index.less';
import { DefaultWLWWSelect, ColorTableSelect } from '@/utils/consts';

interface ITopToolBarProps {
  dispatch: Dispatch<any>;
  originConcentration?: string; //PET图片原始浓度
  concentrationCallBack?: () => any; //浓度变化回调
  patientName?: string;
}

const concentrationUnits = [
  { value: 'mg/mL', label: 'mg/mL' },
  // 其他可能的浓度单位...
];

const TopToolBar: React.FC<ITopToolBarProps> = (props) => {
  const { dispatch, originConcentration, concentrationCallBack, patientName = '-' } = props;
  const [currentValue, setCurrentValue] = useState(Number(originConcentration) || 0);
  const [colorKey, setColorKey] = useState('normal');
  const [maxConcentration, setMaxConcentration] = useState(1);
  const [unit, setUnit] = useState('mg/mL');

  useEffect(() => {
    concentrationCallBack?.();
  }, [currentValue, maxConcentration, unit]);

  useEffect(() => {
    // 弃用
    // dispatch({
    //   type: 'prints/getCurrentPatient',
    //   payload: { Version: 20230101, PatientId: 158 },
    // });
  }, []);

  const onSliderChange = (value: number) => {
    setCurrentValue(value);
  };

  const onMaxConcentrationChange = (value: number) => {
    setMaxConcentration(value);
  };
  const onUnitChange = (value: string) => {
    setUnit(value);
    // 如果需要在单位改变时做特殊处理，可以在这里添加逻辑
  };

  return (
    <div className={styles.functionPoints}>
      <ConfigProvider
        theme={{
          token: {
            colorTextBase: 'white',
          },
          components: {
            Select: {
              colorText: 'white',
              colorBgContainer: '#404040',
              colorBgElevated: '#404040',
              colorTextBase: 'white',
              colorBorder: 'white',
              optionActiveBg: '#3a3a3a',
              borderRadius: 7,
              optionSelectedBg: '#3a3a3a',
              controlOutline: 'none',
            },
          },
        }}
      >
        {/* <span style={{ fontSize: 24 }}>{'Name'}</span> */}
        <span style={{ fontSize: 24, width: 140, marginLeft: 10 }}>{patientName}</span>
        <Dropdown
          trigger={['click']}
          menu={{
            items: ColorTableSelect.map((item) => ({
              key: item.value,
              label: (
                <div
                  className={styles.colorDropdownItem}
                  style={{
                    backgroundImage: 'url(../' + item.path + ')',
                  }}
                />
              ),
            })),
            onClick: ({ key }) => {
              setColorKey(key);
            },
          }}
          placement='bottomRight'
        >
          <div
            className={styles.colorDropdown}
            style={
              {
                // backgroundImage: `url(../${ColorTableSelect.find((item) => item.value === colorKey)?.path})`,
              }
            }
          />
        </Dropdown>
        {'CT' && <Select style={{ width: 160 }} defaultValue={DefaultWLWWSelect[0].value} options={DefaultWLWWSelect} />}
        {/* {'CT' && (
        <>
          <Slider
            className={styles.concentrationSlider}
            tooltip={{ formatter: (value) => `${value}%` }}
            min={0}
            max={100}
            value={Number(currentValue)}
            onChange={onSliderChange}
          />
          <Select
            style={{ width: 80 }}
            value={maxConcentration}
            onChange={onMaxConcentrationChange}
            options={[...Array(10)].fill(null).map((_, index) => ({
              value: index + 1,
              label: String(index + 1),
            }))}
          />
          <Select
            style={{ width: 100 }}
            value={unit}
            options={concentrationUnits}
            onChange={onUnitChange}
          />
        </>
      )} */}
      </ConfigProvider>
    </div>
  );
};

const mapStateToProps = ({ prints }: { prints: PrintType }) => {
  return { patientName: prints.patientName };
};

export default connect(mapStateToProps)(TopToolBar);
