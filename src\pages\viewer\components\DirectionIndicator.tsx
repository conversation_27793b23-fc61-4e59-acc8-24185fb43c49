import React from 'react';

interface DirectionIndicatorProps {
  visible?: boolean;
  style?: React.CSSProperties;
  orientation?: 'axial' | 'sagittal' | 'coronal'; // 图像方向
}

const DirectionIndicator: React.FC<DirectionIndicatorProps> = ({ visible = true, style, orientation = 'axial' }) => {
  // 如果不可见，则不渲染
  if (!visible) {
    return null;
  }

  // 根据不同的图像方向定义不同的方向标识
  const getDirectionLabels = () => {
    switch (orientation) {
      case 'axial': // 轴位图
        return {
          top: 'A', // 前 (Anterior)
          bottom: 'P', // 后 (Posterior)
          left: 'R', // 右 (Right) - 在轴位图中，患者的右侧显示在图像的左侧
          right: 'L', // 左 (Left)
        };
      case 'sagittal': // 矢状位图
        return {
          top: 'S', // 上 (Superior)
          bottom: 'I', // 下 (Inferior)
          left: 'A', // 前 (Anterior)
          right: 'P', // 后 (Posterior)
        };
      case 'coronal': // 冠状位图
        return {
          top: 'S', // 上 (Superior)
          bottom: 'I', // 下 (Inferior)
          left: 'R', // 右 (Right)
          right: 'L', // 左 (Left)
        };
      default:
        return {
          top: 'A',
          bottom: 'P',
          left: 'R',
          right: 'L',
        };
    }
  };

  const directions = getDirectionLabels();

  const baseDirectionStyle: React.CSSProperties = {
    position: 'absolute',
    color: 'white',
    fontSize: '14px',
    fontWeight: 'bold',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    padding: '2px 6px',
    borderRadius: '2px',
    zIndex: 15,
    userSelect: 'none',
    pointerEvents: 'none',
  };

  const overlayStyle: React.CSSProperties = {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    pointerEvents: 'none',
    zIndex: 15,
    ...style,
  };

  return (
    <div style={overlayStyle}>
      {/* 上方 */}
      <div
        style={{
          ...baseDirectionStyle,
          top: '8px',
          left: '50%',
          transform: 'translateX(-50%)',
        }}
      >
        {directions.top}
      </div>

      {/* 下方 */}
      <div
        style={{
          ...baseDirectionStyle,
          bottom: '8px',
          left: '50%',
          transform: 'translateX(-50%)',
        }}
      >
        {directions.bottom}
      </div>

      {/* 左侧 */}
      <div
        style={{
          ...baseDirectionStyle,
          top: '50%',
          left: '8px',
          transform: 'translateY(-50%)',
        }}
      >
        {directions.left}
      </div>

      {/* 右侧 */}
      <div
        style={{
          ...baseDirectionStyle,
          top: '50%',
          right: '8px',
          transform: 'translateY(-50%)',
        }}
      >
        {directions.right}
      </div>
    </div>
  );
};

export default DirectionIndicator;
