import React, { useEffect } from 'react';
import { Row, Col } from 'antd';
import { ImageViewer, PatientLoader, updateViewNum } from 'rayplus-three-view';

// 阅片界面的组件
interface ImgViewer {
  layout?: number;
  pid?: number; //病人ID
}

const userAuths = {
  can_ListSelfSeries: true,
  can_ListResearchCenterSeries: true,
  can_ListAllSeries: true,
  can_UploadDicom: true,
  can_DownloadDicom: true,
  can_LookUpFilm: true,
  can_EditFilm: true,
  can_Registration: true,
  can_ROIAnalyse: true,
  can_ThreeDimension: true,
  can_FourDimension: true,
};

const ImgViewer: React.FC<ImgViewer> = ({ pid }) => {
  localStorage.setItem('userAuths', JSON.stringify(userAuths));

  useEffect(() => {
    updateViewNum(1);

    return () => {};
  }, []);

  return (
    <>
      <div
        style={{
          width: '100%',
          height: 'calc(100% - 72px)',
          backgroundColor: '#4a4a4a',
          overflowX: 'auto',
          overflowY: 'auto',
        }}
      >
        <PatientLoader patient_uuid={pid} />
        <ImageViewer userAuths={userAuths} />
      </div>
    </>
  );
};
export default ImgViewer;
