import * as cornerstoneTools from '@cornerstonejs/tools';
import { getEnabledElement } from '@cornerstonejs/core';
import type { Types } from '@cornerstonejs/core';

const { annotation, Enums: csToolsEnums } = cornerstoneTools;

const { MouseBindings } = csToolsEnums;
const { state } = annotation;

export interface TextAnnotation {
  annotationUID: string;
  data: {
    text: string;
    handles: {
      points: Types.Point3[];
    };
  };
  metadata: {
    toolName: string;
  };
  invalidated: boolean;
}

// 简化的文本标注工具
export class TextTool {
  static toolName = 'Text';

  private isDrawing: boolean = false;
  private startPoint: { x: number; y: number } | null = null;
  private textBoxElement: HTMLTextAreaElement | null = null;
  private currentRect: { x: number; y: number; width: number; height: number } | null = null;
  private textAnnotations: Array<{
    x: number;
    y: number;
    width: number;
    height: number;
    text: string;
    element: HTMLElement;
  }> = [];

  getToolName(): string {
    return TextTool.toolName;
  }

  handleMouseDown = (evt: MouseEvent): void => {
    evt.preventDefault();
    evt.stopPropagation();

    const canvas = evt.target as HTMLCanvasElement;
    if (!canvas || canvas.tagName !== 'CANVAS') return;

    const rect = canvas.getBoundingClientRect();
    const x = evt.clientX - rect.left;
    const y = evt.clientY - rect.top;

    this.isDrawing = true;
    this.startPoint = { x, y };
    this.currentRect = { x, y, width: 0, height: 0 };

    // 开始绘制矩形预览
    this.startDrawingPreview(canvas);
  };

  handleMouseDrag = (evt: MouseEvent): void => {
    if (!this.isDrawing || !this.startPoint) return;

    const canvas = evt.target as HTMLCanvasElement;
    if (!canvas || canvas.tagName !== 'CANVAS') return;

    const rect = canvas.getBoundingClientRect();
    const currentX = evt.clientX - rect.left;
    const currentY = evt.clientY - rect.top;

    // 更新当前矩形
    this.currentRect = {
      x: Math.min(this.startPoint.x, currentX),
      y: Math.min(this.startPoint.y, currentY),
      width: Math.abs(currentX - this.startPoint.x),
      height: Math.abs(currentY - this.startPoint.y),
    };

    // 更新预览
    this.updateDrawingPreview(canvas);
  };

  handleMouseUp = (evt: MouseEvent): void => {
    if (!this.isDrawing || !this.currentRect || !this.startPoint) return;

    this.isDrawing = false;

    const canvas = evt.target as HTMLCanvasElement;
    if (!canvas || canvas.tagName !== 'CANVAS') return;

    // 清除预览
    this.clearDrawingPreview(canvas);

    // 如果矩形太小，不创建输入框
    if (this.currentRect.width < 20 || this.currentRect.height < 20) {
      this.currentRect = null;
      this.startPoint = null;
      return;
    }

    // 创建文本输入框
    this.createTextInput(canvas.parentElement as HTMLElement, this.currentRect);
  };

  private startDrawingPreview(canvas: HTMLCanvasElement): void {
    // 添加一个临时的overlay来显示正在绘制的矩形
    this.addDrawingOverlay(canvas);
  }

  private updateDrawingPreview(canvas: HTMLCanvasElement): void {
    if (!this.currentRect) return;

    const overlay = canvas.parentElement?.querySelector('.text-drawing-overlay') as HTMLDivElement;
    if (overlay) {
      overlay.style.left = `${this.currentRect.x}px`;
      overlay.style.top = `${this.currentRect.y}px`;
      overlay.style.width = `${this.currentRect.width}px`;
      overlay.style.height = `${this.currentRect.height}px`;

      // 添加控制点效果
      this.addControlPoints(overlay, this.currentRect);
    }
  }

  private addControlPoints(overlay: HTMLDivElement, rect: { width: number; height: number }): void {
    // 先移除已存在的控制点
    overlay.querySelectorAll('.control-point').forEach((point) => point.remove());

    // 只在矩形足够大时显示控制点
    if (rect.width > 40 && rect.height > 20) {
      const points = [
        { x: -4, y: -4 }, // 左上
        { x: rect.width - 4, y: -4 }, // 右上
        { x: -4, y: rect.height - 4 }, // 左下
        { x: rect.width - 4, y: rect.height - 4 }, // 右下
        { x: rect.width / 2 - 4, y: -4 }, // 上中
        { x: rect.width / 2 - 4, y: rect.height - 4 }, // 下中
        { x: -4, y: rect.height / 2 - 4 }, // 左中
        { x: rect.width - 4, y: rect.height / 2 - 4 }, // 右中
      ];

      points.forEach((point) => {
        const controlPoint = document.createElement('div');
        controlPoint.className = 'control-point';
        controlPoint.style.cssText = `
          position: absolute;
          left: ${point.x}px;
          top: ${point.y}px;
          width: 8px;
          height: 8px;
          background: #00ff41;
          border: 1px solid #ffffff;
          border-radius: 50%;
          pointer-events: none;
          z-index: 1001;
        `;
        overlay.appendChild(controlPoint);
      });
    }
  }

  private clearDrawingPreview(canvas: HTMLCanvasElement): void {
    const overlay = canvas.parentElement?.querySelector('.text-drawing-overlay');
    if (overlay) {
      // 移除控制点
      overlay.querySelectorAll('.control-point').forEach((point) => point.remove());
      // 移除overlay
      overlay.remove();
    }
  }

  private addDrawingOverlay(canvas: HTMLCanvasElement): void {
    const parent = canvas.parentElement;
    if (!parent) return;

    // 移除已存在的overlay
    const existingOverlay = parent.querySelector('.text-drawing-overlay');
    if (existingOverlay) {
      existingOverlay.remove();
    }

    // 创建新的overlay - 黄色虚线边框
    const overlay = document.createElement('div');
    overlay.className = 'text-drawing-overlay';
    overlay.style.cssText = `
      position: absolute;
      border: 2px dashed #ffff00;
      background: rgba(255, 255, 0, 0.05);
      pointer-events: none;
      z-index: 1000;
    `;

    parent.appendChild(overlay);
  }

  private createTextInput(element: HTMLElement, rect: { x: number; y: number; width: number; height: number }): void {
    // 获取元素的位置偏移
    const elementRect = element.getBoundingClientRect();

    // 计算绝对位置
    const absoluteX = elementRect.left + rect.x;
    const absoluteY = elementRect.top + rect.y;

    // 创建文本输入框
    this.textBoxElement = document.createElement('textarea');
    this.textBoxElement.className = 'text-annotation-input';

    // 设置样式
    Object.assign(this.textBoxElement.style, {
      position: 'fixed',
      left: `${absoluteX}px`,
      top: `${absoluteY}px`,
      width: `${Math.max(rect.width, 120)}px`,
      height: `${Math.max(rect.height, 40)}px`,
      border: '2px dashed #ffff00',
      backgroundColor: 'rgba(0, 0, 0, 0.3)',
      color: '#ffffff',
      fontSize: '14px',
      fontFamily: 'Arial, sans-serif',
      resize: 'none',
      outline: 'none',
      opacity: '1',
      padding: '6px',
      borderRadius: '0px',
      overflow: 'auto',
      lineHeight: '1.4',
      zIndex: '10000',
      wordWrap: 'break-word',
      boxShadow: '0 0 10px rgba(255, 255, 0, 0.3)',
    });

    this.textBoxElement.spellcheck = false;
    this.textBoxElement.placeholder = '请输入文本...';

    // 添加到DOM并聚焦
    document.body.appendChild(this.textBoxElement);
    this.textBoxElement.focus();

    // 保存文本的函数
    const saveText = () => {
      if (this.textBoxElement) {
        const text = this.textBoxElement.value.trim();

        if (text) {
          // 保存标注
          this.textAnnotations.push({
            x: rect.x,
            y: rect.y,
            width: rect.width,
            height: rect.height,
            text: text,
            element: element,
          });

          // 创建永久的文本显示
          this.createPermanentTextDisplay(element, rect, text);
        }

        // 移除输入框
        this.textBoxElement.remove();
        this.textBoxElement = null;
      }

      this.currentRect = null;
      this.startPoint = null;
    };

    // 事件监听器
    this.textBoxElement.addEventListener('blur', saveText);
    this.textBoxElement.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        this.textBoxElement?.blur();
      } else if (e.key === 'Escape') {
        // ESC键取消输入
        if (this.textBoxElement) {
          this.textBoxElement.value = '';
          this.textBoxElement.blur();
        }
      }
      e.stopPropagation();
    });

    this.textBoxElement.addEventListener('mousedown', (e) => {
      e.stopPropagation();
    });

    this.textBoxElement.addEventListener('click', (e) => {
      e.stopPropagation();
    });
  }

  private createPermanentTextDisplay(
    element: HTMLElement,
    rect: { x: number; y: number; width: number; height: number },
    text: string
  ): void {
    const textDisplay = document.createElement('div');
    textDisplay.className = 'text-annotation-display';

    Object.assign(textDisplay.style, {
      position: 'absolute',
      left: `${rect.x}px`,
      top: `${rect.y}px`,
      width: `${rect.width}px`,
      height: `${rect.height}px`,
      border: 'none',
      backgroundColor: 'transparent',
      color: '#00ff41',
      fontSize: '14px',
      fontFamily: 'Arial, sans-serif',
      fontWeight: 'bold',
      padding: '6px',
      overflow: 'hidden',
      lineHeight: '1.4',
      wordWrap: 'break-word',
      pointerEvents: 'none',
      zIndex: '999',
      whiteSpace: 'pre-wrap',
      textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)',
    });

    textDisplay.textContent = text;
    element.appendChild(textDisplay);
  }

  // 清理方法 - 只清理临时元素，保留已保存的文本标注
  cleanup(): void {
    // 移除输入框
    if (this.textBoxElement) {
      this.textBoxElement.remove();
      this.textBoxElement = null;
    }

    // 只移除临时的绘制overlay，保留已保存的文本显示
    document.querySelectorAll('.text-drawing-overlay').forEach((el) => {
      // 先移除控制点
      el.querySelectorAll('.control-point').forEach((point) => point.remove());
      // 再移除overlay
      el.remove();
    });

    // 重置绘制状态
    this.isDrawing = false;
    this.startPoint = null;
    this.currentRect = null;

    // 注意：不删除 textAnnotations 和 .text-annotation-display 元素
    // 因为这些是已保存的标注，应该保持显示
  }

  // 完全清理所有内容（包括已保存的标注）- 用于特殊情况
  clearAllAnnotations(): void {
    this.cleanup();

    // 移除所有文本显示
    document.querySelectorAll('.text-annotation-display').forEach((el) => el.remove());

    // 移除所有控制点（如果有残留）
    document.querySelectorAll('.control-point').forEach((el) => el.remove());

    // 清空标注数组
    this.textAnnotations = [];
  }

  // 获取所有标注
  getAnnotations(): typeof this.textAnnotations {
    return this.textAnnotations;
  }

  // 移除最后一个标注
  removeLastAnnotation(): boolean {
    if (this.textAnnotations.length === 0) return false;

    // 移除数组中的最后一个标注
    const lastAnnotation = this.textAnnotations.pop();
    if (!lastAnnotation) return false;

    // 找到对应的显示元素并移除
    const textDisplays = document.querySelectorAll('.text-annotation-display');
    if (textDisplays.length > 0) {
      textDisplays[textDisplays.length - 1].remove();
      return true;
    }

    return false;
  }

  // 获取标注数量
  getAnnotationCount(): number {
    return this.textAnnotations.length;
  }
}

export default TextTool;
