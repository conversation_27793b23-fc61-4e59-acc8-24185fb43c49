import React, { useEffect, useState } from 'react';
import { Select } from 'antd';
import { connect, Dispatch } from 'umi';
import { ViewType } from '@/models/views';
import styles from './index.less';

const { Option } = Select;

interface SeriesControlsProps {
  dispatch: Dispatch;
  views: ViewType;
  seriesId: string;
  seriesLists?: any[];
  onSeriesChange: (seriesId: string) => void;
}

const SeriesControls: React.FC<SeriesControlsProps> = ({
  dispatch,
  views,
  seriesId,
  seriesLists,
  onSeriesChange,
}) => {
  const handleSeriesChange = (value: string) => {
    onSeriesChange(value);
  };
  // 获取当前可用的序列列表，优先使用传入的seriesLists，否则从views中获取
  const seriesList = seriesLists || (views?.seriesLists ? views.seriesLists.filter(item => item.SeriesDescription !== 'injector') : []) || [];

  return (
    <div className={styles.seriesControls}>
      <div className={styles.controlItem}>
        <span className={styles.controlLabel}>序列：</span>
        <Select
          value={seriesId}
          onChange={handleSeriesChange}
          style={{ width: 150 }}
          dropdownStyle={{
            backgroundColor: 'rgba(30, 30, 30, 0.9)',
            color: '#fff',
            borderColor: '#444',
          }}
        >
          {seriesList.map((series) => (
            <Option key={series.SeriesId} value={series.SeriesId}>
              {series.SeriesDescription || series.ProtocolName || '未知序列'}
            </Option>
          ))}
        </Select>
      </div>
    </div>
  );
};

const mapStateToProps = ({ views }: { views: ViewType }) => {
  return {
    views,
  };
};

export default connect(mapStateToProps)(SeriesControls);