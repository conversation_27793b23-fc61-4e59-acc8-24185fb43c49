import React from 'react';
import { Card, Tag } from 'antd';
import { ProDescriptions } from '@ant-design/pro-components';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  SyncOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import styles from '../styles.less';

interface PatientInfoCardProps {
  patientInfo: any;
}

const columns = [
    {
      label: '姓名',
      dataIndex: 'Name',
    },
    {
      label: '性别',
      dataIndex: 'Sex',
    },
    {
      label: '年龄',
      dataIndex: 'Age',
    },
    {
      label: '血糖',
    },
    {
      label: '体重',
      dataIndex: 'Weight',
    },
    {
      label: '检查号',
      dataIndex: 'StudyId',
    },
    {
      label: '放射性药物',
      dataIndex: 'Medicine',
    },
    {
      label: '检查项目',
      dataIndex: 'CheckingProject',
    },
    {
      label: '检查时间',
      dataIndex: 'time', // 后端缺失
    },
    {
      label: '审核状态',
      dataIndex: 'status',
      render: (_: any, record: any) => {
        const status = record.status;
        switch (status) {
          case 0:
            return <Tag icon={<ClockCircleOutlined />} color="default">未审核</Tag>;
          case 1:
            return <Tag icon={<SyncOutlined spin />} color="processing">审核中</Tag>;
          case 2:
            return <Tag icon={<CheckCircleOutlined />} color="success">已通过</Tag>;
          case 3:
            return <Tag icon={<CloseCircleOutlined />} color="error">未通过</Tag>;
          default:
            return <Tag color="default">未知状态</Tag>;
        }
      },
    },
];


const PatientInfoCard: React.FC<PatientInfoCardProps> = ({ patientInfo }) => {
  return (
    <Card
      style={{
        flex: '0 0 auto',
        width: '100%',
        height: '108px',
      }}
      bodyStyle={{
        padding: '12px',
        height: '100%',
        overflow: 'hidden',
      }}
    >
      <ProDescriptions
        className={styles.description}
        dataSource={patientInfo}
        column={5}
        columns={columns}
        size="small"
        labelStyle={{
          fontFamily: 'Microsoft YaHei UI',
          fontWeight: 400,
          fontSize: '16px',
          lineHeight: '20px',
          width: '120px',
          padding: '4px 8px',
        }}
        contentStyle={{
          fontFamily: 'Microsoft YaHei UI',
          fontWeight: 400,
          fontSize: '16px',
          lineHeight: '20px',
          padding: '4px 8px',
        }}
      />
    </Card>
  );
};

export default PatientInfoCard; 