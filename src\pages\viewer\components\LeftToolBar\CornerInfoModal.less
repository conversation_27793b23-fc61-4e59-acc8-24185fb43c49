.cornerInfoModal {
  :global {
    .ant-modal-content {
      background-color: #1e1e1e;
      border: 1px solid #333;
      border-radius: 4px;
    }
    
    .ant-modal-header {
      background-color: #1e1e1e;
      border-bottom: 1px solid #333;
      padding: 10px 16px;
      border-radius: 4px 4px 0 0;
    }
    
    .ant-modal-title {
      color: #fff;
      font-size: 16px;
      font-weight: bold;
    }
    
    .ant-modal-close {
      color: #999;
      
      &:hover {
        color: #fff;
      }
    }
    
    .ant-modal-body {
      background-color: #1e1e1e;
      padding: 16px;
    }
    
    .ant-modal-footer {
      background-color: #1e1e1e;
      border-top: 1px solid #333;
      padding: 10px 16px;
    }
    
    .ant-btn {
      border-radius: 2px;
    }
    
    .ant-btn-default {
      background-color: transparent;
      border: 1px solid #666;
      color: #fff;
      
      &:hover {
        border-color: #1890ff;
        color: #1890ff;
        background-color: transparent;
      }
    }
    
    .ant-btn-primary {
      background-color: #1890ff;
      border: 1px solid #1890ff;
      
      &:hover {
        background-color: #40a9ff;
        border-color: #40a9ff;
      }
    }
    
    .ant-checkbox-wrapper {
      color: #fff;
    }
    
    .ant-checkbox-inner {
      background-color: transparent;
      border-color: #666;
    }
    
    .ant-checkbox-checked .ant-checkbox-inner {
      background-color: #1890ff;
      border-color: #1890ff;
    }
  }
  
  .displaySetting {
    margin-bottom: 15px;
    padding: 10px 16px;
    background-color: #2a2a2a;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 8px;
    
    .settingLabel {
      color: #fff;
      font-weight: bold;
    }
    
    .displayButton {
      color: #1890ff;
      background: none;
      border: none;
      display: flex;
      align-items: center;
      gap: 4px;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 2px;
      
      &:hover {
        background-color: rgba(24, 144, 255, 0.1);
      }
      
      &.hidden {
        color: #999;
      }
    }
  }
  
  .configTitle {
    font-weight: bold;
    margin-bottom: 20px;
    font-size: 16px;
    text-align: center;
    color: #fff;
  }
  
  .configContainer {
    display: flex;
    gap: 10px;
    margin-top: 10px;
  }
  
  .configSection {
    flex: 1;
    
    .sectionTitle {
      font-weight: bold;
      margin-bottom: 10px;
      font-size: 14px;
      text-align: center;
      color: #1F69B4;
    }
    
    .cornerGrid {
      display: flex;
      flex-direction: column;
      gap: 0;
      min-height: 280px;
      
      .gridRow {
        display: flex;
        gap: 0;
        flex: 1;
      }
      
      .cornerBox {
        flex: 1;
        background-color: #2a2a2a;
        padding: 10px;
        position: relative;
        
        &.topLeft {
          border-radius: 4px 0 0 0;
        }
        
        &.topRight {
          border-radius: 0 4px 0 0;
        }
        
        &.bottomLeft {
          border-radius: 0 0 0 4px;
        }
        
        &.bottomRight {
          border-radius: 0 0 4px 0;
        }
        
        .cornerLabel {
          font-size: 11px;
          color: #999;
          margin-bottom: 6px;
          font-weight: bold;
        }
        
        .checkboxList {
          display: flex;
          flex-direction: column;
          gap: 4px;
          
          .checkboxItem {
            margin: 0;
            font-size: 12px;
            
            span {
              font-size: 12px;
              color: #fff;
            }
          }
        }
      }
    }
  }
}

.closeIcon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 28px;
  height: 28px;
  color: #ffffff;
  font-weight: 600;
  border-radius: 50%;
  background-color: #D92E2D;
}
// 弹出框 样式重新
.modalHeader {
  padding: 12px 20px!important;
  font-weight: 400!important;
  background-color: #4C4C4C!important;
}
.modalContent {
  color: #ffffff;
  padding: 0 0 24px!important;
  background-color: #333233!important;
}
.modalBody {
  padding:16px 24px 0!important;
}
.modalFooter {
  border-radius: 10px!important;
  margin: 0px 16px;
}