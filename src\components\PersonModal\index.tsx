import React, { useState ,type Dispatch } from 'react';
import { connect } from 'dva';
import { ConfigProvider, Modal, Flex, Avatar, Tag, Space, Form, Input, Tooltip , Button, message,  } from 'antd';
import { ExclamationCircleOutlined, UserOutlined, CloseOutlined } from '@ant-design/icons';
import { UpdateUserInfo } from '@/services/user';
import { LoginType, UserInfoType } from '@/types/user';
import styles from './index.less';


type FormType =  {
  Pwd?: string; // 密码，非必须
  NewPwd?: string; // 新密码，非必须
  ConfirmPwd?: string; // 确认密码，非必须
}

// 新增 userInfo 到接口定义
export interface PersonModalProps {
  isModalOpen: boolean;
  handleOk: (val: boolean) => void;
  handleCancel: (val: boolean) => void;
  userInfo: UserInfoType; // 从 dva 连接获取的用户信息
  dispatch: Dispatch;
}
// 修复类型不匹配问题，明确指定 React.FC 的泛型为 PersonModalProps
const PersonModal: React.FC<PersonModalProps> = ({  isModalOpen, handleOk, handleCancel, userInfo, dispatch }) => { 
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [form] = Form.useForm();
  // 确定
  const onOk = () => { 
    form.validateFields().then(async (values) => {
      setConfirmLoading(true);
      let params = { Id: userInfo.Id,Phone: userInfo.Phone,...values };
      try {
        await UpdateUserInfo(params);
        message.success('修改密码成功');
        handleOk(false);
      } catch (error) {
        console.error('Error:', error);
      } finally {
        form.resetFields();
        setConfirmLoading(false);
        setTimeout(() => {
          dispatch({
            type: 'user/logout',
          });
          message.success('请重新登录！')
        }, 500);
      }
    });
  };
  // 取消
  const onCancel = () => { 
    form.resetFields();
    handleCancel(false);
  };
  return (
    <ConfigProvider
      theme={{
        components: {
          Form: {
            fontSize: 16,
            colorPrimary: '#474645',
            labelFontSize: 16,
            labelColor: '#ffffff',
            itemMarginBottom: 24,
            verticalLabelPadding: '0 0 4px',
          },
        },
      }}
    >
      <Modal
        classNames={{
          header: styles.modalHeader,
          content: styles.modalContent,
          body: styles.modalBody,
          footer: styles.modalFooter,
        }}
        title="个人中心"
        centered
        width={1300}
        open={isModalOpen}
        onOk={onOk}
        onCancel={onCancel}
        confirmLoading={confirmLoading}
        closeIcon={<div className={styles.closeIcon}><CloseOutlined /></div>}
      >
        <Flex vertical justify='center' align='center'>
          <Avatar style={{ backgroundColor: '#1F69B4' }} size={94} icon={<UserOutlined />} />
          <div className={styles.info}>
            <Flex className={styles.name} justify='center'>
              {/* 使用 userInfo 中的姓名替换硬编码的姓名 */}
              <Tooltip title={userInfo?.Name}> 
                <span className={styles.ellipsis300}>姓名：{userInfo?.Name || ''} </span>
              </Tooltip>
              <Tag color="success">{ userInfo?.Role == 'check' ? '主任医生' : userInfo?.Role == 'doctor' ? '诊断医生' : '超级管理员' }</Tag>
            </Flex>
            <Flex justify='space-evenly' gap={160}>
              <Tooltip title={userInfo?.Number}>
                <span className={styles.ellipsis300}>员工编号/工号：{userInfo?.Number || ''}</span>
              </Tooltip>
              <span>手机号：{userInfo?.Phone ? userInfo.Phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : ''}</span>
            </Flex>
          </div>
        </Flex>
        <div className={styles.title}>修改密码</div>
        <Form<FormType>
          layout="vertical"
          autoComplete="off"
          form={form}
        >
          <Form.Item
            label="当前密码"
            name="Pwd"
            rules={[{ required: true, whitespace: true, message: '请输入当前密码' },{ min: 6, message: '密码长度不能小于6位' }]}
          >
            <Input.Password placeholder='请输入当前密码' maxLength={6} allowClear />
          </Form.Item>
          <Form.Item
            label="新密码"
            name="NewPwd"
            rules={[{ required: true, whitespace: true, message: '请输入新密码' },{ min: 6, message: '密码长度不能小于6位' }]}
          >
            <Input.Password placeholder='请输入新密码' maxLength={6} allowClear />
          </Form.Item>
          <Form.Item
            label="确认密码"
            name="ConfirmPwd"
            dependencies={['NewPwd']}
            rules={[
              { required: true, whitespace: true, message: '请输入密码!' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('NewPwd') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次密码不一致!'));
                },
              }),
            ]}
          >
            <Input.Password placeholder='请确认密码' maxLength={6} allowClear />
          </Form.Item>
        </Form>
      </Modal>
    </ConfigProvider>
  );
};

// 定义 mapStateToProps 函数，从 dva 的 login 模块中获取 userInfo
const mapStateToProps = ({user}: { user: LoginType }) => {
  return {
    userInfo: user.userInfo
  };
};

// 使用 connect 连接组件和状态
export default connect(mapStateToProps)(PersonModal);
