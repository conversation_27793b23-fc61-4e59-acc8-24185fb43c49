import React, { useState, useEffect } from 'react';
import { ConfigProvider, Modal, Flex, Avatar, Space, Form, Input, Select, Button, message } from 'antd';
import { ExclamationCircleOutlined, UserOutlined, CloseOutlined } from '@ant-design/icons';
import { AddUser, UpdateUserInfo } from '@/services/user';
import { UserInfoType } from '@/types/user';
import styles from './index.less';

type FormType = UserInfoType & {
  CreateUser?: string; // 创建人，非必须
  ConfirmPwd?: string; // 确认密码，非必须
};

export interface UserInfoModalProps {
  visible: boolean;
  initial?: UserInfoType;
  creater?: string;
  createrId?: string;
  handleOk: (val: boolean) => void;
  handleCancel: (val: boolean) => void;
}
// 修复类型不匹配问题，明确指定 React.FC 的泛型为 UserInfoModalProps
const UserInfoModal: React.FC<UserInfoModalProps> = ({
  visible,
  creater = '',
  createrId = '',
  initial = {
    Id: '',
    Name: '',
    Number: '',
    Role: 'doctor',
    Email: '',
    Phone: '',
  },
  handleOk,
  handleCancel,
}) => {

  const [form] = Form.useForm();

  // 当 initial 值改变时，更新表单的值
  useEffect(() => {
    form.setFieldsValue(initial);
  }, [initial]);

  // 确定
  const onOk = () => {
    form.validateFields().then(async (values) => {
      let params = {
        ...values,
      };
      delete params.ConfirmPwd;
      if (initial.Id) {
        params.Id = initial.Id;
      } else {
        params.CreateId = createrId;
        params.CreateUser = creater;
      }
      try {
        if (initial?.Id) {
          await UpdateUserInfo(params);
        } else {
          await AddUser(params);
        }
        message.success(`${initial?.Id ? '编辑' : '新增'}用户成功`);
        form.resetFields();
        handleOk(false);
      } catch (error) {
        console.error('Error:', error);
      } finally {
      }
    });
  };
  // 取消
  const onCancel = () => {
    handleCancel(false);
    form.resetFields();
  };
  return (
    <ConfigProvider
      theme={{
        components: {
          Form: {
            fontSize: 16,
            colorPrimary: '#474645',
            labelFontSize: 16,
            labelColor: '#ffffff',
            itemMarginBottom: 24,
            verticalLabelPadding: '0 0 4px',
          },
          Select: {
            colorBgContainer: '#333233',
            colorText: 'white',
            colorTextPlaceholder: 'rgba(255, 255, 255, 0.45)',
            colorBgElevated: '#333233',
            controlItemBgHover: '#555555',
            optionSelectedBg: '#1F69B4',
          },
        },
      }}
    >
      <Modal
        classNames={{
          header: styles.modalHeader,
          content: styles.modalContent,
          body: styles.modalBody,
          footer: styles.modalFooter,
        }}
        title={initial?.Id ? '编辑用户' : '新增用户'}
        centered
        width={1300}
        open={visible}
        onOk={onOk}
        onCancel={onCancel}
        closeIcon={
          <div className={styles.closeIcon}>
            <CloseOutlined />
          </div>
        }
        footer={(_, { OkBtn, CancelBtn }) => (
          <>
            <CancelBtn />
            <OkBtn />
          </>
        )}
      >
        <Form<FormType> form={form} layout='vertical' autoComplete='off'>
          <Form.Item label='姓名' name='Name' rules={[{ required: true, whitespace: true, message: '请输入姓名!' }]}>
            <Input placeholder='请输入姓名' maxLength={99} allowClear />
          </Form.Item>
          <Form.Item label='员工编号/工号' name='Number' rules={[{ whitespace: true, message: '请输入员工编号/工号!' }]}>
            <Input placeholder='请输入员工编号/工号' maxLength={99} allowClear />
          </Form.Item>
          <Form.Item
            label='手机号'
            name='Phone'
            rules={[
              { required: true, whitespace: true, message: '请输入手机号!' },
              {
                pattern: /^1[3-9]\d{9}$/, // 支持+86/86前缀
                message: '请输入正确的手机号',
              },
            ]}
          >
            <Input placeholder='请输入手机号' allowClear />
          </Form.Item>
          <Form.Item label='角色' name='Role' rules={[{ required: true, message: '请选择角色!' }]}>
            <Select
              options={[
                { value: 'check', label: '主任医生' },
                { value: 'doctor', label: '诊断医生' },
              ]}
            />
          </Form.Item>
          <Form.Item
            label='密码'
            name={initial?.Id ? 'NewPwd' : 'Pwd'}
            rules={[
              { required: initial?.Id ? false : true, whitespace: true, message: '请输入密码' },
              { min: 6, message: '密码长度不能小于6位' },
            ]}
          >
            <Input.Password placeholder='请输入密码' maxLength={6} allowClear />
          </Form.Item>
          <Form.Item
            label='确认密码'
            name='ConfirmPwd'
            dependencies={[initial?.Id ? 'NewPwd' : 'Pwd']}
            rules={[
              { required: initial?.Id ? false : true, whitespace: true, message: '请输入密码!' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue(initial?.Id ? 'NewPwd' : 'Pwd') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次密码不一致!'));
                },
              }),
            ]}
          >
            <Input.Password placeholder='请确认密码' maxLength={6} allowClear />
          </Form.Item>
        </Form>
      </Modal>
    </ConfigProvider>
  );
};

export default UserInfoModal;
