import React, { useEffect, useMemo, useState } from 'react';
import { Button, Table, Space, Input, Pagination, InputNumber, Flex, ConfigProvider } from 'antd';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import dayjs from 'dayjs';
import { connect, Dispatch } from 'umi';
import { SearchOutlined } from '@ant-design/icons';
import CustomIcon from '@/components/CustomIcon';
import type { ApiGroupCategory, ApiGroupTemplate } from '../data';
import { TemplateManagementState } from '../model';
import DeleteConfirmModal from './DeleteConfirmModal';
import storeUtil from '@/utils/store';
import { LoginType } from '@/types/user';
import { DEFAULT_PAGE_SIZE } from '@/types/index';
import styles from './styles.less';

interface GroupCategoriesProps {
  onNewCategory: () => void;
  onEditCategory: (category: ApiGroupCategory) => void;
  onDeleteCategory: (id: number) => void;
  templates: ApiGroupTemplate[];
  dispatch: Dispatch;
  categories: ApiGroupCategory[];
  loading: boolean;
  pagination: TablePaginationConfig;
  userInfo: any;
}

const GroupCategories: React.FC<GroupCategoriesProps> = ({
  onNewCategory,
  onEditCategory,
  onDeleteCategory,
  templates,
  dispatch,
  categories,
  loading,
  pagination,
  userInfo,
}) => {
  const [deleteId, setDeleteId] = useState<number | null>(null);
  const [current, setCurrent] = useState<number>(1);
  const [searchItem, setSearchItem] = useState('');

  // 在搜索或数据源变化时重置页码
  useEffect(() => {
    setCurrent(1);
  }, [categories]);

  // 判断是否为默认分类的辅助函数
  const isDefaultCategory = (name: string) => {
    return name === 'default' || name.includes('默认') || name.includes('default');
  };

  // 判断当前用户是否为诊断医生
  const isDoctorRole = () => {
    const role = userInfo?.Role || storeUtil.get('Role')?.value;
    return role === 'doctor';
  };

  // 判断是否禁用操作（默认分类或诊断医生对团队分类的限制）
  const isOperationDisabled = (category: ApiGroupCategory) => {
    return isDefaultCategory(category.Name) || isDoctorRole();
  };

  useEffect(() => {
    dispatch({
      type: 'templateManagement/fetchGroupCategories',
      payload: { pagination: { ...pagination, SearchItem: '' } }, // 初始加载时清空搜索条件
    });
  }, [dispatch]);

  const handleSearch = (value: string) => {
    dispatch({
      type: 'templateManagement/fetchGroupCategories',
      payload: { pagination: { ...pagination, SearchItem: value } },
    });
  };

  const handlePageChange = (page: number) => {
    setCurrent(page);
  };

  const paginatedDataSource = useMemo(() => {
    const start = (current - 1) * DEFAULT_PAGE_SIZE;
    const end = start + DEFAULT_PAGE_SIZE;
    return categories.slice(start, end);
  }, [categories, current]);

  const columns: ColumnsType<ApiGroupCategory> = [
    {
      title: '模板分类名称',
      dataIndex: 'Name',
      key: 'Name',
      width: '20%',
      ellipsis: true,
    },
    {
      title: '创建人',
      dataIndex: 'Creator',
      key: 'Creator',
      width: '20%',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'CreateTime',
      key: 'CreateTime',
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      width: '20%',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => {
        const isDisabled = isOperationDisabled(record);
        return (
          <Space size='middle'>
            <a
              style={{
                color: isDisabled ? '#999' : '#1F69B4',
                cursor: isDisabled ? 'not-allowed' : 'pointer',
              }}
              onClick={() => !isDisabled && onEditCategory(record)}
            >
              <CustomIcon type={isDisabled ? 'icon-bianji2' : 'icon-bianji'} style={{ marginRight: 4 }} /> 编辑
            </a>
            <a
              style={{
                color: isDisabled ? '#999' : '#1F69B4',
                cursor: isDisabled ? 'not-allowed' : 'pointer',
              }}
              onClick={(e) => e.preventDefault()}
            >
              {isDisabled ? (
                <>
                  <CustomIcon type='icon-shanchu' style={{ marginRight: 4 }} /> 删除
                </>
              ) : (
                <span onClick={() => showDelete(record.Id)}>
                  <CustomIcon type='icon-shanchu2' style={{ marginRight: 4 }} /> 删除
                </span>
              )}
            </a>
          </Space>
        );
      },
      width: '20%',
      align: 'center',
    },
  ];

  const tableComponents = {
    header: {
      row: (props: any) => <tr {...props} style={{ height: 50 }} />,
    },
    body: {
      row: (props: any) => <tr {...props} style={{ height: 50 }} />,
    },
  };

  const showDelete = (id: number) => setDeleteId(id);
  const handleDeleteConfirm = () => {
    if (deleteId !== null) onDeleteCategory(deleteId);
    setDeleteId(null);
  };
  const handleDeleteCancel = () => setDeleteId(null);

  return (
    <ConfigProvider
      theme={{
        components: {
          // Input: {
          //   activeBg: '#262628',
          //   hoverBg: '#262628',
          // },
          Pagination: {
            itemBg: 'transparent',
            itemActiveBg: '#1F69B4',
            itemSize: 24,
          },
        },
      }}
    >
      <div style={{ paddingTop: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 24 }}>
          <Input
            className={styles.searchInput}
            value={searchItem}
            placeholder='请输入关键词搜索'
            allowClear
            prefix={
              <SearchOutlined
                onClick={() => {
                  handleSearch(searchItem);
                }}
              />
            }
            onChange={(e) => setSearchItem(e.target.value)}
            onPressEnter={() => {
              if (!searchItem) return;
              handleSearch(searchItem);
            }}
            onClear={() => {
              setSearchItem('');
              handleSearch('');
            }}
          />
          <Button
            type='primary'
            onClick={onNewCategory}
            disabled={isDoctorRole()}
            icon={<CustomIcon type='icon-xinjianmoban-01' />}
          >
            新建分类
          </Button>
        </div>
        <Table
          columns={columns}
          dataSource={paginatedDataSource}
          rowKey='Id'
          pagination={false}
          loading={loading}
          components={tableComponents}
        />
        <Flex style={{ marginTop: '18px' }} align='center'>
          <Pagination
            current={current}
            total={categories.length}
            pageSize={DEFAULT_PAGE_SIZE}
            showTotal={(total) => <div>共 {total} 条</div>}
            itemRender={(page, type: 'page' | 'prev' | 'next' | 'jump-prev' | 'jump-next', originalElement) => {
              if (type === 'page') {
                return <div>{page}</div>;
              }
              return originalElement;
            }}
            onChange={handlePageChange}
          />
          <Space style={{ marginLeft: '16px' }} size={12}>
            前往
            <InputNumber
              style={{ width: 50, backgroundColor: '#262628' }}
              min={1}
              max={Math.ceil(categories.length / DEFAULT_PAGE_SIZE)}
              precision={0}
              changeOnBlur
              controls={false}
              onKeyPress={(e) => !/^\d$/.test(e.key) && e.preventDefault()}
              onPressEnter={(e) => {
                const value = (e?.target as HTMLInputElement)?.value;
                const pageNum = value ? Number(value) : 1;
                if (pageNum > Math.ceil(categories.length / DEFAULT_PAGE_SIZE)) return;
                setCurrent(pageNum);
              }}
            />
            页
          </Space>
        </Flex>
        <DeleteConfirmModal
          visible={deleteId !== null}
          onConfirm={handleDeleteConfirm}
          onCancel={handleDeleteCancel}
          message={(() => {
            if (deleteId === null) return undefined;
            const cat = categories.find((c) => c.Id === deleteId);
            const hasTemp = templates.some((t) => t.TypeName === cat?.Name);
            return hasTemp ? '已关联报告模板，不支持删除' : undefined;
          })()}
          showConfirm={(() => {
            if (deleteId === null) return true;
            const cat = categories.find((c) => c.Id === deleteId);
            const hasTemp = templates.some((t) => t.TypeName === cat?.Name);
            return !hasTemp;
          })()}
        />
      </div>
    </ConfigProvider>
  );
};

const mapStateToProps = ({ templateManagement, user }: { templateManagement: TemplateManagementState; user: LoginType }) => ({
  categories: templateManagement.groupCategories,
  pagination: templateManagement.groupCategoryPagination,
  loading: templateManagement.groupCategoriesLoading,
  templates: templateManagement.groupTemplates,
  userInfo: user.userInfo,
});

export default connect(mapStateToProps)(GroupCategories);
