import React, { useState, useRef, useEffect, useMemo, Key } from 'react';
import { type Dispatch } from 'umi';
import { connect } from 'dva';
import {
  ConfigProvider,
  Modal,
  Flex,
  Avatar,
  Tag,
  Space,
  Table,
  Row,
  Col,
  Form,
  Input,
  Button,
  Select,
  message,
  type TableProps
} from 'antd';
import { ExclamationCircleOutlined, UserOutlined, SearchOutlined, CloseOutlined } from '@ant-design/icons';
import { CheckListItemType, QueryCheckListParamsType, CheckListType } from '@/types/patients';
import { ReportInfoType } from '@/types/reports';
import { DEFAULT_PAGE_SIZE } from '@/types/index';
import { ReportType } from '@/models/reports';
import { FetchCheckList } from '@/services/patients';
import { getReport, ImportPersonTemplate, ImportPublicTemplate } from '@/services/reports';
import { UpdateUserInfo } from '@/services/user';
import { LoginType, UserInfoType } from '@/types/user';
import CustomIcon from '@/components/CustomIcon';
import { TemplateManagementState } from '../../model';
import styles from './index.less';

type FormType = {
  Pwd?: string; // 密码，非必须
  NewPwd?: string; // 新密码，非必须
  ConfirmPwd?: string; // 确认密码，非必须
};
// 新增 userInfo 到接口定义
export interface PersonModalProps {
  visible: boolean;
  isGroupTemplate?: boolean;
  onSuccess: () => void;
  onClose: () => void;
  dispatch: Dispatch; // 从 dva 连接获取的用户信息
  templateManagement: TemplateManagementState;
}
// 修复类型不匹配问题，明确指定 React.FC 的泛型为 PersonModalProps
const PersonModal: React.FC<PersonModalProps> = ({ visible, onSuccess, onClose, isGroupTemplate = false,dispatch, templateManagement }) => {
  const [form] = Form.useForm();
  const { categories, groupCategories } = templateManagement;
  //  团队模板、个人模板 分类
  const categoryOptions = useMemo(() => {
    return isGroupTemplate ? groupCategories : categories
  }, [isGroupTemplate, groupCategories, categories]);
  // 检查列表列
  const columns: TableProps<CheckListItemType>['columns'] = [
    {
      className: styles.checkColumns,
      title: '患者编号',
      dataIndex: 'Patient_Id',
      ellipsis: true,
    },
    {
      title: '姓名',
      dataIndex: 'Name',
    },
    {
      title: '检查号',
      dataIndex: 'Study_Id',
    },
    {
      title: '检查部位',
      dataIndex: 'Body_Part_Examined',
    },
  ];
  // 模糊查询
  const [searchItem, setSearchItem] = useState('');
  // 检查列表查询
  const [queryParams, setQueryParams] = useState<QueryCheckListParamsType>({
    Page: 1,
    Limit: DEFAULT_PAGE_SIZE,
    SearchItem: '', // 模糊查找,
    SearchType: 'Patient_Id,Study_Id,Name,Sex,Age,Body_Part_Examined', // 查找类型,
    CheckDate: '00', // 检查日期,
    StatusOfReport: '5', // 报告状态,
  });
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<CheckListItemType[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [report, setReport] = useState<ReportInfoType | undefined>();
  const [studyIds, setStudyIds] = useState<Key[]>([]);
  // 序列列表查询
  const getData = async (params: QueryCheckListParamsType, type: 'add' | 'refresh') => {
    try {
      // 如果是追加操作且当前列表数量已达到总数，直接返回
      if (type === 'add' && list.length && list.length >= total) return;
      if (type === 'refresh') setStudyIds([]);
      setLoading(true);
      const res: CheckListType = await FetchCheckList(params);
      // 处理列表数据，根据操作类型决定是追加还是替换
      setList(prevList => type === 'refresh' ? res.PatientList || [] : [...prevList, ...(res.PatientList || [])]);
      setTotal(res.Length || 0);
      // 如果有患者列表数据，获取第一条数据的报告信息
      if (res.PatientList && res.PatientList.length > 0) {
        const firstStudyId = res.PatientList[0].Study_Id;
        if (firstStudyId) {
          getReportInfo(firstStudyId);
        }
      }
    } catch (error) {
      // 捕获请求错误并提示用户
      console.error('获取数据失败:', error);
      message.error('获取数据失败，请稍后重试');
    } finally {
      // 无论请求成功与否，都停止加载状态
      setLoading(false);
    }
  };

  // 报告详情
  const getReportInfo = async (params: string ) => { 
    getReport({ StudyId: params }).then((res) => {
      setReport(res);
    });
  };

  const scrollRef = useRef({
    left: 0,
    top: 0,
  });
  // 下拉加载
  const handleTableScroll = async (event: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, clientHeight, scrollHeight, scrollLeft } = event.target as HTMLDivElement;

    // 垂直滚动 & 到底了
    if (Math.abs(scrollTop - scrollRef.current.top) > 0 && scrollTop + clientHeight >= scrollHeight) {
      getData({ ...queryParams, Page: currentPage + 1 }, 'add');
      setCurrentPage(currentPage + 1);
    }
    // 记录当前滚动信息
    scrollRef.current = {
      left: scrollLeft,
      top: scrollTop,
    };
  };

  // 确定
  const onOk = () => {
    form.validateFields().then(async(values) => {
      if (!studyIds.length) {
        return message.warning('请选择需要导入的患者报告');
      }
      // 构建请求参数
      const params = {
        StudyId: studyIds,
        ...values
      };
      let res
      setConfirmLoading(true);
      try {
        if(isGroupTemplate) {
          res = await ImportPublicTemplate(params);
        } else {
          res = await ImportPersonTemplate(params);
        }
        message.success(res?.Message || '导入成功');
        resetForm();
        onSuccess();
      } catch (error) {
        console.error('Error:', error);
      } finally {
        setConfirmLoading(false);
      }
    });
  };
  const resetForm = () => { 
    setSearchItem('');
    setStudyIds([])
    form.resetFields();
  };
  // 取消
  const onCancel = () => {
    resetForm();
    onClose();
  };

  useEffect(() => {
    getData(queryParams, 'refresh');
  }, [queryParams]);
  
  return (
    <ConfigProvider
      theme={{
        components: {
          Form: {
            fontSize: 16,
            colorPrimary: '#474645',
            labelFontSize: 16,
            labelColor: '#ffffff',
            itemMarginBottom: 24,
            verticalLabelPadding: '0 0 4px',
          },
          Table: {
            rowHoverBg: 'transparent',
            rowSelectedBg: 'transparent',
            rowSelectedHoverBg: 'transparent',
          },
          Button: {
            colorBorder: '#d9d9d9',
          },
        },
      }}
    >
      <Modal
        classNames={{
          header: styles.modalHeader,
          content: styles.modalContent,
          body: styles.modalBody,
          footer: styles.modalFooter,
        }}
        title='选择导入报告的检查号'
        centered
        width={1300}
        open={visible}
        onOk={onOk}
        onCancel={onCancel}
        confirmLoading={confirmLoading}
        closeIcon={
          <div className={styles.closeIcon}>
            <CloseOutlined />
          </div>
        }
        footer={(_, { OkBtn, CancelBtn }) => (
          <>
            <CancelBtn />
            <OkBtn />
          </>
        )}
      >
        <Row gutter={20}>
          <Col span={12}>
            <Form layout='vertical' form={form} initialValues={
              {
                studyIds: [],
                TypeId: undefined
              }
            }>
              <Form.Item label='选择导入的患者报告' required >
                <div className={styles.patient}>
                  <div >
                    <Input
                      className={styles.search}
                      placeholder='请输入关键字搜索'
                      prefix={<SearchOutlined className={styles.searchIcon} onClick={() => setQueryParams({...queryParams,SearchItem: searchItem})} />}
                      allowClear
                      value={searchItem}
                      maxLength={99}
                      onChange={(e) => setSearchItem(e.target.value)}
                      onClear={() => {
                        console.log('clear');
                        setSearchItem('');
                        setQueryParams({...queryParams,SearchItem: ''});
                      }}
                      onPressEnter={(e) => {
                        if(!searchItem) return message.error('请输入关键字搜索');
                        setQueryParams({...queryParams,SearchItem: searchItem});
                      }}
                    />
                  </div>
                  <Table<CheckListItemType>
                    style={{ height: 'calc(50vh)' }}
                    scroll={{ y: 'calc(50vh - 35px)' }}
                    loading={loading}
                    columns={columns}
                    dataSource={list}
                    pagination={false}
                    rowKey='Study_Id'
                    rowSelection={{
                      type: 'checkbox',
                      selectedRowKeys: studyIds, // 使用 form.getFieldValue 获取值
                      onChange: (selectedRowKeys) => {
                        setStudyIds(selectedRowKeys);
                      },
                    }}
                    rowClassName={(record) => {
                      return record.Study_Id == report?.StudyId ? styles.rowSelected : '';
                    }}
                    onRow={(record) => {
                      return {
                        onClick: () => {
                          getReportInfo(record?.Study_Id || '')
                        },
                      };
                    }}
                    onScroll={handleTableScroll}
                  />
                </div>
              </Form.Item>
              <Form.Item label='模板分类' name='TypeId' rules={[{ required: true, message: '请选择模板分类' }]}>
                <Select placeholder="请选择模板分类" allowClear>
                    {categoryOptions.map((type:any) => (
                      <Select.Option key={type.Id} value={type.Id}>
                        {type.Name === 'default' ? '默认分类' : type.Name}
                      </Select.Option>
                    ))}
                  </Select>
              </Form.Item>
            </Form>
          </Col>
          <Col span={12}>
            {report && (
              <div>  
                <Flex align='center'>
                  <Avatar style={{ backgroundColor: '#ffffff' }} size={46} icon={<CustomIcon type='icon-gerenzhongxin' style={{ color: '#1F69B4', fontSize: 32 }} />} />
                  <Flex className={styles.info} vertical justify='space-between'>
                    <div className={styles.info_name}>{report?.Name}</div>
                    <Space className={styles.info_t} size={20}>
                      <div>患者编号: {report?.PatientId}</div>
                      <div>检查号: {report?.StudyId}</div>
                    </Space>
                  </Flex>
                </Flex>
                <Flex style={{ marginTop: '40px' }} vertical >
                  <div className={styles.htmlBox}>
                    <div className={styles.htmlBox_t}>影像所见:</div>
                    <div className={styles.htmlBox_con} dangerouslySetInnerHTML={{ __html: report?.RadiographicFinding }}></div>
                  </div>
                  <div className={styles.htmlBox}>
                    <div className={styles.htmlBox_t}>诊断结论:</div>
                    <div className={styles.htmlBox_con} dangerouslySetInnerHTML={{ __html: report?.ClinicalDiagnosis }}></div>
                  </div>
                </Flex>
              </div>
            )}
          </Col>
        </Row>
      </Modal>
    </ConfigProvider>
  );
};

// 定义 mapStateToProps 函数，从 dva 的 login 模块中获取 userInfo
const mapStateToProps = ({ reports,templateManagement }: { reports: ReportType,templateManagement: TemplateManagementState }) => {
  return {
    // 在patient页中dispatch以下信息
    radio: reports.radioFind, // 影像学研究
    clini: reports.cliniDiag, // 临床诊断
    reportLoading: reports.reportLoading,
    patientInfo: reports.patientInfo,
    studyId: reports.studyId,
    patientId: reports.patientId,
    isRepoSave: reports.isRepoSave,
    templates: reports.templates,
    tempSaveMsg: reports.tempSaveMsg,
    pubRepoMsg: reports.pubRepoMsg,
    status: reports.status,
    checkMsg: reports.checkMsg,
    cancelCheckMsg: reports.cancelCheckMsg,
    imgs: reports.imgs,
    video: reports.video,
    templateTypes: reports.templateTypes,
    templateManagement,
  };
};

// 使用 connect 连接组件和状态
export default connect(mapStateToProps)(PersonModal);
