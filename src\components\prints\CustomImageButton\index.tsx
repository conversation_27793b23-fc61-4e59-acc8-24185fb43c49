import React from 'react';
import { Button, Image } from 'antd';

interface ICustomImageButtonProps {
  icon?: React.ReactNode; // 支持Ant Design Icons或自定义图标节点
  imageUrl?: string; // 图片URL
  text: string; // 按钮文字
  [key: string]: any;
}

const CustomImageButton: React.FC<ICustomImageButtonProps> = ({
  icon,
  imageUrl,
  text,
  ...rest
}) => {
  const hasIconOrImage = icon || imageUrl;
  const renderIcon = () => icon;
  const renderImage = () =>
    imageUrl && <Image src={imageUrl} alt='Button Image' preview={false} width={24} height={24} />;

  return (
    <Button
      style={{
        backgroundColor: '#6d6d6d',
        border: 'none',
        display: 'flex',
        flexDirection: hasIconOrImage ? 'column' : 'row',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '4px',
        width: '60px',
        height: '85px',
        padding: '10px 1px',
      }}
      {...rest}
    >
      {renderIcon()}
      {renderImage()}
      <span style={{ fontSize: '12px', marginLeft: '0px' }}>{text}</span>
    </Button>
  );
};

export default CustomImageButton;
