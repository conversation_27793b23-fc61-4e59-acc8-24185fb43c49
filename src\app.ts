import { history } from 'umi';
import type { RequestConfig, AxiosResponse, AxiosRequestConfig } from 'umi';
import { persistEnhancer } from 'dva-model-persist';
import storeUtil from '@/utils/store';
import { message } from 'antd';
// 提取获取错误信息的逻辑到一个函数中，提高代码复用性
const getErrorMessage = (responseData: any) => {
  // 定义默认错误信息
  const defaultErrorMessage = '请求失败，请稍后再试';
  const { status, data } = responseData;
  if (status !== 200) {
    return data.error;
  } else if (status === 200 && data.code !== 200) {
    if(data.code === 401) storeUtil.remove('token'),history.replace('/login');
    return data.error;
  }
  return defaultErrorMessage;
};

// 优化后的响应拦截器函数
const responseInterceptor = (response: AxiosResponse) => {
  const { data, status } = response;

  // 首先，检查响应数据是否为Blob类型
  if (data instanceof Blob && status === 200) {
    // 对于Blob，返回完整的、原始的response对象。
    // 这可以确保umi-request的内部流程不会中断。
    return response; 
  }

  // 如果代码执行到这里，说明是标准JSON API响应
  if (status !== 200 || (data && data.code !== 200)) {
    if (data && data.code === 401) {
      storeUtil.remove('token');
      history.replace('/login');
    }
    return Promise.reject(response);
  }

  // 对于成功的JSON响应，按原计划返回核心数据
  return data;
};

// 统一fetch请求拦截器
const requestInterceptor = (config: AxiosRequestConfig): AxiosRequestConfig => {
  const token = storeUtil.get('token').value ?? '';
  if (token && config.url !== '/api/login') {
    if (!config.headers) config.headers = {};
    const hasAuth = Object.keys(config.headers).some(
      (key) => key.toLowerCase() === 'authorization',
    );
    if (!hasAuth) {
      config.headers.Authorization = token;
    }
  }
  return config;
};

// 统一fetch请求拦截器
export const request: RequestConfig = {
  timeout: 30000, // 默认超时时间,
  // other axios options you want
  errorConfig: {
    errorHandler(e: any) {
      console.log('errorHandler', e);
      if(e.code == "ERR_NETWORK") return message.error("网络链接异常,请检查网络！");
      // 根据是否存在响应对象调用相应的错误信息获取逻辑
      const errorMessage = e.response ? getErrorMessage(e.response) : getErrorMessage(e);
      message.error(errorMessage);
    },
    errorThrower(e) {
      console.log('e2', e);
    },
  },
  requestInterceptors: [requestInterceptor],
  responseInterceptors: [responseInterceptor], // 响应拦截器,
};

//  处理了全局错误的dva配置
export const dva = {
  config: {
    onError(e: any) {
      console.log('onError', e);
    },
    extraEnhancers: [
      persistEnhancer()
    ],
  },
};


