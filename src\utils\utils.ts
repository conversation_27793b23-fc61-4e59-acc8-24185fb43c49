export const getCurSeriesIndex = (selectAreaId: string | null) => {
  if (!selectAreaId) return;
  const [_, seriesIndex, __] = selectAreaId.split('-');
  return Number(seriesIndex);
};

export const layoutNumToArr = (rowAndColNum: number): [number, number] => {
  // 正确解析布局数字，第一位是列数，第二位是行数
  // 例如：23表示2列3行的布局
  const col = Math.floor(rowAndColNum / 10);
  const row = rowAndColNum % 10;
  return [col, row];
};

// 防抖函数
export const debounce = (fn: Function, delay: number) => {
  let timer: NodeJS.Timeout | null = null;
  return (...args: any[]) => {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      fn.apply(this, args);
      timer = null;
    }, delay);
  };
};

// 节流函数
export const throttle = (fn: Function, limit: number) => {
  let inThrottle: boolean;
  return (...args: any[]) => {
    if (!inThrottle) {
      fn.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};
