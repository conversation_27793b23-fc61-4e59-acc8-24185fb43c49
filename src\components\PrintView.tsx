/**
 * 在报告页面生成Modal
 * 负责审核！
 * 传入诊断报告与影像学检查富文本内容
 */
import React, { useEffect, useState } from 'react';
import { Row, Col, Modal, Button, Card, Divider, Image, ConfigProvider, Input, Space } from 'antd';
import Print from 'print-js';
import { ProDescriptions } from '@ant-design/pro-components';
import styles from './PrintView.less';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.bubble.css';
import storeUtil from '@/utils/store';

interface PrintView {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  radioFind: any;
  cliniDiag: any;
  patientInfo: any;
  handleSave: () => void;
  handlePub: () => void;
  status?: number;
  handleCheck: (IsPass: number, ReturnReason: string) => void;
  handleCancelCheck: () => void;
}

const columns = [
  {
    label: '姓名',
    dataIndex: 'Name',
  },
  {
    label: '性别',
    dataIndex: 'Sex',
  },
  {
    label: '年龄',
    dataIndex: 'Age',
  },
  {
    label: '检查号',
    dataIndex: 'StudyId',
    // labelStyle: {
    //   position: 'relative',
    //   left: '-3vw',
    // },
    // contentStyle: {
    //   position: 'relative',
    //   left: '-3vw',
    // },
    style: {
      position: 'relative',
      left: '-3vw',
      whiteSpace: 'nowrap',
    },
  },
  {
    label: '科室',
    dataIndex: 'ApplyDepartment',
  },
  {
    label: '住院号',
    dataIndex: 'num', // 后端缺失
  },
  {
    label: '床号',
    dataIndex: 'num', // 后端缺失
  },
  {
    label: '检查时间',
    dataIndex: 'time', // 后端缺失
    style: {
      position: 'relative',
      left: '-3vw',
      whiteSpace: 'nowrap',
    },
  },

  {
    label: '检查项目',
    dataIndex: 'CheckingProject',
    span: 2,
  },
  {
    label: '显像剂',
    dataIndex: 'Medicine',
    span: 2,
  },
  {
    label: '临床诊断',
    dataIndex: 'diag', // 短评？
    span: 4,
  },
];

const PrintView: React.FC<PrintView> = ({
  visible,
  setVisible,
  radioFind,
  cliniDiag,
  patientInfo,
  handleSave,
  handlePub,
  status,
  handleCheck,
  handleCancelCheck,
}) => {
  // 退回Modal的开关
  const [backVisible, setBackVisible] = useState(false);
  const [backReason, setBackReason] = useState(''); // 退回原因
  // 撤审Modal的开关
  const [cancelVisible, setCancelVisible] = useState(false);
  const [cancelReason, setCancelReason] = useState(''); // 撤审原因
  // 审核通过确认Modal的开关
  const [checkVisible, setCheckVisible] = useState(false);

  // 普通医生的报告预览按钮
  const pubBtnList = [
    <Button key='edit' type='primary' style={{ width: 80, backgroundColor: '#1a80ff' }} onClick={() => setVisible(false)}>
      编辑
    </Button>,
    <Button
      key='save'
      type='primary'
      style={{ width: 80, backgroundColor: '#2bbc89' }}
      onClick={() => {
        setVisible(false);
        handleSave();
      }}
    >
      保存
    </Button>,
    <Button
      key='submit'
      type='primary'
      style={{ width: 80, backgroundColor: '#0f40f5' }}
      onClick={() => {
        setVisible(false);
        handlePub();
      }}
    >
      发布
    </Button>,
  ];
  // 审核前按钮列表
  const checkBtnList = [
    <Button key='edit' type='primary' style={{ width: 80, backgroundColor: '#1a80ff' }} onClick={() => setVisible(false)}>
      修正
    </Button>,
    <Button
      key='save'
      type='primary'
      style={{ width: 80, backgroundColor: '#f54536' }}
      onClick={() => {
        // setVisible(false);
        // 接口
        setBackVisible(true);
      }}
    >
      退回
    </Button>,
    <Button
      key='submit'
      type='primary'
      style={{ width: 80, backgroundColor: '#2bbc89' }}
      onClick={() => {
        // setVisible(false);
        // handleCheck(0, '');
        setCheckVisible(true);
      }}
    >
      审核
    </Button>,
  ];
  // 审核后按钮列表
  const checkedBtnList = [
    <Button key='edit' type='primary' style={{ width: 80, backgroundColor: '#1a80ff' }} onClick={() => setVisible(false)}>
      取消
    </Button>,
    <Button
      key='save'
      type='primary'
      style={{ width: 80, backgroundColor: '#f54536' }}
      onClick={() => {
        // setVisible(false);
        // 接口
        setCancelVisible(true);
      }}
    >
      撤审
    </Button>,
  ];

  const genBtnList = () => {
    switch (storeUtil.get('right').value) {
      case '1': // 保留
        return pubBtnList;
      case '0': // 管理员
      case '2': // 审核医生
        // 0初始，1保存，2发布，3退回，4通过
        if (status === 0 || status === 1) return pubBtnList;
        if (status === 2) return checkBtnList;
        return checkedBtnList;
      case '3': // 普通医生
        return pubBtnList;

      default:
        return null;
    }
  };

  return (
    <>
      <Modal
        title='审核通过'
        open={checkVisible}
        closable={false}
        zIndex={1002}
        onOk={() => setCheckVisible(false)}
        onCancel={() => setCheckVisible(false)}
        styles={{
          header: {
            backgroundColor: '#a0a0a0',
            height: 40,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          },
          body: {
            backgroundColor: '#373737',
            padding: 20,
            paddingBottom: 0,
            paddingTop: 10,
            height: 50,
          },
          footer: {
            display: 'flex',
            justifyContent: 'space-around',
            padding: 20,
          },
          content: { padding: 0, backgroundColor: '#373737' },
        }}
        style={{ top: '30vh' }}
        width={400}
        footer={[
          <Button
            key='cancel'
            type='primary'
            style={{ width: 80, backgroundColor: '#0360fb' }}
            onClick={() => setCheckVisible(false)}
          >
            取消
          </Button>,
          <Button
            key='ok'
            type='primary'
            style={{ width: 80, backgroundColor: '#2bbc89' }}
            onClick={() => {
              setVisible(false);
              setCheckVisible(false);
              handleCheck(0, '');
            }}
          >
            确定
          </Button>,
        ]}
      >
        <span style={{ fontWeight: 'bolder', fontSize: 16, color: 'white' }}>确认通过?</span>
      </Modal>
      <Modal
        title='报告退回'
        open={backVisible}
        closable={false}
        zIndex={1001}
        onOk={() => setBackVisible(false)}
        onCancel={() => setBackVisible(false)}
        styles={{
          header: {
            backgroundColor: '#a0a0a0',
            height: 40,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          },
          body: {
            backgroundColor: '#373737',
            padding: 20,
            paddingBottom: 0,
            paddingTop: 10,
            height: 150,
          },
          footer: {
            display: 'flex',
            justifyContent: 'space-around',
            padding: 20,
          },
          content: { padding: 0, backgroundColor: '#373737' },
        }}
        style={{ top: '30vh' }}
        width={400}
        footer={[
          <Button
            key='cancel'
            type='primary'
            style={{ width: 80, backgroundColor: '#0360fb' }}
            onClick={() => setBackVisible(false)}
          >
            取消
          </Button>,
          <Button
            key='ok'
            type='primary'
            style={{ width: 80, backgroundColor: '#2bbc89' }}
            onClick={() => {
              setVisible(false);
              setBackVisible(false);
              handleCheck(1, backReason);
            }}
          >
            确定
          </Button>,
        ]}
      >
        <Space direction='vertical' style={{ width: '100%' }}>
          <span style={{ fontWeight: 'bolder', fontSize: 16, color: 'white' }}>退回原因:</span>
          <Input.TextArea
            className={styles.textArea}
            style={{ width: '100%', height: 100, resize: 'none' }}
            value={backReason}
            onChange={(e) => setBackReason(e.target.value)}
          />
        </Space>
      </Modal>
      <Modal
        title='报告撤审'
        open={cancelVisible}
        closable={false}
        zIndex={1001}
        onOk={() => setCancelVisible(false)}
        onCancel={() => setCancelVisible(false)}
        styles={{
          header: {
            backgroundColor: '#a0a0a0',
            height: 40,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          },
          body: {
            backgroundColor: '#373737',
            padding: 20,
            paddingBottom: 0,
            paddingTop: 10,
            height: 150,
          },
          footer: {
            display: 'flex',
            justifyContent: 'space-around',
            padding: 20,
          },
          content: { padding: 0, backgroundColor: '#373737' },
        }}
        style={{ top: '30vh' }}
        width={400}
        footer={[
          <Button
            key='cancel'
            type='primary'
            style={{ width: 80, backgroundColor: '#0360fb' }}
            onClick={() => setCancelVisible(false)}
          >
            取消
          </Button>,
          <Button
            key='ok'
            type='primary'
            style={{ width: 80, backgroundColor: '#2bbc89' }}
            onClick={() => {
              setVisible(false);
              setCancelVisible(false);
              handleCancelCheck();
            }}
          >
            确定
          </Button>,
        ]}
      >
        <Space direction='vertical' style={{ width: '100%' }}>
          <span style={{ fontWeight: 'bolder', fontSize: 16, color: 'white' }}>撤审原因:</span>
          <Input.TextArea
            className={styles.textArea}
            style={{ width: '100%', height: 100, resize: 'none' }}
            value={cancelReason}
            onChange={(e) => setCancelReason(e.target.value)}
          />
        </Space>
      </Modal>
      <ConfigProvider
        theme={{
          // token: {
          //   colorBgContainer: '#5b5b5b',
          //   // colorBgElevated: '#474747',
          //   colorBgBase: '#5b5b5b',
          //   colorTextBase: 'white',
          // },
          components: {
            Descriptions: {
              fontSize: 14,
              itemPaddingBottom: 2,
              titleMarginBottom: 10,
            },
            Modal: {
              borderRadiusLG: 0,
              colorBgMask: '#404040',
            },
            Divider: {
              colorSplit: 'black',
            },
            //   Button: {
            //     defaultBg: 'green',
            //   },
          },
        }}
      >
        <Modal
          title={null}
          open={visible}
          onOk={() => setVisible(false)}
          onCancel={() => setVisible(false)}
          closeIcon={null}
          forceRender
          style={{
            position: 'fixed',
            top: '2vh',
            bottom: '2vh',
            left: '32vw',
            height: '96vh',
          }}
          width={'36vw'}
          styles={{
            body: { height: '86vh' },
            footer: { display: 'flex', justifyContent: 'space-around' },
          }}
          footer={genBtnList()}
        >
          <div id='reportPrint'>
            <Row style={{ marginTop: -10 }}>
              <Col
                span={5}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Image src={'/PnI.png'} width={100} preview={false} />
              </Col>
              <Col span={14} style={{ textAlign: 'center' }}>
                <h2 style={{ fontWeight: 'bold', marginBottom: -4 }}>核医学科</h2>
                <h2
                  style={{
                    fontWeight: 'bold',
                    marginBottom: 0,
                    fontSize: 28,
                  }}
                >
                  xxxx第一人民医院
                </h2>
                <h2
                  style={{
                    fontWeight: 'bold',
                    marginBottom: 0,
                    fontSize: 20,
                  }}
                >
                  PET/CT检查报告单
                </h2>
              </Col>
            </Row>
            <Divider style={{ marginTop: 6, marginBottom: 6 }} />
            <ProDescriptions
              //   title='患者信息'
              className={styles.description}
              dataSource={patientInfo}
              column={4}
              columns={columns as any}
              // style={{ height: '100%' }}
            />
            <Divider style={{ marginTop: 6, marginBottom: 0 }} />
            <ReactQuill
              className={styles.quill}
              placeholder={'暂无数据'}
              style={{ height: '100%' }}
              theme='bubble'
              value={radioFind + '<br /> <br />' + cliniDiag}
              readOnly
            />
          </div>
        </Modal>
      </ConfigProvider>
    </>
  );
};

export default PrintView;
