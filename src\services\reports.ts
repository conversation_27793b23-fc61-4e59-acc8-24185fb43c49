import axios from 'axios';
import storeUtil from '@/utils/store';
import { request } from 'umi';
import type { ReportData } from '@/pages/reports/data';
import { CheckReportParams, RecallReportParams, ImportTemplateParams } from '@/types/reports';


// 获取模板类型列表
export async function ListTemplateType(params: any) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string } = {};
  if (token) {
    headers.Authorization = token;
  }
  return request(`${CLOUD_API}/public/listTemplateType`, {
    method: 'GET',
    params: params,
    headers: headers,
  });
}

// 根据模板类型获取模板列表
export async function ListTemplateByType(params: any) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string } = {};
  if (token) {
    headers.Authorization = token;
  }
  return request(`${CLOUD_API}/public/listTemplateByType`, {
    method: 'GET',
    params: params,
    headers: headers,
  });
}

// 根据模板ID获取模板内容
export async function getTemplateContent(params: { Id: number }) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string } = {};
  if (token) {
    headers.Authorization = token;
  }
  return request(`${CLOUD_API}/public/getTemplateContent`, {
    method: 'GET',
    params: params,
    headers: headers,
  });
}

// 根据团队模板ID获取模板内容
export async function getGroupTemplateContent(params: { Id: number }) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string } = {};
  if (token) {
    headers.Authorization = token;
  }
  return request(`${CLOUD_API}/public/getGroupTemplateContent`, {
    method: 'GET',
    params,
    headers,
  });
}

// 获取团队模板分类列表
export async function listGroupTemplateType(params: any) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string } = {};
  if (token) {
    headers.Authorization = token;
  }
  return request(`${CLOUD_API}/public/listGroupTemplateType`, {
    method: 'GET',
    params: params,
    headers: headers,
  });
}

// 根据团队模板分类获取模板列表
export async function listGroupTemplateByType(params: any) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string } = {};
  if (token) {
    headers.Authorization = token;
  }
  return request(`${CLOUD_API}/public/listGroupTemplateByType`, {
    method: 'GET',
    params: params,
    headers: headers,
  });
}

// 获取模板列表
export async function listTemplate(params: any) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string } = {};
  if (token) {
    headers.Authorization = token;
  }
  return request(`${CLOUD_API}/public/listTemplate`, {
    method: 'GET',
    params: params,
    headers: headers,
  });
}

export async function updateTemplate(data: any) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string; 'Content-Type'?: string } = {
    'Content-Type': 'application/json',
  };
  if (token) {
    headers.Authorization = token;
  }
  return request(`${CLOUD_API}/public/updateTemplate`, {
    method: 'PUT',
    data: data,
    headers: headers,
  });
}

export async function createTemplate(data: any) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string; 'Content-Type'?: string } = {
    'Content-Type': 'application/x-www-form-urlencoded',
  };
  if (token) {
    headers.Authorization = token;
  }

  // 从传入的 data 中解构出需要的字段
  const { Name, TypeId, RadiographicFindingTemplate, ClinicalDiagnosisTemplate } = data;

  // 创建 URLSearchParams 对象以进行表单编码
  const formData = new URLSearchParams();
  formData.append('Name', Name);
  formData.append('TypeId', TypeId);
  formData.append('RadiographicFindingTemplate', RadiographicFindingTemplate || '');
  formData.append('ClinicalDiagnosisTemplate', ClinicalDiagnosisTemplate || '');

  return request(`${CLOUD_API}/public/createTemplate`, {
    method: 'POST',
    data: formData,
    headers: headers,
  });
}

export async function listTemplateCategories(params: any) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string } = {};
  if (token) {
    headers.Authorization = token;
  }
  return request(`${CLOUD_API}/public/listTemplateType`, {
    method: 'GET',
    params: params,
    headers: headers,
  });
}

export async function createTemplateCategory(data: { Name: string }) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string; 'Content-Type'?: string } = {
    'Content-Type': 'application/x-www-form-urlencoded',
  };
  if (token) {
    headers.Authorization = token;
  }

  const formData = new URLSearchParams();
  formData.append('Name', data.Name);

  return request(`${CLOUD_API}/public/createTemplateType`, {
    method: 'POST',
    data: formData,
    headers: headers,
  });
}

export async function updateTemplateCategory(data: { Id: number; Name: string }) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string; 'Content-Type'?: string } = {
    'Content-Type': 'application/json',
  };
  if (token) {
    headers.Authorization = token;
  }
  return request(`${CLOUD_API}/public/updateTemplateType`, {
    method: 'PUT',
    data: { Id: data.Id, Name: data.Name },
    headers: headers,
  });
}

export async function deleteTemplateCategory(data: { Id: number }) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string } = {};
  if (token) {
    headers.Authorization = token;
  }

  // 将 Id 参数作为查询参数传递
  return request(`${CLOUD_API}/public/deleteTemplateType?Id=${data.Id}`, {
    method: 'DELETE',
    headers,
  });
}

export async function listGroupTemplate(params: { Limit: number; Offset: number }) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string } = {};
  if (token) {
    headers.Authorization = token;
  }
  return request(`${CLOUD_API}/public/listGroupTemplate`, {
    method: 'GET',
    params: params,
    headers: headers,
  });
}



export async function createGroupTemplateCategory(data: { Name: string }) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string; 'Content-Type'?: string } = {
    'Content-Type': 'application/x-www-form-urlencoded',
  };
  if (token) {
    headers.Authorization = token;
  }

  const formData = new URLSearchParams();
  formData.append('Name', data.Name);

  return request(`${CLOUD_API}/check/createGroupTemplateType`, {
    method: 'POST',
    data: formData,
    headers: headers,
  });
}

export async function getReport(params: { StudyId: string }) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string } = {};
  if (token) {
    headers.Authorization = token;
  }
  return request<ReportData>(`${CLOUD_API}/public/getReport`, {
    method: 'GET',
    params: params,
    headers: headers,
  });
}

export async function createGroupTemplate(data: any) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string; 'Content-Type'?: string } = {
    'Content-Type': 'application/x-www-form-urlencoded',
  };
  if (token) {
    headers.Authorization = token;
  }
  const { Name, TypeId, RadiographicFindingTemplate, ClinicalDiagnosisTemplate } = data;
  const formData = new URLSearchParams();
  formData.append('Name', Name);
  formData.append('TypeId', TypeId);
  formData.append('RadiographicFindingTemplate', RadiographicFindingTemplate || '');
  formData.append('ClinicalDiagnosisTemplate', ClinicalDiagnosisTemplate || '');

  return request(`${CLOUD_API}/check/createGroupTemplate`, {
    method: 'POST',
    data: formData,
    headers: headers,
  });
}

// 删除个人报告模板
export async function deleteTemplate(data: { Id: number }) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string } = {};
  if (token) {
    headers.Authorization = token;
  }

  // 将 Id 参数作为查询参数传递
  return request(`${CLOUD_API}/public/deleteTemplate?Id=${data.Id}`, {
    method: 'DELETE',
    headers,
  });
}

// 删除团队报告模板
export async function deleteGroupTemplate(data: { Id: number }) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string } = {};
  if (token) {
    headers.Authorization = token;
  }

  // 将 Id 参数作为查询参数传递
  return request(`${CLOUD_API}/check/deleteGroupTemplate?Id=${data.Id}`, {
    method: 'DELETE',
    headers,
  });
}

// 删除团队模板分类，同时删除对应模板
export async function deleteGroupTemplateCategory(data: { Id: number }) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string } = {};
  if (token) headers.Authorization = token;

  // 将 Id 参数作为查询参数传递
  return request(`${CLOUD_API}/check/deleteGroupTemplateType?Id=${data.Id}`, {
    method: 'DELETE',
    headers,
  });
}

// 更新团队模板
export async function updateGroupTemplate(data: any) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string; 'Content-Type'?: string } = {
    'Content-Type': 'application/x-www-form-urlencoded',
  };
  if (token) headers.Authorization = token;

  const { Id, Name, TypeId, RadiographicFindingTemplate, ClinicalDiagnosisTemplate } = data;
  const formData = new URLSearchParams();
  formData.append('Id', Id);
  formData.append('Name', Name);
  formData.append('TypeId', TypeId);
  formData.append('RadiographicFindingTemplate', RadiographicFindingTemplate || '');
  formData.append('ClinicalDiagnosisTemplate', ClinicalDiagnosisTemplate || '');

  return request(`${CLOUD_API}/check/updateGroupTemplate`, {
    method: 'PUT',
    data: formData,
    headers,
  });
}

// 更新团队模板分类
export async function updateGroupTemplateCategory(data: { Id: number; Name: string }) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string; 'Content-Type'?: string } = {
    'Content-Type': 'application/x-www-form-urlencoded',
  };
  if (token) headers.Authorization = token;

  const formData = new URLSearchParams();
  formData.append('Name', data.Name);
  formData.append('Id', data.Id.toString());

  return request(`${CLOUD_API}/check/updateGroupTemplateType`, {
    method: 'PUT',
    data: formData,
    headers,
  });
}

// 获取截图路径列表
export async function listImage(params: { StudyId: string }) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string } = {};
  if (token) headers.Authorization = token;
  return request(`${CLOUD_API}/public/listImage`, {
    method: 'GET',
    params,
    headers,
  });
}

// 保存报告（草稿）
export async function saveReport(data: { StudyId: string; Id: number; RadiographicFinding: string; ClinicalDiagnosis: string }) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string; 'Content-Type': string } = {
    'Content-Type': 'application/json',
  };
  if (token) headers.Authorization = token;
  return request(`${CLOUD_API}/public/saveReport`, {
    method: 'POST',
    data,
    headers,
  });
}

// 提交报告
export async function submitReport(data: { StudyId: string; Id: number; RadiographicFinding: string; ClinicalDiagnosis: string }) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string; 'Content-Type': string } = {
    'Content-Type': 'application/x-www-form-urlencoded',
  };
  if (token) headers.Authorization = token;

  const formData = new URLSearchParams();
  formData.append('StudyId', data.StudyId);
  formData.append('Id', String(data.Id));
  formData.append('RadiographicFinding', data.RadiographicFinding || '');
  formData.append('ClinicalDiagnosis', data.ClinicalDiagnosis || '');

  return request(`${CLOUD_API}/public/submitReport`, {
    method: 'POST',
    data: formData,
    headers,
  });
}

// 获取单张截图
export async function getImage(params: { StudyId?: string; ImageName: string }) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string } = {};
  if (token) headers.Authorization = token;

  try {
    return await request(`${CLOUD_API}/public/getImage`, {
      method: 'GET',
      params,
      headers,
      responseType: 'blob', // 返回图片blob数据
    });
  } catch (error: any) {
    // umi-request 在处理 blob 响应时的已知问题：
    // 成功响应被错误地当作异常抛出，但实际数据在 error.data 中
    if (error?.data && error.data instanceof Blob && error.status === 200) {
      // 这实际上是成功响应，返回 Blob 数据
      return error.data;
    }
    // 如果是真正的错误，重新抛出
    throw error;
  }
}

// 审核模块

// 获取审核流程
export async function FetchCheckProcess(params: string | number) {
  return request(`/api/check/listReportRecord`, {
    method: 'get',
    params: {
      Id: params,
      Offset: 1,
      Limit: -1,
    },
  });
}

// 报告审核
export async function CheckReport(data: CheckReportParams) {
  return request(`/api/check/checkReport`, {
    method: 'post',
    data: data,
  });
}

// 报告召回
export async function RecallReport(data: RecallReportParams) {
  return request(`/api/public/recallReport`, {
    method: 'post',
    data: data,
  });
}

// 导入个人模板
export async function ImportPersonTemplate(data: ImportTemplateParams) {
  return request(`/api/public/importTemplate`, {
    method: 'post',
    data: data,
  });
}
// 导入团队模板
export async function ImportPublicTemplate(data: ImportTemplateParams) {
  return request(`/api/check/importGroupTemplate`, {
    method: 'post',
    data: data,
  });
}

// 上传模板图片
export async function saveTemplateImage(file: File) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string } = {};
  if (token) {
    headers.Authorization = token;
  }

  const formData = new FormData();
  formData.append('image', file);

  return request(`${CLOUD_API}/public/saveTemplateImage`, {
    method: 'POST',
    data: formData,
    headers: headers,
  });
}

// 上传团队模板图片
export async function saveGroupTemplateImage(file: File) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string } = {};
  if (token) {
    headers.Authorization = token;
  }

  const formData = new FormData();
  formData.append('image', file);

  return request(`${CLOUD_API}/check/saveGroupTemplateImage`, {
    method: 'POST',
    data: formData,
    headers: headers,
  });
}

// 更新图片使用状态
export async function updateImageUseStatus(data: { Name: string; Use: number }) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string; 'Content-Type'?: string } = {
    'Content-Type': 'application/x-www-form-urlencoded',
  };
  if (token) headers.Authorization = token;

  const formData = new URLSearchParams();
  formData.append('Name', data.Name);
  formData.append('Use', String(data.Use));

  return request(`${CLOUD_API}/public/updateImage`, {
    method: 'PUT',
    data: formData,
    headers,
  });
}

// 删除图片
export async function deleteImage(data: { Name: string }) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string } = {};
  if (token) headers.Authorization = token;

  // 参数放到 URL 查询参数中
  return request(`${CLOUD_API}/public/deleteImage?Name=${encodeURIComponent(data.Name)}`, {
    method: 'DELETE',
    headers,
  });
}
