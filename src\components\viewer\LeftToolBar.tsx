import React from 'react';
import { Row, Col } from 'antd';
import { LeftToolsBarWebGL, StudiesList } from 'rayplus-three-view';

// 仿照Rayplus前端进行设置
interface LeftToolBar {}

const LeftToolBar: React.FC<LeftToolBar> = ({}) => {
  return (
    <>
      <div
        style={{
          width: '100%',
          height: '100%',
          backgroundColor: '#404040',
          overflowX: 'auto',
          overflowY: 'auto',
          border: 'thin solid gray',
          borderRadius: 5,
        }}
      >
        <div style={{ marginTop: 10, marginLeft: 10 }}>
          <StudiesList />
        </div>
      </div>
    </>
  );
};
export default LeftToolBar;
