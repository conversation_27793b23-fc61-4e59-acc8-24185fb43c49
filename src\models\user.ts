import { stringify } from 'querystring';
import { history, Effect, Reducer } from 'umi';
import { message } from 'antd';
import { accountLogin, FetchUserInfo } from '@/services/user';
import { UserInfoType,LoginType } from '@/types/user';
import storeUtil from '@/utils/store';

export interface ModelType {
  state: LoginType;
  effects: {
    login: Effect;
    logout: Effect;
    getUserInfo: Effect;
  };
  reducers: {
    changeLoginStatus: Reducer<LoginType>;
    save: Reducer<LoginType>;
  };
}

const Model: ModelType = {
  state: {
    data: undefined,
    correct: true,
    userInfo: JSON.parse(storeUtil.get('userInfo')?.value || '{}'),
    networkConfig: undefined,
  },

  effects: {
    // 副作用 *的是异步方法
    *login({ payload }, { call, put }): any {
      const response = yield call(accountLogin, payload);
      if (response) {
        yield put({
          type: 'changeLoginStatus',
          payload: response,
        });
        // 记录用户信息
        yield put({
          type: 'save',
          payload: {
            data: response,
            correct: true,
          },
        });
        // 调用获取个人信息方法
        yield put({
          type: 'getUserInfo',
          payload: {
            Id: response.Id,
          }
        });
        //测试用直接跳转到viewer
        history.replace('/');
        message.success('登录成功！');
      } else {
        yield put({
          type: 'save',
          payload: {
            correct: false,
          },
        });
      }
    },
    // 获取个人信息
    *getUserInfo({ payload }, { call, put }): any { 
      const response = yield call(FetchUserInfo, payload);
      console.log(response,'----------------')
      if (response) {
        yield put({
          type: 'save',
          payload: {
            userInfo: response,
          },
        });
      }
    },
    // 登出
    logout() {
      //localStorage.removeItem('token');
      storeUtil.remove('token');
      storeUtil.remove('Role');
      storeUtil.remove('userInfo');
      // 不是login界面的话跳转到login界面
      if (window.location.pathname !== '/login') {
        history.replace({
          pathname: '/login',
          search: stringify({
            redirect: window.location.href,
          }),
        });
      }
    },
  },

  reducers: {
    // effect获取数据处理方法
    changeLoginStatus(state, { payload }) {
      //localStorage.setItem('token', payload.data.token);
      // 默认一周过期
      storeUtil.set('token', payload.Token);
      // 0：管理员，1：保留，2：可审核报告，3：普通医生
      storeUtil.set('Role', payload.Role);
      storeUtil.set('userInfo', JSON.stringify(payload));
      return { ...state };
    },
    save(state, { payload }) {
      return { ...state, ...payload };
    },
  },
};
export default Model;
