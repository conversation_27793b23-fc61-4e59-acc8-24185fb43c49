import { request } from "umi";
import { QueryCheckListParamsType, QuerySeriesListParamsType, QueryDicomListParamsType } from "@/types/patients";
// 患者列表页的接口调用

// 检查列表
export async function FetchCheckList(params: QueryCheckListParamsType) {
  return request(`/api/public/getStudyListByPage`, {
    method: 'get',
    params: params,
  });
}

//序列列表
export async function FetchSeriesList(params: QuerySeriesListParamsType) {
  return request(`/api/public/listSeries`, {
    method: 'get',
    params: params,
  });
}

// 获取dicon 图片列表
export async function FetchImg(params: QueryDicomListParamsType) {
  return request(`/api/public/listInstances`, {
    method: 'get',
    params: {
      Offset: 1, 
      Limit: -1,
      ...params,
    },
  });
}
