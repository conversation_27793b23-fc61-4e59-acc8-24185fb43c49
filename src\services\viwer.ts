import axios from 'axios';
import storeUtil from '@/utils/store';
import { request } from 'umi';

// 直接获取所有Study列表
export async function FetchStudies(body: any) {
  return axios
    .get(`${CLOUD_API}/v1?Action=ListStudyInFilePrint`, {
      params: { Version: 20230101, Offset: 1, Limit: 10 },
      headers: { Authorization: storeUtil.get('token').value },
    })
    .then((res) => res.data);
}

//根据StudyID获取Series列表
export async function FetchSeries(studyId: string) {
  return axios
    .get(`${CLOUD_API}/public/listSeries`, {
      params: { Offset: 1, Limit: 10, StudyId: studyId },
      headers: { Authorization: storeUtil.get('token').value },
    })
    .then((res) => res.data);
}

// 根据SeriesID获取Instance列表
export async function FetchInstances(params: { SeriesId: string }) {
  const { SeriesId } = params;

  return axios
    .get(`${CLOUD_API}/public/listInstances`, {
      // params: { Offset: 1, Limit: -1, SeriesId: '2.25.252140670569857966247802777958478080178' },
      // params: { Offset: 1, Limit: -1, SeriesId: '1.2.276.0.7230010.3.1.3.8323329.45493.1688041701.341440' },
      params: { Offset: 1, Limit: -1, SeriesId },
      // params: { Offset: 1, Limit: -1, SeriesId: '1.2.276.0.7230010.3.1.3.3764181593.34476.1732074888.1155' },
      headers: { Authorization: storeUtil.get('token').value },
    })
    .then((res) => res.data);
}

// 获取Instance所有Tag
export async function FetchInstanceAllTag(params: { Id: number | string }) {
  const { Id } = params;
  // 使用从listInstances接口返回的List字段下的Id

  return axios
    .get(`${CLOUD_API}/public/getDicomAllTags`, {
      params: { Id },
      headers: { Authorization: storeUtil.get('token').value },
    })
    .then((res) => {
      return res.data;
    })
    .catch((error) => {
      console.error('=== getDicomAllTags接口调用失败 ===');
      console.error('错误信息:', error.message);
      console.error('错误详情:', error);
      if (error.response) {
        console.error('响应状态码:', error.response.status);
        console.error('响应数据:', error.response.data);
      }
      throw error;
    });
}

// 保存截图到服务器
export async function saveScreenshot(imageBlob: Blob, studyId: string) {
  const formData = new FormData();
  formData.append('image', imageBlob);
  formData.append('StudyId', studyId);
  
  // 生成符合YYMMDDHHMMSS格式的文件名前缀
  const now = new Date();
  const year = now.getFullYear().toString().slice(2); // 取年份后两位
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  const hours = now.getHours().toString().padStart(2, '0');
  const minutes = now.getMinutes().toString().padStart(2, '0');
  const seconds = now.getSeconds().toString().padStart(2, '0');
  
  // 生成文件名前缀，后端会添加序号
  const fileNamePrefix = `${year}${month}${day}${hours}${minutes}${seconds}`;
  formData.append('FileNamePrefix', fileNamePrefix);

  try {
    const response = await axios.post(`${CLOUD_API}/public/saveImage`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        Authorization: storeUtil.get('token')?.value,
      },
    });
    return response.data;
  } catch (error) {
    console.error('保存截图失败:', error);
    throw error;
  }
}

// 获取截图路径列表
export async function listImages(params: { StudyId: string }) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string } = {};
  if (token) headers.Authorization = token;
  return request(`${CLOUD_API}/public/listImage`, {
    method: 'GET',
    params,
    headers,
  });
}

// 删除截图
export async function deleteScreenshot(data: { Name: string }) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string } = {};
  if (token) headers.Authorization = token;

  // 参数放到 URL 查询参数中
  return request(`${CLOUD_API}/public/deleteImage?Name=${encodeURIComponent(data.Name)}`, {
    method: 'DELETE',
    headers,
  });
}

// 获取单张截图
export async function getImage(params: { StudyId?: string; ImageName: string }) {
  const token = storeUtil.get('token')?.value;
  const headers: { Authorization?: string } = {};
  if (token) headers.Authorization = token;

  try {
    return await request(`${CLOUD_API}/public/getImage`, {
      method: 'GET',
      params,
      headers,
      responseType: 'blob', // 返回图片blob数据
    });
  } catch (error: any) {
    // umi-request 在处理 blob 响应时的已知问题：
    // 成功响应被错误地当作异常抛出，但实际数据在 error.data 中
    if (error?.data && error.data instanceof Blob && error.status === 200) {
      // 这实际上是成功响应，返回 Blob 数据
      return error.data;
    }
    // 如果是真正的错误，重新抛出
    throw error;
  }
}
