import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { ConfigProvider, Modal, Flex, Avatar, Space, Form, Input, Button, message,  } from 'antd';
import { ExclamationCircleOutlined, UserOutlined, CloseOutlined } from '@ant-design/icons';
import { FetchNetworkConfig, SaveNetworkConfig, CheckIpPort } from '@/services/user'
import { LoginType, NetworkConfigType } from '@/types/user';
import styles from './index.less';
import CustomIcon from '../CustomIcon';

export interface NetworkModalProps {
  isModalOpen: boolean;
  handleOk: (val: boolean) => void;
  handleCancel: (val: boolean) => void;
  networkConfig: NetworkConfigType;
  dispatch: any;
}
// 修复类型不匹配问题，明确指定 React.FC 的泛型为 NetworkModalProps
const NetworkModal: React.FC<NetworkModalProps> = ({  isModalOpen, handleOk, handleCancel, networkConfig, dispatch }) => {
  const [icon,setIcon] = useState<React.ReactNode | string>('');
  const [checked,setChecked] = useState<boolean>(false);
 const [form] = Form.useForm();
 const [loading, setLoading] = useState(false);
  // 确定
  const onOk = () => { 
    form.validateFields().then(res => {
      setLoading(true);
      let params = {
        ...res,
        id: networkConfig?.id || '',
      }
      SaveNetworkConfig(params).then(res => {
        message.success('保存配置成功！');
      }).catch(err => {
        console.error('Error:', err);
      }).finally(() => { 
        getNetworkConfig();
        setLoading(false);
        handleOk(false);
      })
    })
  };
  // 取消
  const onCancel = () => { 
    form.resetFields();
    setChecked(false);
    handleCancel(false);
  };

  // 获取网络配置
  const getNetworkConfig = async () => {
    const res = await FetchNetworkConfig()
    dispatch({
      type: 'user/save',
      payload: {
        networkConfig: res,
      },
    })
  }

  // 测试网络配置
  const checkNetwork = async () => { 
    try {
      // 验证表单字段，等待验证结果
      form.validateFields().then(res => { 
        setChecked(true);
        setIcon(<CustomIcon type="icon-ceshilianjiezhong" />)
        CheckIpPort(res).then(res => { 
          setIcon(<CustomIcon type="icon-ceshilianjiechenggong" />)
          message.success('测试连接成功')
        }).catch(err => {
          setIcon(<CustomIcon type="icon-ceshilianjieshibai" />)
        }).finally(() => { 
        })
        
  });
    // 调用 CheckIpPort 服务进行网络检查
    // const checkResult = await CheckIpPort(res.ip, res.port);
    // console.log('网络检查结果', checkResult);
  } catch (error) {
    // 处理表单验证失败的情况
    console.error('表单验证失败', error);
  }
}

  useEffect(() => {
    getNetworkConfig()
  }, [])
  useEffect(() => { 
    form.setFieldsValue({
      ip: networkConfig?.ip,
      port: networkConfig?.port,
      aeTitle: networkConfig?.aeTitle,
    })
  }, [isModalOpen])

  return (
    <ConfigProvider
      theme={{
        components: {
          Form: {
            fontSize: 16,
            colorPrimary: '#474645',
            labelFontSize: 16,
            labelColor: '#ffffff',
            itemMarginBottom: 24,
            verticalLabelPadding: '0 0 4px',
          },
        },
      }}
    >
      <Modal
        classNames={{
          header: styles.modalHeader,
          content: styles.modalContent,
          body: styles.modalBody,
          footer: styles.modalFooter,
        }}
        title="网络配置"
        centered
        width={1300}
        open={isModalOpen}
        confirmLoading={loading}
        onOk={onOk}
        onCancel={onCancel}
        closeIcon={<div className={styles.closeIcon}><CloseOutlined /></div>}
        footer={(_, { OkBtn, CancelBtn }) => (
          <>
            <OkBtn />
          </>
        )}
      >
        <Form<NetworkConfigType> 
          form={form}
          layout="vertical"
          autoComplete="off"
        >
          <Form.Item
            label="IP地址"
            name="ip"
            rules={[
              { required: true, message: '请输入IP地址!' },
              // 新增正则校验，确保输入只包含数字和.
              { pattern: /^[0-9.]+$/, message: '请输入正确的IP地址！' }
            ]}
          >
            <Input 
              placeholder="请输入IP地址" 
              allowClear 
              maxLength={99} 
              // 限制输入框只能输入数字和. 字符
              onKeyPress={(e) => {
                const charCode = e.which ? e.which : e.keyCode;
                const charStr = String.fromCharCode(charCode);
                if (!/^[0-9.]$/.test(charStr)) {
                  e.preventDefault();
                }
              }}
            />
          </Form.Item>

          <Form.Item
            label="端口号"
            name="port"
            rules={[
              { required: true, message: '请输入端口号!' },
              // 新增正则校验，确保输入只包含数字和.
              { pattern: /^[0-9]+$/, message: '请输入正确端口号！' }
            ]}
          >
            <Input
              placeholder="请输入端口号" 
              allowClear 
              maxLength={99} 
              // 限制输入框只能输入数字和. 字符
              onKeyPress={(e) => {
                const charCode = e.which ? e.which : e.keyCode;
                const charStr = String.fromCharCode(charCode);
                if (!/^[0-9]$/.test(charStr)) {
                  e.preventDefault();
                }
              }}
            />
          </Form.Item>
          <Form.Item
            label="AE Title"
            name="aeTitle"
            rules={[
              { required: true, whitespace: true, message: '请输入AE Title!' },
              { max: 99, message: '输入的字符长度不能超过99！' },
            ]}
          >
            <Input  placeholder="请输入AE Title" allowClear />
          </Form.Item>
          <Form.Item
            label="测试结果"
          >
            <Button 
              className={ checked ? styles.checkBtn : ''}
              type='primary'
              icon={icon}
              onClick={checkNetwork}
            >
              测试连接
            </Button>
            <div className={styles.tips}>注：保存前您可以点击[测试连接]验证目标端口通信是否正常。</div>
          </Form.Item>
        </Form>
      </Modal>
    </ConfigProvider>
  );
};
const mapStateToProps = ({user}: { user: LoginType }) => {
  return {
    networkConfig: user.networkConfig,
  };
};

export default connect(mapStateToProps)(NetworkModal)