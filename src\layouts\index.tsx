import { ProLayout } from '@ant-design/pro-components';
import { Outlet, history } from 'umi';
import { useEffect, useState } from 'react';
import type { Dispatch } from 'umi';
import type { MenuProps } from 'antd';
import { connect } from 'dva';
import { Dropdown, ConfigProvider, Button, Modal, Space, Image, Flex, Divider } from 'antd';
import { CloseOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import PersonModal from '@/components/PersonModal';
import NetworkModal from '@/components/NetworkModal';
import CustomIcon from '@/components/CustomIcon';
import { LoginType, UserInfoType } from '@/types/user';
import moment from 'moment';
import storeUtil from '@/utils/store';
import styles from './index.less';

moment.locale('zh-cn');

// 退出登录在顶层实现
interface LoginProps {
  dispatch: Dispatch;
  data?: any;
  userInfo?: UserInfoType;
}

const CssSetItemStyle = {
  width: '135px',
  color: '#ffffff',
};
const Layout: React.FC<LoginProps> = (props) => {
  const { userInfo } = props;
  // 对登录界面使用不同的layout
  if (location.pathname === '/login') {
    return (
      <div>
        <Outlet />
      </div>
    );
  }

  // 对阅片页面使用不同的layout，不显示顶部设置栏
  if (location.pathname === '/viewer') {
    return (
      <ConfigProvider
        theme={{
          token: {
            colorPrimary: '#1f69b4',
            colorText: '#ffffff',
            colorTextDisabled: '#ffffff',
            colorTextDescription: '#ffffff',
          },
          components: {
            Input: {
              activeBorderColor: '#FFFFFF',
              hoverBorderColor: '#FFFFFF',
            },
            Button: {
              colorPrimary: '#1f69b4',
              defaultBg: '#262628',
              defaultColor: '#ffffff',
            },
            Select: {
              activeBorderColor: '#FFFFFF',
              selectorBg: '#333233',
              optionSelectedBg: '#1f69b4',
              optionSelectedColor: '#ffffff',
              optionActiveBg: '#1f69b4',
            },
            Table: {
              colorBgContainer: '#262628',
              headerBg: '#333233',
              borderColor: '#333233',
              headerColor: '#ffffff',
              colorText: '#ffffff',
              rowHoverBg: '#1F69B4',
              rowSelectedBg: '#1F69B4',
              rowSelectedHoverBg: '#1F69B4',
              cellPaddingBlock: 6,
            },
          },
        }}
      >
        <div style={{ height: '100vh', background: '#191919' }}>
          <Outlet />
        </div>
      </ConfigProvider>
    );
  }

  let items: MenuProps['items'] = [];
  // 全局设置项
  switch (userInfo?.Role || storeUtil.get('Role').value) {
    case 'check': // 主任医生
      items = [
        {
          key: '1',
          label: (
            <Flex style={CssSetItemStyle} justify='center' gap={6}>
              <CustomIcon type='icon-mobanguanli-01' />
              模板管理
            </Flex>
          ),
        },
        {
          key: '2',
          label: (
            <Flex style={CssSetItemStyle} justify='center' gap={6}>
              <CustomIcon type='icon-yonghuguanli' />
              用户管理
            </Flex>
          ),
        },
        {
          key: '4',
          label: (
            <Flex style={CssSetItemStyle} justify='center' gap={6}>
              <CustomIcon type='icon-gerenzhongxin' />
              个人中心
            </Flex>
          ),
        },
        {
          key: '5',
          label: (
            <Flex style={CssSetItemStyle} justify='center' gap={6}>
              <CustomIcon type='icon-tuichudenglu' />
              退出登录
            </Flex>
          ),
        },
      ];
      break;
    case 'admin': // 管理员
      items = [
        {
          key: '2',
          label: (
            <Flex style={CssSetItemStyle} justify='center' gap={6}>
              <CustomIcon type='icon-yonghuguanli' />
              用户管理
            </Flex>
          ),
        },
        {
          key: '3',
          label: (
            <Flex style={CssSetItemStyle} justify='center' gap={6}>
              <CustomIcon type='icon-wangluoshezhi' />
              网络配置
            </Flex>
          ),
        },
        {
          key: '4',
          label: (
            <Flex style={CssSetItemStyle} justify='center' gap={6}>
              <CustomIcon type='icon-gerenzhongxin' />
              个人中心
            </Flex>
          ),
        },
        {
          key: '5',
          label: (
            <Flex style={CssSetItemStyle} justify='center' gap={6}>
              <CustomIcon type='icon-tuichudenglu' />
              退出登录
            </Flex>
          ),
        },
      ];
      break;
    case 'doctor': // 诊断医生
      items = [
        {
          key: '1',
          label: (
            <Flex style={CssSetItemStyle} justify='center' gap={6}>
              <CustomIcon type='icon-mobanguanli-01' />
              模板管理
            </Flex>
          ),
        },
        {
          key: '4',
          label: (
            <Flex style={CssSetItemStyle} justify='center' gap={6}>
              <CustomIcon type='icon-gerenzhongxin' />
              个人中心
            </Flex>
          ),
        },
        {
          key: '5',
          label: (
            <Flex style={CssSetItemStyle} justify='center' gap={6}>
              <CustomIcon type='icon-tuichudenglu' />
              退出登录
            </Flex>
          ),
        },
      ];
      break;
    default:
      items = [
        {
          key: '5',
          label: (
            <Flex style={CssSetItemStyle} justify='center' gap={6}>
              <CustomIcon type='icon-tuichudenglu' />
              退出登录
            </Flex>
          ),
        },
      ];
      break;
  }

  // 修复 key 类型隐式具有 any 类型的问题，正确定义参数类型
  const handleSelectDropdown = ({ key }: { key: string }) => {
    switch (key) {
      case '1':
        history.push('/templateManagement');
        break;
      case '2':
        history.push('/user');
        break;
      case '3':
        changeNetworkModal(true);
        break;
      case '4':
        changePersonModal(true);
        break;
      case '5':
        setIsModalOpen(true);
        break;
    }
  };
  // 退出登录
  const [isModalOpen, setIsModalOpen] = useState(false);
  const handleOk = () => {
    setIsModalOpen(false);
    props.dispatch({
      type: 'user/logout',
    });
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };
  // 个人中心
  const [isPersonModalOpen, setIsPersonModalOpen] = useState(false);
  const changePersonModal = (value: boolean) => {
    setIsPersonModalOpen(value);
  };
  // 网络配置
  const [isNetworkModalOpen, setIsNetworkModalOpen] = useState(false);
  const changeNetworkModal = (value: boolean) => {
    setIsNetworkModalOpen(value);
  };
  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#1f69b4',
          colorText: '#ffffff',
          colorTextDisabled: '#ffffff73',
          colorTextDescription: 'red',
        },

        components: {
          Input: {
            activeBorderColor: '#FFFFFF',
            hoverBorderColor: '#1f69b4',
          },
          Button: {
            colorPrimary: '#1f69b4',
            defaultBg: '#262628',
            defaultColor: '#ffffff',
          },
          Select: {
            activeBorderColor: '#FFFFFF',
            selectorBg: '#333233',
            optionSelectedBg: '#1f69b4',
            optionSelectedColor: '#ffffff',
            optionActiveBg: '#1f69b4',
          },
          Dropdown: {
            /* 这里是你的组件 token */
            colorBgElevated: '#302f2f',
            colorText: '#ffffff',
            colorPrimary: '#ffffff',
            controlItemBgActive: '#1f69b4',
            controlItemBgActiveHover: '#1f69b4',
            controlItemBgHover: '#1f69b4',
            borderRadiusLG: 0,
            padding: 48,
            fontSize: 16,
            paddingBlock: 14,
          },
          Table: {
            colorBgContainer: '#262628',
            headerBg: '#333233',
            borderColor: '#333233',
            headerColor: '#ffffff',
            colorText: '#ffffff',
            rowHoverBg: '#1F69B4',
            rowSelectedBg: '#1F69B4',
            rowSelectedHoverBg: '#1F69B4',
            cellPaddingBlock: 6,
          },
          Modal: {
            titleColor: '#ffffff',
            titleFontSize: 18,
            fontWeightStrong: 400,
          },
        },
      }}
    >
      <ProLayout
        layout='top'
        token={{
          bgLayout: '#191919',
          header: {
            colorBgHeader: '#302f2f',
          },
        }}
        fixedHeader={true}
        headerRender={() => (
          <Flex justify='space-between' align='center' style={{ height: '100%', padding: '0 24px' }}>
            <img src='/icon/ray_header_logo.png' height={37} style={{ cursor: 'pointer' }} onClick={() => history.push('/')} />
            <Dropdown
              menu={{
                items,
                onClick: handleSelectDropdown,
              }}
              placement='bottomLeft'
            >
              <Button
                type='text'
                icon={<CustomIcon className={styles.DropIcon} type='icon-shezhi-01' />}
                size='large'
                style={{ color: '#ffffff' }}
              >
                设置
              </Button>
            </Dropdown>
          </Flex>
        )}
        contentStyle={{ padding: '16px' }}
      >
        <Outlet />
      </ProLayout>
      {/* 退出登录 */}
      <Modal
        classNames={{ 
          header: styles.modalHeader,
          content: styles.modalContent,
          body: styles.modalBody,
          footer: styles.modalFooter,
        }}
        title='提示'
        centered
        width={680}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        closeIcon={
          <div className={styles.closeIcon}>
            <CloseOutlined />
          </div>
        }
      >
        <Flex style={{ width: '100%', margin: '80px auto 40px', fontSize: '18px' }} justify='center' gap={8}>
          <ExclamationCircleOutlined style={{ color: '#EFB844', fontSize: '22px' }} />
          <span>确认退出登录吗？</span>
        </Flex>
      </Modal>
      {/* 个人中心 */}
      <PersonModal isModalOpen={isPersonModalOpen} handleOk={changePersonModal} handleCancel={changePersonModal} />
      {/* 网络配置 */}
      <NetworkModal isModalOpen={isNetworkModalOpen} handleOk={changeNetworkModal} handleCancel={changeNetworkModal} />
    </ConfigProvider>
  );
};

const mapStateToProps = ({ user }: { user: LoginType }) => {
  return {
    userInfo: user.userInfo,
  };
};

export default connect(mapStateToProps)(Layout);
