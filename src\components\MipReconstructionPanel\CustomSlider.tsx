import React, { useRef, useState, useCallback, useEffect } from 'react';
import './CustomSlider.less';

// 节流函数，限制函数调用频率
const throttle = (func: Function, delay: number) => {
  let timeoutId: NodeJS.Timeout | null = null;
  let lastExecTime = 0;
  
  return (...args: any[]) => {
    const currentTime = Date.now();
    
    if (currentTime - lastExecTime > delay) {
      func(...args);
      lastExecTime = currentTime;
    } else {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      timeoutId = setTimeout(() => {
        func(...args);
        lastExecTime = Date.now();
      }, delay - (currentTime - lastExecTime));
    }
  };
};

interface CustomSliderProps {
  min: number;
  max: number;
  value: number;
  onChange: (value: number) => void;
  className?: string;
  color?: string;
  disabled?: boolean;
}

const CustomSlider: React.FC<CustomSliderProps> = ({
  min,
  max,
  value,
  onChange,
  className = '',
  color = '#1890ff',
  disabled = false,
}) => {
  const sliderRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStartX, setDragStartX] = useState(0);
  const [dragStartValue, setDragStartValue] = useState(0);

  // 计算滑动条的百分比位置
  const percentage = ((value - min) / (max - min)) * 100;

  // 根据鼠标位置计算值
  const calculateValueFromPosition = useCallback((clientX: number) => {
    if (!sliderRef.current) return value;
    
    const rect = sliderRef.current.getBoundingClientRect();
    const relativeX = clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (relativeX / rect.width) * 100));
    const newValue = Math.round(min + (percentage / 100) * (max - min));
    
    return Math.max(min, Math.min(max, newValue));
  }, [min, max, value]);

  // 鼠标按下事件
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (disabled) return;
    
    e.preventDefault();
    e.stopPropagation();
    
    const newValue = calculateValueFromPosition(e.clientX);
    onChange(newValue); // 立即更新，不使用节流
    
    setIsDragging(true);
    setDragStartX(e.clientX);
    setDragStartValue(newValue);
  }, [disabled, calculateValueFromPosition, onChange]);

  // 节流的onChange函数
  const throttledOnChange = useCallback(
    throttle((newValue: number) => {
      onChange(newValue);
    }, 4), // 约250fps，更流畅的响应
    [onChange]
  );

  // 鼠标移动事件
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || disabled) return;
    
    e.preventDefault();
    
    const newValue = calculateValueFromPosition(e.clientX);
    throttledOnChange(newValue);
  }, [isDragging, disabled, calculateValueFromPosition, throttledOnChange]);

  // 鼠标释放事件
  const handleMouseUp = useCallback(() => {
    if (isDragging) {
      setIsDragging(false);
    }
  }, [isDragging]);

  // 键盘事件
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (disabled) return;
    
    let newValue = value;
    
    switch (e.key) {
      case 'ArrowLeft':
      case 'ArrowDown':
        newValue = Math.max(min, value - 1);
        break;
      case 'ArrowRight':
      case 'ArrowUp':
        newValue = Math.min(max, value + 1);
        break;
      case 'Home':
        newValue = min;
        break;
      case 'End':
        newValue = max;
        break;
      default:
        return;
    }
    
    e.preventDefault();
    onChange(newValue);
  }, [disabled, value, min, max, onChange]);

  // 添加全局鼠标事件监听
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  return (
    <div
      ref={sliderRef}
      className={`custom-slider ${className} ${disabled ? 'disabled' : ''} ${isDragging ? 'dragging' : ''}`}
      onMouseDown={handleMouseDown}
      onKeyDown={handleKeyDown}
      tabIndex={disabled ? -1 : 0}
      role="slider"
      aria-valuemin={min}
      aria-valuemax={max}
      aria-valuenow={value}
      aria-disabled={disabled}
    >
      {/* 滑动条轨道 */}
      <div className="custom-slider-rail" />
      
      {/* 滑动条已填充部分 */}
      <div 
        className="custom-slider-track" 
        style={{ 
          width: `${percentage}%`,
          backgroundColor: color 
        }} 
      />
      
      {/* 滑动条手柄 */}
      <div 
        className="custom-slider-handle" 
        style={{ 
          left: `${percentage}%`,
          borderColor: color 
        }}
      >
        {/* 工具提示 */}
        <div className="custom-slider-tooltip">
          {value}
        </div>
      </div>
    </div>
  );
};

export default CustomSlider;