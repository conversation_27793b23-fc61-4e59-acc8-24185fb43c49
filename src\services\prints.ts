import storeUtil from '@/utils/store';
import axios from 'axios';

// 获取dicom数据
export async function getDicomFile(body: any) {
  return axios
    .get(`${CLOUD_API}/v1?Action=GetDicomFileInPrint`, { params: body, headers: { Authorization: storeUtil.get('token').value } })
    .then((res) => res.data);
}

// 获取患者
export async function getPatient(body: any) {
  return axios
    .get(`${CLOUD_API}/v1?Action=GetPatientInFilePrint`, {
      params: body,
      headers: { Authorization: storeUtil.get('token').value },
    })
    .then((res) => res.data);
}

// 获取所有study
export async function listStudy(body: any) {
  return axios
    .get(`${CLOUD_API}/v1?Action=ListStudyInFilePrint`, {
      params: body,
      headers: { Authorization: storeUtil.get('token').value },
    })
    .then((res) => res.data);
}

// 获取单个study
export async function getStudy(body: any) {
  return axios
    .get(`${CLOUD_API}/v1?Action=GetStudyInFilePrint`, { params: body, headers: { Authorization: storeUtil.get('token').value } })
    .then((res) => res.data);
}

// 获取所有series
export async function listSeries(body: any) {
  return axios
    .get(`${CLOUD_API}/v1?Action=ListSeriesInFilePrint`, {
      params: body,
      headers: { Authorization: storeUtil.get('token').value },
    })
    .then((res) => res.data);
}

// 获取单个series
export async function getSeries(body: any) {
  return axios
    .get(`${CLOUD_API}/v1?Action=GetSeriesInFilePrint`, {
      params: body,
      headers: { Authorization: storeUtil.get('token').value },
    })
    .then((res) => res.data);
}

// 获取所有instance
export async function listInstance(body: any) {
  return axios
    .get(`${CLOUD_API}/v1?Action=ListInstanceInFilePrint`, {
      params: body,
      headers: { Authorization: storeUtil.get('token').value },
    })
    .then((res) => res.data);
}
