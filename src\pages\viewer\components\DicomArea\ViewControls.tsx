import React, { useEffect, useState } from 'react';
import { Select } from 'antd';
import { connect, Dispatch } from 'umi';
import { Enums } from '@cornerstonejs/core';
import { ViewType } from '@/models/views';
import styles from './index.less';

const { Option } = Select;

interface ViewControlsProps {
  dispatch: Dispatch;
  views: ViewType;
  orientation: string;
  onOrientationChange: (orientation: string) => void;
}

const orientationOptions = [
  { label: 'AXIAL', value: Enums.OrientationAxis.AXIAL },
  { label: 'SAGITTAL', value: Enums.OrientationAxis.SAGITTAL },
  { label: 'CORONAL', value: Enums.OrientationAxis.CORONAL },
];
const ViewControls: React.FC<ViewControlsProps> = ({ dispatch, views, orientation, onOrientationChange }) => {
  console.log('ViewControls', orientation);
  const handleOrientationChange = (value: string) => {
    onOrientationChange(value);
  };

  return (
    <div className={styles.viewControls}>
      <div className={styles.controlItem}>
        <span className={styles.controlLabel}>视图：</span>
        <Select
          value={orientation}
          onChange={handleOrientationChange}
          style={{ width: 120 }}
          dropdownStyle={{
            backgroundColor: 'rgba(30, 30, 30, 0.9)',
            color: '#fff',
            borderColor: '#444',
          }}
        >
          {orientationOptions.map((option) => (
            <Option key={option.value} value={option.value}>
              {option.label}
            </Option>
          ))}
        </Select>
      </div>
    </div>
  );
};

const mapStateToProps = ({ views }: { views: ViewType }) => {
  return {
    views,
  };
};

export default connect(mapStateToProps)(ViewControls);
