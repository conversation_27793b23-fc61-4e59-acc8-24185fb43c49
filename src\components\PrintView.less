.description {
  //样式穿透
  :global {
    // scrollbar
    .ant-descriptions-view {
      //   height: 15vh;
      overflow-y: auto !important;
      &::-webkit-scrollbar {
        height: 0px;
        width: 10px;
        overflow-y: auto;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 5px;
        background: #595959;
      }
      &::-webkit-scrollbar-track {
        -webkit-box-shadow: 0;
        border-radius: 5px;
        background: #7f7f7f;
      }
    }
    .ant-descriptions-item-label {
      color: black;
      font-weight: bold;
    }
  }
}

.textArea {
  overflow-y: auto !important;
  &::-webkit-scrollbar {
    height: 0px;
    width: 10px;
    overflow-y: auto;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 5px;
    background: #b8b8b8;
  }
  &::-webkit-scrollbar-track {
    -webkit-box-shadow: 0;
    border-radius: 5px;
    background: #e6e6e6;
  }
}

.quill {
  border-radius: 5px;
  //样式穿透
  :global {
    .ql-editor {
      background-color: white;
      color: black;
      border-bottom-left-radius: 10px;
      border-bottom-right-radius: 10px;
      // scrollbar
      height: 60vh;
      overflow-y: auto !important;
      &::-webkit-scrollbar {
        height: 0px;
        width: 10px;
        overflow-y: auto;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 5px;
        background: #595959;
      }
      &::-webkit-scrollbar-track {
        -webkit-box-shadow: 0;
        border-radius: 5px;
        background: #7f7f7f;
      }
    }
    .ql-container {
      // border-color: transparent;
      border-bottom-left-radius: 10px;
      border-bottom-right-radius: 10px;
    }
    .ql-toolbar {
      background-color: white;
      color: black;
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
    }
  }
}
