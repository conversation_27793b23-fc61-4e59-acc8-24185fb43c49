.rich-text-editor {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  //样式穿透
  :global {
    .ql-container {
      border: none !important;
      border-radius: 0;
      flex: 1;
      display: flex;
      flex-direction: column;
    }
    
    .ql-editor {
      background-color: white;
      color: black;
      border: none !important;
      border-radius: 0;
      flex: 1;
      min-height: 0;
      // scrollbar - 使用全局样式
      overflow-y: auto !important;
    }
    
    .ql-toolbar {
      background-color: #EDEDED !important;
      color: black;
      border: none !important;
      border-bottom: 1px solid #d9d9d9 !important;
      border-radius: 0;
      flex-shrink: 0;
    }
    
    // 确保整个编辑器的背景和边框一致
    .ql-snow {
      border: none !important;
    }

    // 自定义按钮样式 - 使用自定义图标
    .ql-undo:before {
      content: "";
      background-image: url('/icon/report/recover.png') !important;
      background-size: 16px 16px;
      background-repeat: no-repeat;
      background-position: center;
      display: inline-block;
      width: 16px;
      height: 16px;
    }
    
    // 隐藏默认的SVG图标
    .ql-undo svg,
    .ql-redo svg,
    .ql-clean svg,
    .ql-format-painter svg,
    .ql-image svg,
    .ql-symbols svg {
      display: none !important;
    }
    
    .ql-redo:before {
      content: "";
      background-image: url('/icon/report/recover.png') !important;
      background-size: 16px 16px;
      background-repeat: no-repeat;
      background-position: center;
      display: inline-block;
      width: 16px;
      height: 16px;
      transform: scaleX(-1); // 水平翻转来表示重做
    }
    
    .ql-clean:before {
      content: "";
      background-image: url('/icon/report/quash.png') !important;
      background-size: 16px 16px;
      background-repeat: no-repeat;
      background-position: center;
      display: inline-block;
      width: 16px;
      height: 16px;
    }
    
    .ql-format-painter:before {
      content: "";
      background-image: url('/icon/report/format_painter.png') !important;
      background-size: 16px 16px;
      background-repeat: no-repeat;
      background-position: center;
      display: inline-block;
      width: 16px;
      height: 16px;
    }
    
    .ql-header:before {
      content: "T";
      font-size: 16px;
      font-weight: bold;
      color: inherit;
      display: inline-block;
      width: 16px;
      height: 16px;
      text-align: center;
      line-height: 16px;
    }
    
    .ql-header-toggle:before {
      content: "T";
      font-size: 16px;
      font-weight: bold;
      color: inherit;
      display: inline-block;
      width: 16px;
      height: 16px;
      text-align: center;
      line-height: 16px;
    }
    
    .ql-size-toggle:before {
      content: "";
      background-image: url('/icon/report/font_size.png') !important;
      background-size: 16px 16px;
      background-repeat: no-repeat;
      background-position: center;
      display: inline-block;
      width: 16px;
      height: 16px;
    }
    
    .ql-image:before {
      content: "";
      background-image: url('/icon/report/image.png') !important;
      background-size: 16px 16px;
      background-repeat: no-repeat;
      background-position: center;
      display: inline-block;
      width: 16px;
      height: 16px;
    }
    
    .ql-symbols:before {
      content: "";
      background-image: url('/icon/report/symbol.png') !important;
      background-size: 16px 16px;
      background-repeat: no-repeat;
      background-position: center;
      display: inline-block;
      width: 16px;
      height: 16px;
    }

    // 撤销和恢复按钮移除视觉变化效果
    .ql-toolbar button.ql-undo:hover,
    .ql-toolbar button.ql-redo:hover {
      color: inherit !important;
      background-color: transparent !important;
    }
    
    .ql-toolbar button.ql-undo.ql-active,
    .ql-toolbar button.ql-redo.ql-active {
      color: inherit !important;
      background-color: transparent !important;
    }
    
    .ql-toolbar button.ql-undo:focus,
    .ql-toolbar button.ql-redo:focus {
      outline: none !important;
      color: inherit !important;
      background-color: transparent !important;
    }

    // 工具栏按钮通用样式
    .ql-toolbar .ql-formats {
      margin-right: 0px;
    }
    
    .ql-toolbar button {
      padding: 5px;
      margin: 2px 11.25px;
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
    }
    
    // 第一个按钮左边距为0
    .ql-toolbar .ql-formats:first-child button:first-child {
      margin-left: 0;
    }
    
    // 最后一个按钮右边距为0
    .ql-toolbar .ql-formats:last-child button:last-child {
      margin-right: 0;
    }
    
    .ql-toolbar button:hover {
      color: #06c;
    }
    
    .ql-toolbar button.ql-active {
      color: #06c;
      background-color: #e6f7ff;
    }

    // 确保所有自定义图标按钮有正确的尺寸
    .ql-toolbar button {
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
    }

    // 为自定义图标按钮设置统一的hover效果
    .ql-toolbar button:hover::before {
      opacity: 0.7;
    }

    .ql-toolbar button.ql-active::before {
      opacity: 1;
      filter: brightness(1.2);
    }

    // 确保自定义图标按钮覆盖默认样式
    .ql-toolbar button.ql-undo,
    .ql-toolbar button.ql-redo,
    .ql-toolbar button.ql-clean,
    .ql-toolbar button.ql-format-painter,
    .ql-toolbar button.ql-image,
    .ql-toolbar button.ql-symbols,
    .ql-toolbar button.ql-header-toggle,
    .ql-toolbar button.ql-size-toggle {
      background: none !important;
      border: none !important;
      overflow: hidden;
    }

    // 确保before伪元素正确定位
    .ql-toolbar button.ql-undo:before,
    .ql-toolbar button.ql-redo:before,
    .ql-toolbar button.ql-clean:before,
    .ql-toolbar button.ql-format-painter:before,
    .ql-toolbar button.ql-image:before,
    .ql-toolbar button.ql-symbols:before,
    .ql-toolbar button.ql-header-toggle:before,
    .ql-toolbar button.ql-size-toggle:before {
      position: relative;
      z-index: 1;
    }
  }
}

// quill字体样式
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='SimSun']::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='SimSun']::before {
  font-family: 'SimSun' !important;
  content: '宋体';
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='SimHei']::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='SimHei']::before {
  font-family: 'SimHei';
  content: '黑体';
}

.ql-snow
  .ql-picker.ql-font
  .ql-picker-label[data-value='Microsoft-YaHei']::before,
.ql-snow
  .ql-picker.ql-font
  .ql-picker-item[data-value='Microsoft-YaHei']::before {
  font-family: '微软雅黑';
  content: '微软雅黑';
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='KaiTi']::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='KaiTi']::before {
  font-family: 'KaiTi' !important;
  content: '楷体';
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='FangSong']::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='FangSong']::before {
  font-family: 'FangSong';
  content: '仿宋';
}

/*  设置每个字体的css字体样式 */
.ql-font-SimSun {
  font-family: 'SimSun';
}

.ql-font-SimHei {
  font-family: 'SimHei';
}

.ql-font-Microsoft-YaHei {
  font-family: '微软雅黑';
}

.ql-font-KaiTi {
  font-family: 'KaiTi';
}

.ql-font-FangSong {
  font-family: 'FangSong';
}

// quill字体大小样式
.ql-snow .ql-picker.ql-size {
  width: 70px; // 菜单栏占比宽度
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='14px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='14px']::before {
  content: '14px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='16px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='16px']::before {
  content: '16px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='20px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='20px']::before {
  content: '20px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='24px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='24px']::before {
  content: '24px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='36px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='36px']::before {
  content: '36px';
}

// 只读禁用样式
.disabled-toolbar {
  :global {
    .ql-toolbar {
      background-color: #f0f0f0 !important;
      pointer-events: none;
      opacity: 0.7;

      button {
        cursor: not-allowed;
      }
    }
  }
}

.disable-toolbar-buttons {
  .ql-toolbar {
    background-color: #AEAEAF !important;
    .ql-formats {
      button, .ql-picker {
        cursor: not-allowed !important;
        pointer-events: none !important;
        opacity: 0.5;
      }
    }
  }
}

// 选择性禁用工具栏按钮样式
:global {
  // 只有格式化工具在禁用时应用这些样式
  .ql-toolbar button.ql-format-painter.ql-disabled,
  .ql-toolbar button.ql-header-toggle.ql-disabled,
  .ql-toolbar button.ql-size-toggle.ql-disabled,
  .ql-toolbar button.ql-bold.ql-disabled,
  .ql-toolbar button.ql-italic.ql-disabled,
  .ql-toolbar button.ql-underline.ql-disabled {
    cursor: not-allowed !important;
    pointer-events: none !important;
    opacity: 0.4 !important;
    background-color: transparent !important;
    
    &:hover {
      color: inherit !important;
      background-color: transparent !important;
    }
    
    &:before {
      opacity: 0.4 !important;
      filter: grayscale(100%) !important;
    }
  }
  
  // 确保关闭工具始终保持正常状态，即使有ql-disabled类也不影响
  .ql-toolbar button.ql-undo,
  .ql-toolbar button.ql-redo,
  .ql-toolbar button.ql-image,
  .ql-toolbar button.ql-symbols,
  .ql-toolbar .ql-list {
    cursor: pointer !important;
    pointer-events: auto !important;
    opacity: 1 !important;
    
    &:before {
      opacity: 1 !important;
      filter: none !important;
    }
    
    &:hover {
      color: #06c !important;
    }
  }
} 

// 字数显示样式
.word-count-display {
  background-color: transparent;
  border: none;
  border-radius: 0;
  padding: 10px 12px;
  font-size: 12px;
  color: white;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
  
  .word-count-text {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    
    .word-count-limit {
      color: white;
      font-weight: 500;
    }
  }
}

.word-count-top {
  margin-bottom: 4px;
}

.word-count-bottom {
  margin-top: -8px;
  margin-bottom: 4px;
}

// 当字数超过限制时的警告样式
.word-count-display.word-count-warning {
  background-color: transparent;
  border: none;
  color: #ff6b6b;
  
  .word-count-text {
    .word-count-limit {
      color: #ff6b6b;
      font-weight: 600;
    }
  }
} 