import React, { useEffect, useState } from 'react';
import { Checkbox, Flex } from 'antd';
import type { CheckboxProps } from 'antd'
import styles from './index.less';
// 定义 FilterProps 接口，支持 'radio' 和 'checkbox' 类型
interface FilterProps {
  type: 'radio' | 'checkbox';
  options: {
    label: string;
    value: string;
  }[];
  onChange?: (value: string[]) => void;
}

const CheckboxGroup = Checkbox.Group;

const Filter: React.FC<FilterProps> = ({ type, options,onChange }) => {
  const [selectedValues, setSelectedValues] = useState<string[]>([]);
  const checkAll = options.length === selectedValues.length;
  const indeterminate = selectedValues.length > 0 && selectedValues.length < options.length;
  // 单选
  const handleChange = (value: string) => {
      setSelectedValues([value]);
      onChange && onChange([value]);
  };
  // 多选
  const handleCheckboxChange = (list: string[]) => {
    setSelectedValues(list);
    onChange && onChange(list);
  };
  // 全选
  const onCheckAll: CheckboxProps['onChange'] = (e) => {
    let list = e.target.checked ? options.map(item => item.value) : []
    setSelectedValues(list);
    onChange && onChange(list);
  };
  // 渲染单选
  const radio = () => {
    return (
      <div className={styles.radio}>
        {options.map((option) => (
          <div className={`${styles.radio_item} ${selectedValues.includes(option.value) ? styles.active : ''}`} key={option.value} onClick={() => handleChange(option.value)}>
            {option.label}
          </div>
        ))}
      </div>
    );
  };
  // 渲染复选
  const checkbox = () => (
    <div className={styles.checkbox}>
      <Checkbox className={styles.checkbox_item} indeterminate={indeterminate} checked={checkAll} onChange={onCheckAll} >
        全部
      </Checkbox>
      <CheckboxGroup onChange={handleCheckboxChange} value={selectedValues}>
        <Flex style={{ flex: '1' }} vertical>
            { options.map((item) => (
            <Checkbox className={styles.checkbox_item} key={item.value} value={item.value} >{item.label}</Checkbox>
            )) }
        </Flex>
      </CheckboxGroup>
    </div>
  )

  useEffect(() => {
    if (type == 'checkbox') {
      setSelectedValues(options.map(item => item.value))
    }
  }, [])
  return (
    <div>
      { type === 'radio' ? radio() : checkbox() }
    </div>
  )
}

export default Filter