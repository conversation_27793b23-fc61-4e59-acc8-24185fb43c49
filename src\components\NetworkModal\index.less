.closeIcon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 28px;
  height: 28px;
  color: #ffffff;
  font-weight: 600;
  border-radius: 50%;
  background-color: #D92E2D;
}
.checkBtn {
  background-color: #191919;
  border: 1px solid #ffffff;
  &:hover {
    background: #191919!important;
  }
}

// 弹出框 样式重新
.modalHeader {
  padding: 12px 20px!important;
  font-weight: 400!important;
  background-color: #4C4C4C!important;
}
.modalContent {
  color: #ffffff;
  padding: 0 0 80px!important;
  background-color: #333233!important;
}
.modalBody {
  padding:48px 48px 0!important;
}
.modalFooter {
  border-radius: 10px!important;
  margin: 0px 48px;
  background-color: #333233!important;
}
.title {
  margin-bottom: 16px;
  font-size: 16px;
  color: #ffffff;
  font-weight: 600;
}
.info {
  font-size: 16px;
  margin-top: 15px;
  margin-bottom: 40px;
  .name {
    margin-bottom: 40px;
  }
}

.tips {
  margin-top: 24px;
  color: #ffffff;
  font-size: 14x;
}