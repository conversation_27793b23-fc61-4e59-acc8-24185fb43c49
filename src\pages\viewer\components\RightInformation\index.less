/* 右侧整体布局调整 */
.rightPart {
  padding: 16px;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #191919;
  color: #ffffff;
  position: relative; /* 为绝对定位的底部按钮提供定位上下文 */
}

/* 患者信息区域 */
.patientInfo {
  margin-bottom: 36px;
  .patientName {
    font-size: 36px;
    color: #ffffff;
  }
  .patientId {
    margin-top: 12px;
    font-size: 12px;
  }
}


/* 标签页样式 */
.tabs {
  border-radius: 8px 8px 0 0;
  overflow: hidden;
  :global {
    .ant-tabs-nav {
      margin-bottom: 0;
    }

    .ant-tabs-tab {
      background-color: #555555 !important;
      border: none !important;
      border-radius: 0 !important;
      color: #ffffff !important;
      padding: 8px 16px !important;
      margin: 0 !important;
      text-align: center;
    }

    .ant-tabs-tab-active {
      background-color: rgb(31, 105, 180) !important;
      border-bottom: none !important;
      color: #ffffff !important;
    }

    .ant-tabs-nav-wrap {
      background-color: #000000;
    }

    .ant-tabs-content-holder {
      background-color: #000000 !important;
    }

    .ant-tabs-card > .ant-tabs-nav .ant-tabs-tab-active .ant-tabs-tab-btn {
      color: #ffffff !important;
      font-weight: normal;
    }

    .ant-tabs-nav-list {
      width: 100%;
      display: flex;
    }

    .ant-tabs-tab {
      flex: 1;
      display: flex;
      justify-content: center;
    }

    .ant-tabs-content {
      background-color: #000000 !important;
    }

    /* 更具体的选择器确保激活标签的文字是白色 */
    .ant-tabs-tab-active .ant-tabs-tab-btn {
      color: #ffffff !important;
    }

    .ant-tabs-tab-active span {
      color: #ffffff !important;
    }
  }
}

.tabContent {
  padding: 0;
}

/* 选中行样式 */
.selectedRow {
  background-color: #1f69b4 !important;
}

/* 拖拽行样式 */
.draggingRow {
  :global {
    .ant-table-tbody > tr > td {
      background-color: #52c41a !important;
      color: #ffffff !important;
      opacity: 0.7;
    }
  }
}

/* 拖拽时的表格行样式 */
.tableRow.draggingRow {
  :global {
    td {
      background-color: #52c41a !important;
      opacity: 0.7;
      transform: scale(0.98);
      transition: all 0.2s ease;
    }
  }
}

/* 截图库区域 */
.screenshotSection {
  margin-top: 36px;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0; /* 确保flex子元素能够正确收缩 */
  .sectionTitle {
    font-size: 18px;
    margin-bottom: 16px;
    color: #ffffff;
  }
  
  .screenshotGalleryWrapper {
    margin-bottom: 10px;
    border-radius: 10px;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 0; /* 关键：确保能够正确计算滚动区域 */
    background-color: #262628;
    
    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: #666666;
      border-radius: 3px;
      
      &:hover {
        background-color: #888888;
      }
    }
    
    &::-webkit-scrollbar-track {
      background-color: #333333;
    }
  }
}


/* 底部操作按钮区域 - 固定在页面最底部 */
.bottomActionButtons {
  display: flex;
  justify-content: flex-end;
}

.actionButton {
  margin-left: 8px;
  min-width: 80px;
  height: 28px;

  &:global(.ant-btn-primary) {
    background-color: #1890ff;
    border-color: #1890ff;
    color: #ffffff;

    &:hover {
      background-color: #40a9ff;
      border-color: #40a9ff;
    }

    &:active {
      background-color: #096dd9;
      border-color: #096dd9;
    }
  }
}

/* 删除按钮样式 */
:global(.ant-btn-primary.ant-btn-dangerous) {
  background-color: #ff4d4f;
  border-color: #ff4d4f;
  color: #ffffff;

  &:hover {
    background-color: #ff7875;
    border-color: #ff7875;
  }

  &:active {
    background-color: #d9363e;
    border-color: #d9363e;
  }

  &:focus {
    background-color: #ff4d4f;
    border-color: #ff7875;
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
  }
}

/* 截图预览模态框样式 */
:global(.screenshot-preview-modal) {
  .ant-modal-content {
    background-color: transparent;
    box-shadow: none;

    .ant-modal-body {
      border-radius: 4px;
      overflow: hidden;
    }
  }

  .ant-modal-wrap {
    background-color: rgba(0, 0, 0, 0.85);
  }

  /* 禁止图片被选中变蓝 */
  img {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    /* 移除pointer-events限制，允许点击 */
  }
}

/* 预览左右切换按钮悬停效果 */
.preview-nav-button {
  opacity: 0.7;
  transition: opacity 0.3s ease;

  &:hover {
    opacity: 1;
  }
}
