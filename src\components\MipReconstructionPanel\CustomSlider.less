.custom-slider {
  position: relative;
  height: 20px;
  width: 100%;
  cursor: pointer;
  outline: none;
  user-select: none;
  
  &:focus {
    outline: none;
  }
  
  &.disabled {
    cursor: not-allowed;
    opacity: 0.5;
    
    .custom-slider-handle {
      cursor: not-allowed;
    }
  }
  
  &.dragging {
    .custom-slider-handle {
      cursor: grabbing;
      
      .custom-slider-tooltip {
        opacity: 1;
        transform: translateX(-50%) translateY(-100%) scale(1);
      }
    }
  }
  
  // 滑动条轨道
  .custom-slider-rail {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    transform: translateY(-50%);
    pointer-events: none;
  }
  
  // 滑动条已填充部分
  .custom-slider-track {
    position: absolute;
    top: 50%;
    left: 0;
    height: 4px;
    background: #1890ff;
    border-radius: 2px;
    transform: translateY(-50%);
    transition: width 0.1s ease;
    pointer-events: none;
  }
  
  // 滑动条手柄
  .custom-slider-handle {
    position: absolute;
    top: 50%;
    width: 12px;
    height: 12px;
    background: #fff;
    border: 2px solid #1890ff;
    border-radius: 50%;
    cursor: grab;
    transform: translateX(-50%) translateY(-50%);
    transition: all 0.2s ease;
    z-index: 10;
    
    &:hover {
      transform: translateX(-50%) translateY(-50%) scale(1.2);
      box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.2);
      
      .custom-slider-tooltip {
        opacity: 1;
        transform: translateX(-50%) translateY(-100%) scale(1);
      }
    }
    
    &:active {
      cursor: grabbing;
      transform: translateX(-50%) translateY(-50%) scale(1.1);
    }
    
    // 工具提示
    .custom-slider-tooltip {
      position: absolute;
      bottom: 100%;
      left: 50%;
      transform: translateX(-50%) translateY(-100%) scale(0.8);
      background: rgba(0, 0, 0, 0.8);
      color: #fff;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 10px;
      white-space: nowrap;
      opacity: 0;
      transition: all 0.2s ease;
      pointer-events: none;
      z-index: 20;
      
      &::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 4px solid transparent;
        border-top-color: rgba(0, 0, 0, 0.8);
      }
    }
  }
  
  // 颜色变体
  &.axial-slider {
    .custom-slider-track {
      background: #ff4d4f;
    }
    
    .custom-slider-handle {
      border-color: #ff4d4f;
      
      &:hover {
        box-shadow: 0 0 0 4px rgba(255, 77, 79, 0.2);
      }
    }
  }
  
  &.coronal-slider {
    .custom-slider-track {
      background: #52c41a;
    }
    
    .custom-slider-handle {
      border-color: #52c41a;
      
      &:hover {
        box-shadow: 0 0 0 4px rgba(82, 196, 26, 0.2);
      }
    }
  }
  
  &.sagittal-slider {
    .custom-slider-track {
      background: #1890ff;
    }
    
    .custom-slider-handle {
      border-color: #1890ff;
      
      &:hover {
        box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.2);
      }
    }
  }
}

// 确保在所有情况下都能正常交互
.custom-slider * {
  pointer-events: none;
}

.custom-slider {
  pointer-events: auto !important;
}