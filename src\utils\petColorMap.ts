import vtkColorMaps from '@kitware/vtk.js/Rendering/Core/ColorTransferFunction/ColorMaps';
import vtkColorTransferFunction from '@kitware/vtk.js/Rendering/Core/ColorTransferFunction';
import vtkPiecewiseFunction from '@kitware/vtk.js/Common/DataModel/PiecewiseFunction';

export default function setPetColorMapTransferFunctionForVolumeActor(
  volumeInfo: any,
  ptTags?: {
    AcquisitionTime?: string;
    RadiopharmaceuticalStartTime?: string;
    RadionuclideTotalDose?: number;
    RadionuclideHalfLife?: number;
    PatientWeight?: number;
  }
) {
  if (
    !ptTags ||
    !ptTags.RadiopharmaceuticalStartTime ||
    !ptTags.AcquisitionTime ||
    !ptTags.RadionuclideTotalDose ||
    !ptTags.RadionuclideHalfLife ||
    !ptTags.PatientWeight
  ) {
    return;
  }

  // 解析时间字符串 HHMMSS -> 秒数
  const parseTimeString = (timeStr: string): number => {
    const match = timeStr.match(/(\d{2})(\d{2})(\d{2})/);
    if (!match) return 0;
    const hour = parseInt(match[1], 10);
    const minute = parseInt(match[2], 10);
    const second = parseInt(match[3], 10);
    return hour * 3600 + minute * 60 + second;
  };

  const acquisitionTimeSec = parseTimeString(ptTags.AcquisitionTime);
  const radiopharmaStartSec = parseTimeString(ptTags.RadiopharmaceuticalStartTime);

  const elapsedSec = acquisitionTimeSec - radiopharmaStartSec;

  // 半衰期修正因子
  const decayFactor = Math.pow(0.5, elapsedSec / ptTags.RadionuclideHalfLife);

  // SUV 计算公式
  const suvFactor = (ptTags.PatientWeight * 1000) / (ptTags.RadionuclideTotalDose * decayFactor);

  const { volumeActor, preset } = volumeInfo;
  const mapper = volumeActor.getMapper();
  const volumeData = mapper.getInputData(); // 获取体积数据

  const dataArray = volumeData.getPointData().getScalars(); // 获取标量数组
  const originalData = dataArray.getData(); // 获取原始数据
  const dataRange = dataArray.getRange(); // 获取原始数据范围 [min, max]

  // 🆕 创建SUV标准化后的数据数组
  const suvData = new Float32Array(originalData.length);
  let suvMin = Infinity;
  let suvMax = -Infinity;

  for (let i = 0; i < originalData.length; i++) {
    const suvValue = originalData[i] * suvFactor;
    suvData[i] = suvValue;

    if (suvValue < suvMin) suvMin = suvValue;
    if (suvValue > suvMax) suvMax = suvValue;
  }

  // 🆕 统计SUV值分布
  const suvBins = [-0.02, 0, 0.5, 1, 2, 3, 5, 10, 15, 25, 50, 100, 250];
  const suvHistogram = new Array(suvBins.length - 1).fill(0);

  for (let i = 0; i < suvData.length; i++) {
    const value = suvData[i];
    for (let j = 0; j < suvBins.length - 1; j++) {
      if (value >= suvBins[j] && value < suvBins[j + 1]) {
        suvHistogram[j]++;
        break;
      }
    }
  }

  // 🆕 更新数据数组为SUV值
  // 注意：这里需要更新VTK数据数组
  dataArray.setData(suvData);
  dataArray.setRange(suvMin, suvMax, 0);
  dataArray.modified();
  volumeData.modified();

  mapper.setSampleDistance(1.0);

  // 🆕 设置基于SUV值的颜色映射
  const cfun = vtkColorTransferFunction.newInstance();
  let presetToUse = preset ? preset : vtkColorMaps.getPresetByName('hsv');
  cfun.applyColorMap(presetToUse);

  // 🆕 调整颜色映射范围为SUV值范围（0-25是常见的PET SUV显示范围）
  cfun.setMappingRange(0, 25);

  volumeActor.getProperty().setRGBTransferFunction(0, cfun);

  // 🆕 创建基于SUV值的透明度函数
  const ofun = vtkPiecewiseFunction.newInstance();

  // 设置SUV值对应的透明度函数
  ofun.addPoint(0, 0.0);
  ofun.addPoint(0.1, 0.9);
  ofun.addPoint(5, 1.0);

  volumeActor.getProperty().setScalarOpacity(0, ofun);
}
