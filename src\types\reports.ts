export interface CheckReportParams {
  Id?: string | number;
  StudyId?: string | number;
  IsPass?: string | number;
  RejectedReason?: string;
}

export interface CheckStepItemType {
  Operator: string;
  Change_Date: string;
  Status_of_Report: number | string;
  [key: string]: any;
}

export interface ReportInfoType {
  Id?: number;		// 主键
  Name?: string; // 非必须，mock: @time
  Sex?: number; // 非必须
  Age?: number; // 非必须
  Weight?: string; // 非必须
  StudyId?: string; // 非必须
  Medicine?: string; // 非必须
  PartChecked?: number; // 非必须
  ExamDate: string; // 必须
  StatusOfReport: string; // 必须，初始状态：0  写了保存（草稿）：1  提交：2   召回：3   退回：4   完成检查：5
  RadiographicFinding?: string; // 非必须，影像学表现（展示）
  ClinicalDiagnosis?: string; // 非必须，临床诊断（展示）
}

// 召回 参数
export interface RecallReportParams {
  StudyId?: string | number; // 必须
  Id?: string | number; // 必须
}

// 导入模板 参数
export interface ImportTemplateParams {
  StudyId?: string[] | number[]; // 必须
  Id?: string[] | number[]; // 必须
  TypeId: string | number;
}