import React, { Key, useCallback, useEffect, useState } from 'react';
import { connect, type Dispatch } from 'umi';
import { PrintType } from '@/models/prints';
import { Checkbox, Tree, message } from 'antd';
import type { TreeDataNode } from 'antd';
import styles from './index.less';
import { VERSION, getCurPatientId } from '@/utils/consts';
import { Radio } from 'antd';
import storeUtil from '@/utils/store';

interface ISequencesListProp {
  dispatch: Dispatch<any>;
  studyList: any[];
  partSeries?: any[];
}

const SequencesList: React.FC<ISequencesListProp> = (props) => {
  const { dispatch, studyList, partSeries } = props;

  const [expandedKeys, setExpandedKeys] = useState<Key[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<Key[]>([]);
  const [loadedStudies, setLoadedStudies] = useState<Map<Key, { children: TreeDataNode[] }>>(new Map()); // 优化实现：展开一个Study单独请求，而不是一次性请求完毕
  const [curStudy, setCurStudy] = useState<any>(null); //用于记录当前展开的Study信息，方便其Series插入原有的树形结构
  const [value, setValue] = useState<Key[]>([]); // 维护单选值

  const updateSelectedValue = (key: Key) => {
    
    setValue([key]); // 保持数组结构，确保只有一个选中值
  };

  const onExpand = async (expandedKeys: Key[]) => {
    setExpandedKeys(expandedKeys);
    // 对于新展开的study，触发异步请求获取其子项
    const newlyExpandedStudyIds = expandedKeys.filter((key) => !loadedStudies.has(key));
    for (const key of newlyExpandedStudyIds) {
      const study = studyList.find((study) => study.Id === key);
      
      //study && (await getSeriesByStudyId(study.Id));
      if (study) {
    
        await getSeriesByStudyId(study.Id);
      }
    }
  };

  //测试是否找到series
  useEffect(() => {
    if (partSeries && partSeries.length > 0 && curStudy) {

      partSeries.forEach((series) => {

      });
    } else {

    }
  }, [partSeries, curStudy]);

  const handleSelect = async (selectedKeys: Key[], e: any) => {


    if (selectedKeys.length) {
      // setSelectedKeys(selectedKeys);
      const seriesIdParms = String(selectedKeys?.[0]).split('-').pop();
  
      // 更新当前选中的Series
      dispatch({
        type: 'prints/saveSelectSeries',
        payload: { Version: VERSION, SeriesId: seriesIdParms },
      });
    }
  };

  //生成树形结构的数据节点
  const getTreeNode = useCallback(() => {
    const treeNodes: TreeDataNode[] = [];

    //添加对 studyList 是否存在的检查
    if (!studyList || studyList.length === 0) {
  
      return treeNodes;
    }

    studyList.forEach((study) => {
      const loadedStudy = loadedStudies?.get(study.Id);
      // console.log('获取了Study：',study);
  
      const node: TreeDataNode = {
        title: study.StudyInstanceUid?.slice(0, 15),
        key: study.Id,
        isLeaf: false,
        // children: loadedStudy?.children.map((item) => ({ ...item, selectable: false, isLeaf: true })) || [],
        children: loadedStudy?.children || [],
        // selectable: false,
      };
      treeNodes.push(node);
  
    });

    return treeNodes;
  }, [studyList, loadedStudies]);

  const getSeriesByStudyId = async (studyId: string) => {

    setCurStudy(studyList.find((_) => _.Id === studyId));
    // dispatch({
    //   type: 'prints/getSeriesByStudyId',
    //   payload: { Version: VERSION, StudyId: studyId },
    // });
    dispatch({
      type: 'views/fetchSeries',
      payload: { StudyId: storeUtil.get('studyId').value },
    });
  };

  // 页面初始化时候，请求所有的Study
  useEffect(() => {
    dispatch({
      type: 'prints/getStudys',
      payload: {
        Version: VERSION,
        PatientId: getCurPatientId(),
      },
    });
  }, []);



  // 页面默认展开第一个Study下的Series
  useEffect(() => {
    //studyList.length > 0 && onExpand([studyList[0].Id]);
  }, [studyList]);

  // 用户展开特定研究节点时，根据后台返回的系列数据动态构建该研究下的子节点数据，并更新到组件的状态中
  useEffect(() => {
    if (partSeries && curStudy) {

      const childrenNodes = partSeries.map((child: any, index: number) => ({
        title: child.SeriesInstanceUid?.slice(0, 15) + '-' + child.NumOfSlices,
        key: `${curStudy.Id}-${child.Id}`,
      }));

      setLoadedStudies((prevMap) => {
        if (prevMap) {
          return new Map(prevMap).set(curStudy.Id, {
            ...curStudy,
            children: childrenNodes,
          });
        }
        return new Map([[curStudy.Id, { ...curStudy, children: childrenNodes }]]);
      });
    }
  }, [partSeries, curStudy]);

  return (
    <div className={styles.sequencesList}>
      {/* <div className={`${styles.siderItemTitle} dragHandler`}>序列列表</div> */}
      <div className={styles.treeContainer}>
        <Tree
          //showLine
          //showIcon
          //checkable
          onSelect={handleSelect}
          treeData={getTreeNode()}
          selectedKeys={selectedKeys}
          expandedKeys={[...expandedKeys]}
          onExpand={onExpand}
          titleRender={(nodeData: TreeDataNode) => {
            const isLeafNode = !nodeData.children || nodeData.children.length === 0;


            // 仅为叶子节点渲染内容
            if (isLeafNode) {

              let cnt = 0;
              partSeries?.forEach((series) => {

              });

              const titleString = String(nodeData.title);
              const title1 = titleString.includes('-') ? titleString.split('-')[0] : '';
              const title2 = titleString.includes('-') ? titleString.split('-')[1] : '';


              return (
                <div style={{ display: 'flex', alignItems: 'center', position: 'relative', width: '11vw' }}>
                  <Radio
                    value={nodeData.key} // 设置 value 为 nodeData.key
                    checked={value.includes(nodeData.key)} // 控制选中状态
                    onChange={() => updateSelectedValue(nodeData.key)} // 更新选中值
                  />
                  <div style={{ marginLeft: '1px', width: '100%' }}>{title1}</div>
                  <div style={{ marginLeft: 'auto', textAlign: 'right', width: '100%' }}>{title2}</div>
                  {/* <div style={{  marginLeft: '10px'}}>{keySuffix}</div>  */}
                </div>
              );
            }

            // 父节点渲染
            return (
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <span style={{ marginLeft: '10px' }}>{nodeData.title as string}</span>
              </div>
            );
          }}

          // titleRender={(nodeData) => {
          //   return nodeData.isLeaf ? (
          //     <>
          //       <Checkbox
          //         onChange={(e) => {
          //           //TODO

          //         }}
          //       />
          //       <span style={{ marginLeft: '10px' }}>{nodeData.title}</span>
          //     </>
          //   ) : (
          //     <div>{nodeData.title}</div>
          //   );
          // }}
        />
      </div>
    </div>
  );
};

const mapStateToProps = ({ prints }: { prints: PrintType }) => {
  const { studyList, partSeries } = prints;
  return {
    studyList,
    partSeries,
  };
};

export default connect(mapStateToProps)(SequencesList);
