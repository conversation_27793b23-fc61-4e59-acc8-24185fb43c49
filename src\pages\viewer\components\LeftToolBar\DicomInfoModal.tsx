import React, { useState, useEffect } from 'react';
import { Modal, Table, List, Spin, message, Flex, Input, } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { CloseOutlined } from '@ant-design/icons';
import { connect, Dispatch } from 'umi';
import { ViewType } from '@/models/views';
import styles from './DicomInfoModal.less';

interface DicomInfoModalProps {
  visible: boolean;
  onClose: () => void;
  dispatch: Dispatch;
  views: ViewType;
}

// 序列数据类型
interface SeriesData {
  key: string;
  seriesNumber: string;
  seriesDescription: string;
  imageCount: number;
  seriesId: string;
  modality: string;
}

// DICOM标签数据类型
interface DicomTag {
  tag: string;
  description: string;
  value: string;
}

const { Search } = Input;

const DicomInfoModal: React.FC<DicomInfoModalProps> = ({ visible, onClose, dispatch, views }) => {
  const [selectedSeriesId, setSelectedSeriesId] = useState<string | null>(null);
  const [dicomTags, setDicomTags] = useState<DicomTag[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  // 获取序列的DICOM标签描述
  const getSeriesDescription = async (seriesId: string): Promise<string> => {
    try {
      const instances = views.curInstances?.get(seriesId);
      if (!instances || instances.length === 0) {
        return '未知序列';
      }

      const firstInstance = instances[0];
      const instanceId = firstInstance.Id;

      // 调用获取DICOM标签的接口
      await dispatch({
        type: 'views/FetchInstanceAllTag',
        payload: instanceId,
      });

      // 等待状态更新
      await new Promise(resolve => setTimeout(resolve, 100));

      // 从InstanceTag中获取序列描述
      if (views.InstanceTag && typeof views.InstanceTag === 'object') {
        const seriesDescTag = views.InstanceTag['(0008,103e)'];
        if (seriesDescTag && seriesDescTag.Value) {
          return String(seriesDescTag.Value);
        }
      }

      return '未知序列';
    } catch (error) {
      console.error('获取序列描述失败:', error);
      return '未知序列';
    }
  };

  // 将序列数据转换为表格格式
  const seriesData: SeriesData[] = React.useMemo(() => {
    if (!views.seriesLists || views.seriesLists.length === 0) {
      return [];
    }

    return views.seriesLists
      .map((series: any) => {
        // 获取对应的instances数量
        let instanceCount = 0;
        if (views.curInstances && typeof views.curInstances.get === 'function') {
          const instances = views.curInstances.get(series.SeriesId);
          instanceCount = instances?.length || 0;
        }

        // 优先使用series.SeriesDescription，如果为空则使用ProtocolName，最后才是默认值
        let seriesDescription = '未知序列';
        if (series.SeriesDescription && series.SeriesDescription.trim()) {
          seriesDescription = series.SeriesDescription;
        } else if (series.ProtocolName && series.ProtocolName.trim()) {
          seriesDescription = series.ProtocolName;
        }

        return {
          key: series.SeriesId,
          seriesNumber: series.SeriesNumber || '未知',
          seriesDescription,
          imageCount: instanceCount,
          seriesId: series.SeriesId,
          modality: series.Modality || '未知',
        };
      })
      .sort((a, b) => {
        // 当前选中的序列优先显示
        if (a.seriesId === views.selectedSeriesId) return -1;
        if (b.seriesId === views.selectedSeriesId) return 1;
        return 0;
      });
  }, [views.seriesLists, views.curInstances, views.selectedSeriesId]);

  // 序列表格列定义
  const seriesColumns: ColumnsType<SeriesData> = [
    {
      title: '序列',
      dataIndex: 'seriesDescription',
      key: 'seriesDescription',
      ellipsis: true,
    },
  ];

  // DICOM标签表格列定义
  const tagColumns: ColumnsType<DicomTag> = [
    {
      title: 'Tag ID',
      dataIndex: 'tag',
      key: 'tag',
      width: 120,
      align: 'center',
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      width: 200,
    },
    {
      title: 'Value',
      dataIndex: 'value',
      key: 'value',
      ellipsis: true,
    },
  ];

  // 定义常见DICOM标签的中文描述映射
  const tagDescriptions: { [key: string]: string } = {
    // 患者信息
    '(0010,0010)': '患者姓名',
    '(0010,0020)': '患者ID',
    '(0010,0030)': '出生日期',
    '(0010,0040)': '性别',
    '(0010,1010)': '患者年龄',
    '(0010,1020)': '患者身高',
    '(0010,1030)': '患者体重',
    '(0010,4000)': '患者备注',
    
    // 检查信息
    '(0008,0020)': '检查日期',
    '(0008,0030)': '检查时间',
    '(0008,0050)': '检查号',
    '(0008,0060)': '模态',
    '(0008,0070)': '制造商',
    '(0008,0080)': '机构名称',
    '(0008,0090)': '医师姓名',
    '(0008,1010)': '工作站名称',
    '(0008,1030)': '检查描述',
    '(0008,103E)': '序列描述',
    '(0008,1040)': '科室名称',
    '(0008,1050)': '执行医师姓名',
    '(0008,1060)': '阅片医师姓名',
    '(0008,1070)': '操作员姓名',
    '(0008,1080)': '医师地址',
    '(0008,1090)': '制造商型号',
    
    // 设备信息
    '(0018,0015)': '身体部位',
    '(0018,0020)': '扫描序列',
    '(0018,0021)': '序列变量',
    '(0018,0022)': '扫描选项',
    '(0018,0023)': 'MR采集类型',
    '(0018,0050)': '层厚',
    '(0018,0060)': 'kVp',
    '(0018,0080)': '重复时间',
    '(0018,0081)': '回波时间',
    '(0018,0082)': '反转时间',
    '(0018,0083)': '平均次数',
    '(0018,0084)': '成像频率',
    '(0018,0085)': '成像核素',
    '(0018,0086)': '回波数',
    '(0018,0087)': '磁场强度',
    '(0018,0088)': '层间距',
    '(0018,0089)': '层数',
    '(0018,0090)': '数据采集直径',
    '(0018,0091)': '回波训练长度',
    '(0018,0093)': '占空比',
    '(0018,0094)': '梯度输出',
    '(0018,0095)': '像素带宽',
    '(0018,1000)': '设备序列号',
    '(0018,1020)': '软件版本',
    '(0018,1030)': '协议名称',
    '(0018,1100)': '重建直径',
    '(0018,1110)': '距离源到探测器',
    '(0018,1111)': '距离源到患者',
    '(0018,1120)': '准直器/网格名称',
    '(0018,1130)': '床高度',
    '(0018,1131)': '床角度',
    '(0018,1140)': '旋转方向',
    '(0018,1150)': '曝光时间',
    '(0018,1151)': 'X射线管电流',
    '(0018,1152)': '曝光量',
    '(0018,1160)': '滤波器类型',
    '(0018,1170)': '发生器功率',
    '(0018,1190)': '焦点大小',
    '(0018,1200)': '日期最后校准',
    '(0018,1201)': '时间最后校准',
    '(0018,1210)': '卷积核',
    '(0018,5100)': '患者位置',
    
    // 图像信息
    '(0020,000D)': '检查实例UID',
    '(0020,000E)': '序列实例UID',
    '(0020,0010)': '检查ID',
    '(0020,0011)': '序列号',
    '(0020,0012)': '采集号',
    '(0020,0013)': '实例号',
    '(0020,0020)': '患者方向',
    '(0020,0032)': '图像位置',
    '(0020,0037)': '图像方向',
    '(0020,0052)': '帧参考UID',
    '(0020,1040)': '位置参考指示器',
    '(0020,1041)': '层位置',
    
    // 像素数据
    '(0028,0002)': '样本/像素',
    '(0028,0004)': '光度解释',
    '(0028,0008)': '帧数',
    '(0028,0010)': '行数',
    '(0028,0011)': '列数',
    '(0028,0030)': '像素间距',
    '(0028,0100)': '分配位数',
    '(0028,0101)': '存储位数',
    '(0028,0102)': '高位',
    '(0028,0103)': '像素表示',
    '(0028,0106)': '最小像素值',
    '(0028,0107)': '最大像素值',
    '(0028,1050)': '窗位',
    '(0028,1051)': '窗宽',
    '(0028,1052)': '重标定截距',
    '(0028,1053)': '重标定斜率',
    '(0028,1054)': '重标定类型',
    '(0028,1055)': '窗位窗宽说明',
    
    // CT特定
    '(0018,0060)': 'kVp',
    '(0018,1100)': '重建直径',
    '(0018,1120)': '准直器类型',
    '(0018,1130)': '床高度',
    '(0018,1140)': '旋转方向',
    '(0018,1150)': '曝光时间',
    '(0018,1151)': 'X射线管电流',
    '(0018,1152)': '曝光量',
    '(0018,1160)': '滤波器类型',
    '(0018,1170)': '发生器功率',
    '(0018,1210)': '卷积核',
    
    // MR特定
    '(0018,0080)': '重复时间(TR)',
    '(0018,0081)': '回波时间(TE)',
    '(0018,0082)': '反转时间(TI)',
    '(0018,0083)': '平均次数',
    '(0018,0084)': '成像频率',
    '(0018,0087)': '磁场强度',
    '(0018,0091)': '回波训练长度',
    '(0018,0095)': '像素带宽',
    
    // 其他常用标签
     '(0002,0000)': '文件元信息组长度',
     '(0002,0001)': '文件元信息版本',
     '(0002,0002)': '媒体存储SOP类UID',
     '(0002,0003)': '媒体存储SOP实例UID',
     '(0002,0010)': '传输语法UID',
     '(0002,0012)': '实现类UID',
     '(0002,0013)': '实现版本名称',
     '(0008,0005)': '特定字符集',
     '(0008,0008)': '图像类型',
     '(0008,0012)': '实例创建日期',
     '(0008,0013)': '实例创建时间',
     '(0008,0014)': '实例创建者UID',
     '(0008,0016)': 'SOP类UID',
     '(0008,0018)': 'SOP实例UID',
     '(0008,0021)': '序列日期',
     '(0008,0022)': '采集日期',
     '(0008,0023)': '内容日期',
     '(0008,0031)': '序列时间',
     '(0008,0032)': '采集时间',
     '(0008,0033)': '内容时间',
     '(0008,103e)': '序列描述',
     '(0008,1140)': '参考图像序列',
     
     // 设备和采集参数
     '(0018,0031)': '放射性药物',
     '(0018,0071)': '采集终止条件',
     '(0018,0073)': '采集开始条件',
     '(0018,0074)': '采集开始条件数据',
     '(0018,0075)': '采集终止条件数据',
     '(0018,1060)': '触发时间',
     '(0018,1063)': '帧时间',
     '(0018,1072)': '放射性药物开始时间',
     '(0018,1074)': '放射性药物总剂量',
     '(0018,1075)': '放射性药物半衰期',
     '(0018,1076)': '放射性药物比活度',
     '(0018,1083)': '间隔时间',
     '(0018,1084)': '间隔数',
     '(0018,1147)': '探测器几何形状',
     '(0018,1149)': '视野形状',
     '(0018,1181)': '准直器类型',
     '(0018,1242)': '实际帧持续时间',
     
     // 图像位置和方向
     '(0020,000d)': '检查实例UID',
     '(0020,000e)': '序列实例UID',
     
     // 像素数据相关
     '(0028,0006)': '平面配置',
     '(0028,0051)': '校正图像',
     '(0028,2110)': '有损图像压缩',
     
     // 核医学特定标签
     '(0054,0014)': '能量窗口数',
     '(0054,0015)': '能量窗口总数',
     '(0054,0081)': '探测器数',
     '(0054,0202)': '类型数据校正',
     '(0054,1000)': '序列类型',
     '(0054,1001)': '单位',
     '(0054,1002)': '计数源',
     '(0054,1100)': '随机校正方法',
     '(0054,1101)': '衰减校正方法',
     '(0054,1102)': '衰减校正',
     '(0054,1103)': '散射校正方法',
     '(0054,1104)': '死时间校正',
     '(0054,1105)': '门控信息序列',
     '(0054,1201)': '轴向接受',
     '(0054,1202)': '轴向采样',
     '(0054,1210)': '旋转角度',
     '(0054,1300)': '扫描弧',
     '(0054,1310)': '角度步长',
     '(0054,1311)': '旋转方向',
     '(0054,1320)': '间隔时间',
     '(0054,1321)': '间隔时间分辨率',
     '(0054,1322)': '提示计数率',
     '(0054,1323)': '单计数率',
     '(0054,1324)': '随机计数率',
     '(0054,1330)': '门控周期',
     
     '(0032,1030)': '检查原因',
     '(0032,1060)': '请求过程描述',
     
     // 增强扫描和重建参数
     '(0018,9309)': '采集协议元素',
     '(0018,9311)': '采集协议描述',
     '(0018,9317)': '采集协议名称',
     '(0018,9323)': '曝光调制类型',
     '(0018,9327)': '曝光时间',
     '(0018,9345)': '管电流调制类型',
     '(0018,9943)': '水等效直径',
     
     // 其他标签
     '(0020,4000)': '图像注释',
     '(0008,0081)': '地址',
     '(0018,0010)': '对比剂/造影剂'
  };

  // 获取DICOM标签信息
  const fetchDicomTags = async (seriesId: string) => {
    setLoading(true);
    try {
      // 获取该序列的第一个实例
      const instances = views.curInstances?.get(seriesId);
      if (!instances || instances.length === 0) {
        message.warning('该序列没有可用的图像实例');
        setDicomTags([]);
        return;
      }

      const firstInstance = instances[0];
      const instanceId = firstInstance.Id;

      // 调用获取DICOM标签的接口
      const result = await dispatch({
        type: 'views/FetchInstanceAllTag',
        payload: instanceId,
      });

      // 从views状态中获取更新后的InstanceTag
      const updatedViews = await new Promise((resolve) => {
        setTimeout(() => {
          resolve(views);
        }, 100);
      });

      if (views.InstanceTag && typeof views.InstanceTag === 'object') {
        // 显示所有DICOM标签
        const tags: DicomTag[] = Object.keys(views.InstanceTag)
          .map(tagId => {
            const tagData = views.InstanceTag[tagId];
            if (tagData) {
              return {
                tag: tagId,
                description: tagDescriptions[tagId] || tagId, // 使用硬编码的中文描述，如果没有则显示标签ID
                value: tagData.Value || tagData.value || String(tagData) || '未知',
              };
            }
            return null;
          })
          .filter(tag => tag !== null) as DicomTag[];
        
        setDicomTags(tags);
      } else {
        setDicomTags([]);
        message.warning('未能获取到DICOM标签信息');
      }
    } catch (error) {
      console.error('获取DICOM标签失败:', error);
      message.error('获取DICOM标签信息失败');
      setDicomTags([]);
    } finally {
      setLoading(false);
    }
  };

  // 处理序列选择
  const handleSeriesSelect = (record: SeriesData) => {
    setSelectedSeriesId(record.seriesId);
    fetchDicomTags(record.seriesId);
  };

  // 初始化时选择当前序列或第一个序列
  useEffect(() => {
    if (visible && seriesData.length > 0) {
      const initialSeriesId = views.selectedSeriesId || seriesData[0].seriesId;
      setSelectedSeriesId(initialSeriesId);
      fetchDicomTags(initialSeriesId);
    }
  }, [visible, seriesData, views.selectedSeriesId]);

  // 模态框关闭时重置状态
  const handleClose = () => {
    setSelectedSeriesId(null);
    setDicomTags([]);
    setLoading(false);
    onClose();
  };


  // 过滤 DICOM 标签数据
  const filteredDicomTags = React.useMemo(() => {
    if (!searchText) return dicomTags;
    let lowerSearchText = searchText.toLowerCase();
    console.log('filteredDicomTags', dicomTags);
    return dicomTags.filter(item => 
      (item?.tag ? item.tag.toLowerCase().includes(lowerSearchText) : false) ||
      (item?.description ? item.description.toLowerCase().includes(lowerSearchText) : false) ||
      // 处理 value 为数组的情况
      (Array.isArray(item?.value) 
        ? item.value.some(v => String(v).toLowerCase().includes(lowerSearchText))
        : String(item?.value).toLowerCase().includes(lowerSearchText))
    );
  }, [dicomTags, searchText]);

  return (
    <Modal
      title="DICOM信息"
      open={visible}
      onCancel={handleClose}
      footer={null}
      width={1000}
      classNames={{
        header: styles.modalHeader,
        content: styles.modalContent,
        body: styles.modalBody,
        footer: styles.modalFooter,
      }}
      closeIcon={<div className={styles.closeIcon}><CloseOutlined /></div>}
    >
      <Flex gap={16} >
        {/* 左侧序列列表 */}
        <div className={styles.seriesSection}>
          <Table
            columns={seriesColumns}
            dataSource={seriesData}
            pagination={false}
            size="small"
            scroll={{ y: 400 }}
            rowClassName={(record) => {
              return record.seriesId === selectedSeriesId ? styles.selectedRow : styles.tableRow;
            }}
            onRow={(record) => ({
              onClick: () => handleSeriesSelect(record),
              style: { cursor: 'pointer' },
            })}
            bordered
          />
        </div>

        {/* 右侧DICOM标签列表 */}
        <div className={styles.tagsSection}>
          <div className={styles.searchContainer}>
            <Search
              placeholder="搜索DICOM标签"
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
            />
          </div>
          {loading ? (
            <div className={styles.loadingContainer}>
              <Spin size="large" />
              <p>正在加载DICOM标签信息...</p>
            </div>
          ) : (
            <Table
              columns={tagColumns}
              dataSource={filteredDicomTags}
              pagination={false}
              size="small"
              scroll={{ y: 400 }}
              bordered
            />
          )}
        </div>
      </Flex>
    </Modal>
  );
};

const mapStateToProps = ({ views }: { views: ViewType }) => {
  return {
    views,
  };
};

export default connect(mapStateToProps)(DicomInfoModal);