import { Confi<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Di<PERSON>r, Radio, Modal, Select, message, Tabs, InputNumber } from 'antd';
import { PlusOutlined, ZoomInOutlined, RedoOutlined } from '@ant-design/icons';
import styles from './index.less';
import React, { Dispatch, useMemo, useState } from 'react';
import GridSelect from './GridSelect';
import { connect } from 'umi';
import { PrintType } from '@/models/prints';
import CustomizeRadio from './CustomizeRadio';
import { getCurSeriesIndex } from '@/utils/utils';
import CustomImageButton from '@/components/prints/CustomImageButton';
import { IMouseLeftToolType } from '@/utils/enums';

const layoutRadiooptions = [
  { label: '1×1', value: 11 },
  { label: '1×2', value: 12 },
  { label: '2×1', value: 21 },
  { label: '2×2', value: 22 },
  { label: '1×3', value: 13 },
  { label: '3×1', value: 31 },
];

const numOfFilmSelectOption = [
  { value: 1, label: '1' },
  { value: 2, label: '2' },
  { value: 3, label: '3' },
  { value: 4, label: '4' },
];

enum ModalTypeName {
  layoutSelect = 'layoutSelect',
  grid = 'grid',
  sequences = 'sequences',
  intervalPrinting = 'intervalPrinting',
  mergedLayerThickness = 'mergedLayerThickness',
}

interface IPrintSettingsProps {
  dispatch: Dispatch<any>;
  filmNum: PrintType['filmNum'];
  selectedFilmId: PrintType['selectedFilmId'];
  filmsConfig: PrintType['filmsConfig'];
  selectedDicomId: PrintType['selectedDicomId'];
}

const PrintSettings: React.FC<IPrintSettingsProps> = (props) => {
  const { dispatch, filmNum, selectedFilmId, filmsConfig, selectedDicomId, mouseLeftTool } = props;
  const curFilm = useMemo(() => filmsConfig?.[(selectedFilmId as number) - 1], [filmsConfig, selectedFilmId]);
  const [layoutModal, setLayoutModal] = useState<boolean>(false);
  const [layoutModalType, setLayoutModalType] = useState<ModalTypeName>(ModalTypeName.grid);
  const [curSeriesLayout, setCurSeriesLayout] = useState<{
    row: number;
    col: number;
  } | null>(null);
  const [curDicomsLayout, setCurDicomsLayout] = useState<{
    row: number;
    col: number;
  } | null>(null);
  const [curPrintSpace, setCurPrintSpace] = useState<number>(1);

  const layoutModalConfigure = useMemo(() => {
    return {
      layoutSelect: {
        title: '胶片布局模板选择',
        content: (
          <Tabs
            centered
            defaultActiveKey='system'
            items={[
              {
                key: 'system',
                label: '系统',
                children: 'Content of Tab Pane 1',
              },
              {
                key: 'custom',
                label: '自定义',
                children: 'Content of Tab Pane 2',
              },
            ]}
          />
        ),
      },
      sequences: {
        title: '序列布局自定义',
        onOk: () => {

          if (curSeriesLayout) {
            dispatch({
              type: 'prints/selectedFilmStateHandler',
              payload: {
                seriesLayout: (curSeriesLayout?.row + 1) * 10 + curSeriesLayout?.col + 1,
              },
            });
          }
          setLayoutModal(false);
        },
        onCancel: () => {
          setLayoutModal(false);
          setCurSeriesLayout(null);
        },
        content: (
          <GridSelect
            value={curSeriesLayout}
            onChange={(row, col) => {
              setCurSeriesLayout({ row, col });
            }}
          />
        ),
      },
      grid: {
        title: '像格布局自定义',
        onOk: () => {
          if (curDicomsLayout) {
            gridLayoutChangeHandler((curDicomsLayout?.row + 1) * 10 + curDicomsLayout?.col + 1);
          }
          setLayoutModal(false);
        },
        onCancel: () => {
          setLayoutModal(false);
          setCurDicomsLayout(null);
        },
        content: (
          <GridSelect
            value={curDicomsLayout}
            onChange={(row, col) => {
              setCurDicomsLayout({ row, col });
            }}
          />
        ),
      },
      intervalPrinting: {
        title: '间隔打印设置',
        content: (
          <div>
            间隔
            <InputNumber value={curPrintSpace} min={1} onChange={(value) => setCurPrintSpace(Number(value || 1))} />张
          </div>
        ),
        onOk: () => {
          printSpaceChangeHandler(curPrintSpace);
          setLayoutModal(false);
        },
        onCancel: () => {
          setLayoutModal(false);
          setCurDicomsLayout(null);
        },
      },
      mergedLayerThickness: {
        title: '合并层厚打印设置',
        content: (
          <div>
            <div>
              起始位置：
              <InputNumber />
            </div>
            <div>
              结束位置：
              <InputNumber />
            </div>
            <div>
              每
              <InputNumber />
              层合并
            </div>
          </div>
        ),
      },
    };
  }, [curSeriesLayout, curDicomsLayout, curPrintSpace]);

  const gridLayoutValue = useMemo(() => {
    if (!selectedDicomId) return 11;
    const curSeriesIndex = getCurSeriesIndex(selectedDicomId);
    return curFilm?.dicomsLayoutList?.[curSeriesIndex as number] || 11;
  }, [selectedDicomId, curFilm, curFilm?.dicomsLayoutList]);

  const openModal = (type: ModalTypeName) => {
    setLayoutModalType(type);
    setLayoutModal(true);
  };

  //处理胶片数量变化。
  const filmNumChangeHandler = (value: number) => {
    dispatch({
      type: 'prints/stateChangeHandler',
      payload: { filmNum: value },
    });
  };

  //处理像格布局变化。
  const gridLayoutChangeHandler = (value: number) => {
    if (selectedDicomId) {
      const curSeriesIndex = getCurSeriesIndex(selectedDicomId);
      const tempLayoutList = [...(curFilm?.dicomsLayoutList || [])];
      tempLayoutList.splice(Number(curSeriesIndex), 1, value);
      dispatch({
        type: 'prints/selectedFilmStateHandler',
        payload: {
          dicomsLayoutList: tempLayoutList,
        },
      });
    }
  };

  //处理打印间隔变化。
  const printSpaceChangeHandler = (value: number) => {
    if (selectedDicomId) {
      const curSeriesIndex = getCurSeriesIndex(selectedDicomId);
      const tempSpaceList = [...(curFilm?.printSpaceList || [])];
      tempSpaceList.splice(Number(curSeriesIndex), 1, value);

      dispatch({
        type: 'prints/selectedFilmStateHandler',
        payload: {
          printSpaceList: tempSpaceList,
        },
      });
    }
  };

  //插入新胶片。
  const insertFilm = () => {
    if (filmNum < 4) {
      if (!selectedFilmId) {
        message.info('插入胶片至少需要选中胶片');
        return;
      }
      const filmConfig = filmsConfig?.[(selectedFilmId as number) - 1];
      const tempConfigs = [...(filmsConfig || [{}, {}, {}, {}])];
      tempConfigs.splice(selectedFilmId as number, 1, filmConfig || {});
      dispatch({
        type: 'prints/stateChangeHandler',
        payload: {
          filmNum: filmNum + 1,
          filmsConfig: tempConfigs,
        },
      });
    } else {
      message.info('胶片最多展示4张');
    }
  };

  //添加新胶片。
  const addNewFilm = () => {
    if (filmNum < 4) {
      filmNumChangeHandler(filmNum + 1);
      if (selectedFilmId) {
        message.info('新建空白胶片');
      }
    } else {
      message.info('胶片最多展示四张');
    }
  };

  //反序当前序列
  const inverseSeries = () => {
    if (!selectedDicomId) {
      message.info('请先确认反序序列');
    } else {
      const curSeriesIndex = getCurSeriesIndex(selectedDicomId);
      const targetIsInverse = [...(filmsConfig?.[Number(selectedFilmId) - 1]?.isInverseList || [false])];
      dispatch({
        type: 'prints/selectedFilmStateHandler',
        payload: {
          isInverseList: targetIsInverse.map((_, index) => (index === Number(curSeriesIndex) ? !_ : _)),
        },
      });
    }
  };

  //切换鼠标左键工具。
  const activeToolChange = () => {
    const currentLeftTool = mouseLeftTool;
    dispatch({
      type: 'prints/stateChangeHandler',
      payload: {
        mouseLeftTool: currentLeftTool === IMouseLeftToolType.PanTool ? IMouseLeftToolType.ZoomTool : IMouseLeftToolType.PanTool,
      },
    });
  };

  // 全选胶片
  const SelectAllFilms = () => {
    dispatch({
      type: 'prints/stateChangeHandler',
      payload: { selectedFilmId: [1, 2, 3, 4] },
    });
  };

  // 删除所有胶片
  const DeleteAllFilms = () => {
    dispatch({
      type: 'prints/stateChangeHandler',
      payload: {
        filmNum: 1,
        selectedFilmId: 1,
        selectedDicomId: null,
        filmsConfig: [{}, {}, {}, {}],
      },
    });
  };

  // 删除当前选中序列
  const deleteCurSeries = () => {
    const curSeriesIndex = getCurSeriesIndex(selectedDicomId);
    if (curSeriesIndex !== undefined) {
      const deleteTarget = [...(filmsConfig?.[Number(selectedFilmId) - 1]?.seriesList || [])];
      deleteTarget.splice(curSeriesIndex, 1, undefined);
      dispatch({
        type: 'prints/selectedFilmStateHandler',
        payload: { seriesList: deleteTarget, dicomsLayoutList: [] },
      });
    } else {
      message.info('当前无选中序列');
    }
  };

  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#4169E1',
          colorBgBase: '#2d2d2d',
          colorText: '#ffffff',
        },
        components: {
          Select: {
            colorBgContainer: '#2d2d2d',
            colorBorder: 'black',
            optionActiveBg: '#3a3a3a',
            optionSelectedBg: '#3a3a3a',
            controlOutline: 'none',
          },
        },
      }}
    >
      <div className={styles.printSetting}>
        <div className={`${styles.siderItemTitle} dragHandler`}>打印设置</div>
        <div className={styles.contentContainer}>
          <Row style={{ width: '100%' }}>
            <Col span={7} offset={2}>
              <Button icon={<PlusOutlined />} onClick={addNewFilm} style={{ width: '100%' }}>
                新建胶片
              </Button>
            </Col>
            <Col span={10} offset={3}>
              <span style={{ marginLeft: '10px', color: 'white', fontWeight: 'bold' }}>胶片数量:</span>
              <Select
                style={{ width: '60px', marginLeft: '10px' }}
                options={numOfFilmSelectOption}
                value={filmNum}
                onChange={filmNumChangeHandler}
              />
            </Col>
            {/* <Col span={7}>
              <Button
                style={{ width: '100%' }}
                onClick={() => openModal(ModalTypeName.layoutSelect)}
                disabled
              >
                模板布局
              </Button>
            </Col> */}
          </Row>
          <div className={styles.layoutTitle}>序列布局:</div>
          <CustomizeRadio
            value={curFilm?.seriesLayout || 11}
            onChange={(e) => {
              dispatch({
                type: 'prints/selectedFilmStateHandler',
                payload: { seriesLayout: e.target.value },
              });
            }}
            onCustomize={() => openModal(ModalTypeName.sequences)}
          />
          <div className={styles.layoutTitle}>像格布局:</div>
          <CustomizeRadio
            value={gridLayoutValue}
            onChange={(e) => gridLayoutChangeHandler(e.target.value)}
            onCustomize={() => openModal(ModalTypeName.grid)}
          />
          <Divider orientation='center' className={styles.divider}>
            基础操作
          </Divider>
          {/* <Row className={styles.settingRow}>
            <Col className={styles.quickerDesc} span={8}>
              移动:鼠标左键
            </Col>
            <Col className={styles.quickerDesc} span={8}>
              缩放:鼠标右键
            </Col>
          </Row> */}
          {/* <Row className={styles.settingRow}>
            <Col span={6}>
              <Button>缩放</Button>
            </Col>
            <Col span={6}>
              <Button disabled>剪切</Button>
            </Col>
            <Col span={6}>
              <Button disabled>复制</Button>
            </Col>
            <Col span={6}>
              <Button disabled>粘贴</Button>
            </Col>
          </Row> */}
          {/* <Divider orientation='left' className={styles.divider}>
            高级打印设置
          </Divider> */}
          <div className={styles.settingRow}>
            <CustomImageButton text={'缩放'} icon={<ZoomInOutlined className={styles.buttonIcon} />} onClick={activeToolChange} />

            <CustomImageButton text={'反序'} imageUrl={'/icon/2.png'} onClick={inverseSeries} />

            <CustomImageButton
              text={'间隔打印'}
              imageUrl={'/icon/3.png'}
              onClick={() => openModal(ModalTypeName.intervalPrinting)}
            />

            <CustomImageButton text={'插入胶片'} imageUrl={'/icon/4.png'} onClick={insertFilm} />

            {/* <CustomImageButton
              disabled
              text={'合并层厚'}
              imageUrl={'/icon/5.png'}
              onClick={() => openModal(ModalTypeName.mergedLayerThickness)}
            /> */}
          </div>
          <Divider orientation='center' className={styles.divider}>
            批量操作
          </Divider>
          <div className={styles.settingRow}>
            <CustomImageButton text={'删除选中'} imageUrl={'/icon/6.png'} onClick={deleteCurSeries} />

            <CustomImageButton text={'全选胶片'} imageUrl={'/icon/7.png'} onClick={SelectAllFilms} />

            <CustomImageButton text={'删除全部'} imageUrl={'/icon/8.png'} onClick={DeleteAllFilms} />
          </div>
        </div>
      </div>
      <Modal open={layoutModal} {...layoutModalConfigure[layoutModalType]}>
        {layoutModalConfigure[layoutModalType]?.content}
      </Modal>
    </ConfigProvider>
  );
};

const mapStateToProps = ({ prints }: { prints: PrintType }) => {
  const { filmNum, selectedFilmId, filmsConfig, selectedDicomId, mouseLeftTool } = prints;
  return {
    filmNum,
    selectedFilmId,
    filmsConfig,
    selectedDicomId,
    mouseLeftTool,
  };
};

export default connect(mapStateToProps)(PrintSettings);
