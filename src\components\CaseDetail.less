.table {
  //样式穿透
  :global {
    // table scrollbar
    .ant-table-body {
      overflow-y: scroll !important;
      &::-webkit-scrollbar {
        height: 0px;
        width: 10px;
        overflow-y: auto;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 5px;
        background: #595959;
      }
      &::-webkit-scrollbar-track {
        -webkit-box-shadow: 0;
        border-radius: 5px;
        background: #7f7f7f;
      }
    }
  }
}

// 自定义图片遮罩
.mask {
  background: none !important;
  opacity: 1 !important;
  color: black !important;
  align-items: flex-start !important;
  justify-content: space-between !important;
  cursor: default !important;
}
