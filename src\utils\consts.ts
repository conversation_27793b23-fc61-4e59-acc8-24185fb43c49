export const VERSION = 20230101;

export const LAYOUT_BREAK_POINTS = {
  xxl: 3500,
  xl: 2000,
  lg: 1200,
  md: 996,
  sm: 768,
  xs: 480,
  xxs: 0,
};

export const LAYOUT_BREAK_COLS = {
  xxl: 12,
  xl: 12,
  lg: 12,
  md: 11.5,
  sm: 6,
  xs: 4,
  xxs: 2,
};

export const LAYOUT_ROW_HEIGHT = {
  xl: 50,
  lg: 30,
};

export const SETTING_MODAL_TYPE = {};

export const DefaultWLWW = {
  default: {
    label: 'default',
    WL: '窗位',
    WW: '窗宽',
  },
  chest: {
    label: 'chest',
    WL: 40,
    WW: 400,
  },
  abdomen: {
    label: 'abdomen',
    WL: 60,
    WW: 400,
  },
  lung: {
    label: 'lung',
    WL: -400,
    WW: 1500,
  },
  bone: {
    label: 'bone',
    WL: 300,
    WW: 1500,
  },
  head: {
    label: 'head',
    WL: 90,
    WW: 350,
  },
  brain: {
    label: 'brain',
    WL: 40,
    WW: 80,
  },
  heart: {
    label: 'heart',
    WL: 300,
    WW: 800,
  },
  // new
  soft_Tissue: {
    label: 'soft_Tissue',
    WL: 400,
    WW: 40,
  },
  liver: {
    label: 'liver',
    WL: 190,
    WW: 90,
  },
  angio: {
    label: 'angio',
    WL: 700,
    WW: 80,
  },
  colon: {
    label: 'colon',
    WL: 1000,
    WW: 150,
  },
};

export const DefaultWLWWSelect = Object.keys(DefaultWLWW).map((key) => ({
  value: key,
  label: DefaultWLWW[key].label + ` ${DefaultWLWW[key].WL}/${DefaultWLWW[key].WW}`,
}));

export const ColorTable = {
  normal: {
    label: 'normal',
    path: 'textures/normal.png',
  },
  hot_iron: {
    label: 'hot_iron',
    path: 'textures/hot_iron.png',
  },
  hot_metal_blue: {
    label: 'hot_metal_blue',
    path: 'textures/hot_metal_blue.png',
  },
  hsv: {
    label: 'hsv',
    path: 'textures/hsv.png',
  },
  jet: {
    label: 'jet',
    path: 'textures/jet.png',
  },
  pet: {
    label: 'pet',
    path: 'textures/pet.png',
  },
  pet_NIH: {
    label: 'pet_NIH',
    path: 'textures/NIH.png',
  },
  spectral: {
    label: 'spectral',
    path: 'textures/spectral.png',
  },
  black_white_black: {
    label: 'black_white_black',
    path: 'textures/black_white_black.png',
  },
};
export const ColorTableSelect = Object.keys(ColorTable).map((key) => ({
  value: key,
  label: ColorTable[key].label,
  path: ColorTable[key].path,
}));

export const getCurPatientId = () => {
  return localStorage.getItem('PatientId');
};
