import React, { Dispatch, useCallback, useEffect, useMemo, useState } from 'react';
import { Row, Col } from 'antd';
import { connect } from 'umi';
import { PrintType } from '@/models/prints';
import FilmContainer from './FilmContainer';
import styles from './index.less';
import { initCornerstone } from '../../../../components/prints/cornerstone/index';
import { PatientType } from '@/models/patients';
import { ToolGroupManager, ZoomTool, PanTool, PlanarRotateTool, Enums as csToolsEnums } from '@cornerstonejs/tools';
import { IToolGroup } from '@cornerstonejs/tools/dist/types/types';
import { IMouseLeftToolType } from '@/utils/enums';

interface IFilmShowcaseAreaProps {
  dispatch: Dispatch<any>;
  filmNum: PrintType['filmNum'];
  selectedFilmId: PrintType['selectedFilmId'];
  selectedDicomId: PrintType['selectedDicomId'];
  filmsConfig: PrintType['filmsConfig'];
  curInstances: PrintType['curInstances'];
}

const filmNumToLayout = {
  1: [1, 1],
  2: [1, 2],
  3: [1, 3],
  4: [2, 2],
};

const { MouseBindings } = csToolsEnums;

const FilmShowcaseArea: React.FC<IFilmShowcaseAreaProps> = (props) => {
  const {
    dispatch,
    filmNum,
    selectedFilmId,
    selectedDicomId,
    filmsConfig,
    curInstances,
    // cornerstoneInited,
  } = props;
  // console.log('FilmShowcaseArea props:', props);
  const filmLayout = useMemo(() => filmNumToLayout[filmNum], [filmNum]);

  const renderAreas = useCallback(() => {
    return new Array(filmLayout[0]).fill(null).map((_, rowIndex) => (
      <Row key={`row-${rowIndex}`} style={{ width: '100%', height: `${100 / filmLayout[0]}%` }}>
        {new Array(filmLayout[1]).fill(null).map((__, colIndex) => {
          const areaIndex = colIndex + 1 + (filmLayout[0] !== 1 ? rowIndex * filmLayout[1] : 0);

          return (
            <Col
              className={`${styles.displayArea} ${[selectedFilmId].flat().indexOf(areaIndex) !== -1 ? styles.selected : ''}`}
              id={String(areaIndex)}
              key={`col-${rowIndex}-${colIndex}`}
              style={{ width: `${100 / filmLayout[1]}%`, height: '100%' }}
              onClick={filmSelectHandler}
            >
              <FilmContainer
                filmId={areaIndex}
                selectValue={selectedDicomId}
                filmConfig={filmsConfig?.[areaIndex - 1]}
                onChange={DicomSelectHandler}
                curInstances={curInstances}
              />
            </Col>
          );
        })}
      </Row>
    ));
  }, [filmLayout, selectedFilmId, filmsConfig, selectedDicomId, curInstances]);

  useEffect(() => {
    initCornerstone().then(() => {
      const initToolGroup = ToolGroupManager.createToolGroup('stackViewportToolGroup');
      initToolGroup?.addTool(ZoomTool.toolName);
      initToolGroup?.addTool(PanTool.toolName);
      initToolGroup?.addTool(PlanarRotateTool.toolName);
    });
  }, []);

  const filmSelectHandler = (e: any) => {
    if (!Array.isArray(selectedFilmId)) {
      dispatch({
        type: 'prints/stateChangeHandler',
        payload: {
          selectedFilmId: Number(e.target.id),
          selectedDicomId: e.target.id === selectedFilmId ? selectedDicomId : null,
        },
      });
    }
  };

  const DicomSelectHandler = (selectValue: string | null) => {
    // console.log('DicomSelectChange', selectValue);
    dispatch({
      type: 'prints/stateChangeHandler',
      payload: {
        selectedDicomId: selectValue,
        selectedFilmId: Number(selectValue?.split('-')[0]),
      },
    });
  };

  return (
    <div className={styles.filmContainer}>
      {/* <div className={`${styles.siderItemTitle} dragHandler`}>胶片展示</div> */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          right: 0,
          bottom: 0,
          left: 0,
          // paddingTop: '38px',
        }}
      >
        <div id='printArea' className={styles.filmShowcase}>
          {renderAreas()}
        </div>
      </div>
    </div>
  );
};

const mapStateToProps = ({ prints, patients }: { prints: PrintType; patients: PatientType }) => {
  const { filmNum, selectedFilmId, selectedDicomId, filmsConfig, curInstances } = prints;
  // const { cornerstoneInited } = patients;
  return {
    filmNum,
    selectedFilmId,
    selectedDicomId,
    filmsConfig,
    curInstances,
    // cornerstoneInited,
  };
};

export default connect(mapStateToProps)(FilmShowcaseArea);
