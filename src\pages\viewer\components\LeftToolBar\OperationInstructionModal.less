.operationInstructionModal {
  :global {
    .ant-modal-content {
      background-color: #262626;
    }
  }
}

.instructionContent {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0 10px;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #333;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #666;
  }
}

.instructionTable {
  width: 100%;
  border-collapse: collapse;
  color: #fff;

  th, td {
    border: 1px solid #444;
    padding: 12px;
    text-align: left;
  }
  
  th:nth-child(1), td:nth-child(1) {
    width: 150px; /* 增加操作名称列宽度 */
    min-width: 150px;
  }
  
  th:nth-child(2), td:nth-child(2) {
    width: 150px; /* 增加控件列宽度 */
    min-width: 150px;
  }

  th {
    background-color: #333;
    font-weight: normal;
  }

  tr:nth-child(even) {
    background-color: #2a2a2a;
  }

  tr:hover {
    background-color: #303030;
  }
}
.closeIcon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 28px;
  height: 28px;
  color: #ffffff;
  font-weight: 600;
  border-radius: 50%;
  background-color: #D92E2D;
}
// 弹出框 样式重新
.modalHeader {
  padding: 12px 20px!important;
  font-weight: 400!important;
  background-color: #4C4C4C!important;
}
.modalContent {
  color: #ffffff;
  padding: 0 0 24px!important;
  background-color: #333233!important;
}
.modalBody {
  padding:16px 24px 0!important;
}
.modalFooter {
  border-radius: 10px!important;
  margin: 0px 16px;
}