.filmContainer {
  background-color: #404040;
  height: 100%;
  width: 100%;
}
.siderItemTitle {
  width: 100%;
  border-bottom: rgba(128, 128, 128, 0.5) 1px solid;
  line-height: 35px;
  font-size: 18px;
  color: white;
  padding: 0 10px;
}
.filmShowcase {
  width: 100%;
  height: 100%;
  border: 1px gray;
  .ant-row,
  .ant-col {
    padding: 2px;
  }
}
.displayArea {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  border: 1px solid #d9d9d9;
  cursor: pointer;
  padding: 0;
  &:hover {
    border-color: #00a0e9;
  }
}
.selected {
  border-color: #00a0e9;
}
.seriesContainer {
  width: 100%;
  height: 100%;
  .ant-col {
    padding: 2px !important;
  }
}
.imgsContainer {
  width: 100%;
  height: 100%;
  .ant-row,
  .ant-col {
    padding: 2px;
  }
}
.dicomRender {
  width: inherit;
  height: inherit;
}
.dicomRenderContainer {
  width: 100%;
  height: 100%;
}
