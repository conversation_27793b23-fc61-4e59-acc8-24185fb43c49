import React, { useEffect, useState } from 'react';
import { type Dispatch } from 'umi';
import { connect } from 'dva';
import { Row, Col, Empty } from 'antd';
import style from './viewer.less';
import withAuth from '@/hocs/withAuth';
import { ViewType } from '@/models/views';
import ImgViewer from '@/components/viewer/ImgViewer';
import RightToolBar from '@/components/viewer/RightToolBar';
import LeftToolbar from '@/components/viewer/LeftToolBar';

import { TopToolsBar } from 'rayplus-three-view';

const { Responsive } = require('react-grid-layout');
import { useResizeDetector } from 'react-resize-detector';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import storeUtil from '@/utils/store';
const _ = require('lodash');

const userAuths = {
  can_ListSelfSeries: true,
  can_ListResearchCenterSeries: true,
  can_ListAllSeries: true,
  can_UploadDicom: true,
  can_DownloadDicom: true,
  can_LookUpFilm: true,
  can_EditFilm: true,
  can_Registration: true,
  can_ROIAnalyse: true,
  can_ThreeDimension: true,
  can_FourDimension: true,
};

interface InfoProps {
  dispatch: Dispatch;
  initialLayout?: any;
  leftFoldLayout?: any;
  leftFoldLayout_2k?: any;
  leftFoldLayout_4k?: any;
  rightFoldLayout?: any;
  rightFoldLayout_2k?: any;
  rightFoldLayout_4k?: any;
  allFoldLayout?: any;
  allFoldLayout_2k?: any;
  allFoldLayout_4k?: any;
  leftUnfoldLayout?: any;
  rightUnfoldLayout?: any;
  allUnfoldLayout?: any;
  layout_2k?: any;
  layout_4k?: any;
  layout_ipad?: any;
  leftFoldLayout_pad?: any;
  rightFoldLayout_pad?: any;
  allFoldLayout_pad?: any;
  pid?: number;
}

const Viewer: React.FC<InfoProps> = ({
  dispatch,
  initialLayout,
  leftFoldLayout,
  rightFoldLayout,
  allFoldLayout,
  layout_2k,
  layout_4k,
  layout_ipad,
  allFoldLayout_2k,
  allFoldLayout_4k,
  leftFoldLayout_2k,
  leftFoldLayout_4k,
  leftFoldLayout_pad,
  rightFoldLayout_pad,
  allFoldLayout_pad,
  rightFoldLayout_2k,
  rightFoldLayout_4k,
  //pid, // 修改为使用localStorage中存储的值
}) => {
  // pid
  const pid = Number(storeUtil.get('patientId').value);
  // console.log('pid:', pid);

  // 布局
  const [layouts, setLayouts] = useState<any>(undefined);
  // 布局的开启状态
  const [layoutState, setLayoutState] = useState({
    top: true,
    left: true,
    right: true,
  });
  const [currentBreakpoint, setCurrentBreakpoint] = useState('lg');
  const [isLeftHidden, setIsLeftHidden] = useState(false);
  const [isRightHidden, setIsRightHidden] = useState(false);
  const { width, height, ref: targetRef } = useResizeDetector();
  const [layout, setLayout] = useState(3);

  const Contents = {
    top: (
      <Row style={{ marginTop: 7 }} align='middle'>
        <Col span={24}>
          <TopToolsBar />
        </Col>
      </Row>
    ),
    left: (
      <>
        {/* <div className={`${style.layout_items_header} rgl-drag-zone`}>
          序列列表
        </div> */}
        <LeftToolbar />
      </>
    ),
    right: (
      <>
        {isRightHidden ? '' : <div className={`${style.layout_items_header} rgl-drag-zone`}>工具栏</div>}
        <RightToolBar />
      </>
    ),
    img: (
      <>
        {/* <div className={`${style.layout_items_header} rgl-drag-zone`}>
          阅片区
        </div> */}
        <ImgViewer layout={layout} pid={pid} />
      </>
    ),
  };

  useEffect(() => {
    let showRightToolBar: boolean = !userAuths.can_LookUpFilm;
    Object.keys(userAuths).map((item) => {
      if (item !== 'can_LookUpFilm') {
        showRightToolBar += userAuths[item];
      }
    });
    //setLayouts({ lg: initialLayout });
    setLayouts({
      lg: showRightToolBar ? initialLayout : rightFoldLayout,
      xl: showRightToolBar ? layout_2k : rightFoldLayout_2k,
      xxl: showRightToolBar ? layout_4k : rightFoldLayout_4k,
      md: showRightToolBar ? layout_ipad : rightFoldLayout_pad,
    });
    return () => {};
  }, [initialLayout]);

  useEffect(() => {
    // console.log('Viewer: width:', width);
    // console.log('Viewer: height:', height);
    // console.log('here is screen size', screen.width, screen.height);
    if (screen.width < 1200) {
      setIsLeftHidden(true);
      setLayouts({
        md: leftFoldLayout_pad,
        lg: leftFoldLayout,
        xl: leftFoldLayout_2k,
        xxl: leftFoldLayout_4k,
      });
    }
    return () => {};
  }, [width, height]);

  const generateDOM = () => {
    if (!layouts) return <></>;
    return _.map(layouts[currentBreakpoint], (item: any) => {
      return (
        <div key={item.i} className={item.i === 'img' ? style.layout_items_viewer : style.layout_items}>
          {Contents[item.i]}
        </div>
      );
    });
  };

  return (
    <div
      style={{
        backgroundColor: '#404040',
        height: '100%',
        padding: 0,
        margin: -40,
      }}
    >
      {pid ? (
        <div style={{ padding: '8px' }} ref={targetRef}>
          <Responsive
            width={width || 0}
            className='layout'
            layouts={layouts}
            breakpoints={{
              xxl: 3500,
              xl: 2000,
              lg: 1200,
              md: 996,
              sm: 768,
              xs: 480,
              xxs: 0,
            }}
            cols={{ xxl: 12, xl: 12, lg: 12, md: 11.5, sm: 6, xs: 4, xxs: 2 }}
            isBounded={true}
            rowHeight={50}
            verticalCompact={true}
            compactType='horizontal'
            draggableHandle='.rgl-drag-zone'
            margin={[4, 4]}
            containerPadding={[0, 0]}
            measureBeforeMount={false}
            onLayoutChange={(_layout: any, _layouts: any) => {
              setLayouts(_layouts);
            }}
            resizeHandles={['se']}
            resizeHandle={(resizeHandleAxis: any) => {
              if (resizeHandleAxis === 'e') {
                return (
                  <div
                    className={style.custom_resizable_handle}
                    style={{
                      bottom: '50%',
                      transform: 'rotate(0deg)',
                      cursor: 'ew-resize',
                    }}
                  >
                    {' '}
                  </div>
                );
              }
              return <div className={style.custom_resizable_handle}> </div>;
            }}
          >
            {generateDOM()}
          </Responsive>
        </div>
      ) : (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={<span style={{ color: 'white' }}>请在患者列表选择患者进行阅片！</span>}
        />
      )}
    </div>
  );
};

Viewer.defaultProps = {
  initialLayout: [
    // { i: 'top', x: 0, y: 0, w: 12, h: 1, static: true, resizeHandles: [] },
    // { i: 'left', x: 8, y: 1, w: 3, h: 3, resizeHandles: [] },
    // { i: 'right', x: 8, y: 5, w: 3, h: 11 },
    // { i: 'img', x: 0, y: 1, w: 9, h: 15 },
    { i: 'top', x: 0, y: 0, w: 2, h: 1, static: true, resizeHandles: [] },
    { i: 'left', x: 0, y: 1, w: 2, h: 4, resizeHandles: [] },
    { i: 'right', x: 0, y: 5, w: 2, h: 11.5 },
    { i: 'img', x: 2, y: 0, w: 10, h: 17 },
  ],
  //ipad 尺寸为1180×820
  //一个单位为100像素
  layout_ipad: [
    // { i: 'top', x: 0, y: 0, w: 12, h: 1, static: true, resizeHandles: [] }
    { i: 'left', x: 0, y: 0, w: 2, h: 7.5 },
    { i: 'right', x: 10, y: 0, w: 2, h: 7.5 },
    { i: 'img', x: 2, y: 0, w: 7.5, h: 7.5 },
  ],
  layout_2k: [
    // { i: 'top', x: 0, y: 0, w: 12, h: 1, static: true, resizeHandles: [] },
    // { i: 'left', x: 0, y: 0, w: 2, h: 12 },
    // { i: 'right', x: 10, y: 0, w: 2, h: 12 },
    // { i: 'img', x: 2, y: 0, w: 8, h: 12 },
    { i: 'top', x: 0, y: 0, w: 12, h: 1, static: true, resizeHandles: [] },
    { i: 'left', x: 8, y: 1, w: 3, h: 6, resizeHandles: [] },
    { i: 'right', x: 8, y: 7, w: 3, h: 12 },
    { i: 'img', x: 0, y: 1, w: 9, h: 18 },
  ],
  layout_4k: [
    // { i: 'top', x: 0, y: 0, w: 12, h: 1, static: true, resizeHandles: [] },
    { i: 'left', x: 0, y: 0, w: 2, h: 20 },
    { i: 'right', x: 10, y: 0, w: 2, h: 20 },
    { i: 'img', x: 2, y: 0, w: 8, h: 20 },
  ],
  leftFoldLayout: [
    // { i: 'top', x: 0, y: 0, w: 12, h: 1, static: true, resizeHandles: [] },
    { i: 'left', x: 0, y: 0, w: 0, h: 9 },
    { i: 'right', x: 10, y: 0, w: 2, h: 9 },
    { i: 'img', x: 0, y: 0, w: 10, h: 9 },
  ],
  leftFoldLayout_pad: [
    // { i: 'top', x: 0, y: 0, w: 12, h: 1, static: true, resizeHandles: [] }
    { i: 'left', x: 0, y: 0, w: 0, h: 7.5 },
    { i: 'right', x: 10, y: 0, w: 2.5, h: 7.5 },
    { i: 'img', x: 0, y: 0, w: 9, h: 7.5 },
  ],
  leftFoldLayout_2k: [
    // { i: 'top', x: 0, y: 0, w: 12, h: 1, static: true, resizeHandles: [] },
    { i: 'left', x: 0, y: 0, w: 0, h: 12 },
    { i: 'right', x: 10, y: 0, w: 2, h: 12 },
    { i: 'img', x: 0, y: 0, w: 10, h: 12 },
  ],
  leftFoldLayout_4k: [
    // { i: 'top', x: 0, y: 0, w: 12, h: 1, static: true, resizeHandles: [] },
    { i: 'left', x: 0, y: 0, w: 0, h: 20 },
    { i: 'right', x: 10, y: 0, w: 2, h: 20 },
    { i: 'img', x: 0, y: 0, w: 10, h: 20 },
  ],
  rightFoldLayout: [
    // { i: 'top', x: 0, y: 0, w: 12, h: 1, static: true, resizeHandles: [] },
    { i: 'left', x: 0, y: 0, w: 2, h: 9 },
    { i: 'right', x: 1, y: 0, w: 0, h: 9 },
    { i: 'img', x: 2, y: 0, w: 10, h: 9 },
  ],
  rightFoldLayout_pad: [
    // { i: 'top', x: 0, y: 0, w: 12, h: 1, static: true, resizeHandles: [] }
    { i: 'left', x: 0, y: 0, w: 2.5, h: 7.5 },
    { i: 'right', x: 1, y: 0, w: 0, h: 7.5 },
    { i: 'img', x: 2, y: 0, w: 9, h: 7.5 },
  ],
  rightFoldLayout_2k: [
    // { i: 'top', x: 0, y: 0, w: 12, h: 1, static: true, resizeHandles: [] },
    { i: 'left', x: 0, y: 0, w: 2, h: 12 },
    { i: 'right', x: 1, y: 0, w: 0, h: 12 },
    { i: 'img', x: 2, y: 0, w: 10, h: 12 },
  ],
  rightFoldLayout_4k: [
    // { i: 'top', x: 0, y: 0, w: 12, h: 1, static: true, resizeHandles: [] },
    { i: 'left', x: 0, y: 0, w: 2, h: 20 },
    { i: 'right', x: 1, y: 0, w: 0, h: 20 },
    { i: 'img', x: 2, y: 0, w: 10, h: 20 },
  ],
  allFoldLayout: [
    // { i: 'top', x: 0, y: 0, w: 12, h: 1, static: true, resizeHandles: [] },
    { i: 'left', x: 0, y: 0, w: 0, h: 9 },
    { i: 'right', x: 0, y: 0, w: 0, h: 9 },
    { i: 'img', x: 2, y: 0, w: 12, h: 9 },
  ],
  allFoldLayout_pad: [
    // { i: 'top', x: 0, y: 0, w: 12, h: 1, static: true, resizeHandles: [] }
    { i: 'left', x: 0, y: 0, w: 0, h: 7.5 },
    { i: 'right', x: 0, y: 0, w: 0, h: 7.5 },
    { i: 'img', x: 2, y: 0, w: 11.5, h: 7.5 },
  ],
  allFoldLayout_2k: [
    // { i: 'top', x: 0, y: 0, w: 12, h: 1, static: true, resizeHandles: [] },
    { i: 'left', x: 0, y: 0, w: 0, h: 12 },
    { i: 'right', x: 0, y: 0, w: 0, h: 12 },
    { i: 'img', x: 2, y: 0, w: 12, h: 12 },
  ],
  allFoldLayout_4k: [
    // { i: 'top', x: 0, y: 0, w: 12, h: 1, static: true, resizeHandles: [] },
    { i: 'left', x: 0, y: 0, w: 0, h: 20 },
    { i: 'right', x: 0, y: 0, w: 0, h: 20 },
    { i: 'img', x: 2, y: 0, w: 12, h: 20 },
  ],
};

const mapStateToProps = ({ views }: { views: ViewType }) => {
  return {
    // 阅片model存储的信息
    pid: views.patientId,
  };
};

export default withAuth(connect(mapStateToProps)(Viewer));
