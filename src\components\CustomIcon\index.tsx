import { createFromIconfontCN } from '@ant-design/icons';

const Icon = createFromIconfontCN({
  scriptUrl: '//at.alicdn.com/t/c/font_4956434_9fdo88d1grq.js',
});
const CustomIcon = (props: any) => {
  const { type, className, ...rest } = props;
  // 将 className 属性正确传递给 Icon 组件，并设置默认字体大小为 18px
  return <Icon className={className} type={type} style={{ fontSize: 18, ...rest.style }} {...rest} />;
};

export default CustomIcon;