import React, { useState, useEffect } from 'react';
import { Row, Col, Radio, Button } from 'antd';
import styles from './index.less';

const layoutRadiooptions = [
  { label: '1×1', value: 11 },
  { label: '1×2', value: 12 },
  { label: '2×1', value: 21 },
  { label: '2×2', value: 22 },
  { label: '1×3', value: 13 },
  { label: '3×1', value: 31 },
];

interface ICustomizeRadioProps {
  value: number;
  onChange: (e: any) => void;
  onCustomize: () => void;
}

const CustomizeRadio: React.FC<ICustomizeRadioProps> = (props) => {
  const { value, onChange, onCustomize } = props;
  const [isCustom, setIsCustom] = useState(false);
  const [customLabel, setCustomLabel] = useState<string | undefined>(undefined);

  useEffect(() => {
    const updateCustomLabel = () => {
      const matchedOption = layoutRadiooptions.find((option) => option.value === value);

      if (matchedOption) {
        setIsCustom(false);
      } else {
        const newCustomLabel = `${Math.floor(props.value / 10)}×${props.value % 10}`;
        setIsCustom(true);
        setCustomLabel(newCustomLabel);
      }
    };

    updateCustomLabel();
  }, [value]);

  let selectedValue = value;
  if (isCustom && customLabel) {
    selectedValue = -1; // 使用特殊值表示自定义状态
  }

  return (
    <Row style={{ width: '100%' }}>
      <Col span={20} style={{ display: 'flex', flexWrap: 'nowrap' }}>
        <Radio.Group
          value={value}
          optionType='button'
          onChange={props.onChange}
          options={layoutRadiooptions}
          className={styles.radioGroup}
        />
      </Col>
      <Col span={1} />
      <Col span={3}>
        <Button
          style={{ width: '100%', padding: 0, fontSize: 12 }}
          onClick={props.onCustomize}
          type={isCustom ? 'primary' : 'default'}
        >
          {isCustom ? customLabel || '自定义' : '自定义'}
        </Button>
      </Col>
    </Row>
  );
};

export default CustomizeRadio;
