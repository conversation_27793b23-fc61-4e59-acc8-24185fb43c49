.dicom_area {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  background-color: #000000;
  border-radius: 10px;
}
.dirom {
  z-index: 999;
  width: 100%;
  min-height: 300px;
  height: calc(100% - 16px);
}
.front {
  z-index: 1000;
  position: absolute;
  top: 0%;
  left: 50%;
  transform: translate(-50%, 8px);
}
.right {
  z-index: 1000;
  position: absolute;
  top: 50%;
  left: 0%;
  transform: translate(8px, -50%);
}
.left {
  z-index: 1000;
  position: absolute;
  top: 50%;
  right: 0%;
  transform: translate(-8px, -50%);
}
.queen {
  z-index: 1000;
  position: absolute;
  bottom: 0%;
  left: 50%;
  transform: translate(-50%, -8px);
}
.index {
  z-index: 1000;
  position: absolute;
  top: 0%;
  left: 0%;
  transform: translate(8px, 8px);
}
.time {
  z-index: 1000;
  position: absolute;
  bottom: 0%;
  left: 0%;
  transform: translate(8px, -8px);
}
.frame {
  z-index: 1000;
  position: absolute;
  bottom: 0%;
  right: 0%;
  transform: translate(-8px, -8px);
}