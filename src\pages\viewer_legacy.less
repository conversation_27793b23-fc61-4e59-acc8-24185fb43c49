.layout_items {
  // background: #0e0e0e;
  background: #404040;
  // border: 1px solid black;
  // opacity: 30%;
  // overflow: scroll;
  // padding: 0%;
  // height: auto;
}

.layout_items_viewer {
  // background: #0e0e0e;
  background: #404040;

  // background: #101621;

  // border: 1px solid black;
  // opacity: 30%;
}

.layout_items_header {
  display: flex;
  padding: 8px 8px 8px 16px;
  color: white;
  font-size: 14px;
  background-color: #2d2d2d;
  // background-color: #1c1c1c;
  box-shadow: inset 0 0 4px #1b2436;
  cursor: pointer;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
}

.custom_resizable_handle {
  position: absolute;
  right: -4px;
  bottom: -4px;
  z-index: 10;
  width: 0;
  height: 0;
  background: none;

  border: 7px solid transparent;
  border-left-color: #d8dce5;
  transform: rotate(45deg);

  cursor: se-resize;
}

.viewport_wrapper {
  border: 2px solid black;
}

.viewport_wrapper_active {
  border: 2px solid dodgerblue;
}

body::-webkit-scrollbar {
  display: none;
}
