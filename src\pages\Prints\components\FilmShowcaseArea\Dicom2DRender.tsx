import { Enums } from '@cornerstonejs/core';
import { <PERSON>T<PERSON>, ToolGroupManager, ZoomTool, PlanarRotateTool, Enums as csToolsEnums } from '@cornerstonejs/tools';
import React, { useEffect, useRef, useState, useMemo, useC<PERSON>back, Key, WheelEventHandler } from 'react';
import dicomParser from 'dicom-parser';
import { VERSION } from '@/utils/consts';
import styles from './index.less';
import { IRenderingEngine, IStackViewport } from '@cornerstonejs/core/dist/types/types';
import { message } from 'antd';
import { connect } from 'umi';
import type { PrintType } from '@/models/prints';
import { IMouseLeftToolType } from '@/utils/enums';

interface IDicomRenderProps {
  key: Key;
  renderIndex: number;
  row: number;
  col: number;
  series: {
    Id: string;
    [key: string]: any;
  };
  curInstances: Map<string, any[]>;
  isInverse: boolean;
  space: number;
  onWheelHandler?: (seriesId: string, renderIndex: number, nextIndex: number, space: number) => void;
  getRenderingEngine: (seriesId: string) => IRenderingEngine | undefined;
}

const { ViewportType } = Enums;
const { MouseBindings } = csToolsEnums;

const Dicom2DRender: React.FC<IDicomRenderProps & { mouseLeftTool: IMouseLeftToolType }> = React.memo((props) => {
  const {
    row = 1,
    col = 1,
    renderIndex,
    curInstances,
    isInverse = false,
    series,
    space = 1,
    onWheelHandler,
    getRenderingEngine,
    mouseLeftTool,
  } = props;
  const rendererRef = useRef<HTMLDivElement>(null);
  const toolGroup = ToolGroupManager.getToolGroup('stackViewportToolGroup');
  const [currentImageIndex, setCurrentImageIndex] = useState(renderIndex * space);
  const [imgMetaData, setImgMetaData] = useState<any>({});

  const imageIds = useMemo(() => {
    if (!series) return undefined;
    const originImageIds = curInstances
      .get(String(series.Id))
      ?.map((instence: any) => `wadouri:${CLOUD_API}/v1?Action=GetDicomFileInPrint&Version=${VERSION}&InstanceId=${instence.Id}`);
    return isInverse ? originImageIds?.reverse() : originImageIds;
  }, [isInverse, curInstances, rendererRef]);

  const bindToolsForViewport = useCallback(() => {
    const isPanTool = mouseLeftTool === IMouseLeftToolType.PanTool;
    const isZoomTool = mouseLeftTool === IMouseLeftToolType.ZoomTool;
    toolGroup?.setToolActive(PlanarRotateTool.toolName, {
      bindings: [
        {
          mouseButton: MouseBindings.Secondary, // Right Click
        },
      ],
    });
    if (isPanTool) {
      toolGroup?.setToolPassive(ZoomTool.toolName);
      toolGroup?.setToolActive(PanTool.toolName, {
        bindings: [
          {
            mouseButton: MouseBindings.Primary, // left Click
          },
        ],
      });
    }
    if (isZoomTool) {
      toolGroup?.setToolPassive(PanTool.toolName);
      toolGroup?.setToolActive(ZoomTool.toolName, {
        bindings: [
          {
            mouseButton: MouseBindings.Primary, // left Click
          },
        ],
      });
    }
  }, [toolGroup, mouseLeftTool]);

  const loadImageMetaData = useCallback((image: any) => {
    if (!image) return;
    const dataSet = image?.data;

    // 实现具体的元数据提取逻辑
    const readValue = (tag: string, vr: string) => {
      switch (vr) {
        case 'PN':

          return dicomParser.parsePN(dataSet.string(tag));
        case 'DA':
          return dicomParser.parseDA(dataSet.string(tag));
        case 'TM':
          return dicomParser.parseTM(dataSet.string(tag));
        case 'UI':
        case 'LO':
          return dataSet.string(tag);
        case 'SH':
        case 'ST':
        case 'CS':
          return dataSet.string(tag);
        case 'LT':
          return dataSet.string(tag);
        case 'US':
          return dataSet.uint16(tag);
        case 'SS':
        case 'SL':
          return dataSet.int32(tag);
        case 'UL':
          return dataSet.uint32(tag);
        case 'FL':
        case 'FD':
        case 'DS':
          return dataSet.floatString(tag);
        default:
          return null;
      }
    };
    // 具体更多项目可以参阅https://cornerstonejs.github.io/dicomParser/examples/dumpWithDataDictionary/index.html
    const tagsOfInterest = [
      ['x00080020', 'StudyDate', 'DA'],
      ['x00080021', 'SeriesDate', 'DA'],
      ['x00080030', 'StudyTime', 'TM'],
      ['x00080031', 'SeriesTime', 'TM'],
      ['x00100020', 'PatientID', 'LO'],
      ['x00100040', 'PatientSex', 'CS'],
      ['x00100010', 'PatientName', 'PN'],
      ['x00101010', 'PatientAge', 'AS'],
      ['x00281051', 'WindowWidth', 'DS'],
      ['x00541330', 'ImageIndex', 'US'],
      ['x00080060', 'Modality', 'CS'],
    ];
    //const metadata = {};
    const metadata: Record<string, any> = {};
    tagsOfInterest.forEach(([tag, name, vr]) => {
      metadata[name] = readValue(tag, vr);
    });
    setImgMetaData(metadata);
  }, []);

  useEffect(() => {
    setCurrentImageIndex(renderIndex * space);
  }, [space, row, col]);

  useEffect(() => {
    if (!imageIds || !imageIds.length) return;
    if (rendererRef.current) {
      const viewportId = `STACK_${renderIndex}`;
      const viewportInput = {
        viewportId,
        element: rendererRef.current,
        type: ViewportType.STACK,
      };
      const curRenderingEngine = getRenderingEngine(series?.Id);
      curRenderingEngine?.enableElement(viewportInput);
      const viewport: IStackViewport | undefined = curRenderingEngine?.getViewport(viewportId) as IStackViewport;
      if (viewport && curRenderingEngine) {
        try {
          viewport.setStack(imageIds).then(() => {
            viewport.render();
            const image = viewport?.csImage;
            if (image) {
              loadImageMetaData(image); // 加载 DICOM 元数据
            }
          });
          toolGroup?.addViewport(viewport.id, curRenderingEngine.id);
          bindToolsForViewport();
          viewport.setImageIdIndex(currentImageIndex);
        } catch (error) {
          message.error('视口渲染失败请重试');
          console.error('Error setting the stack:', error);
        }
      }
    }
  }, [imageIds, rendererRef, loadImageMetaData, bindToolsForViewport]);

  const handleWheel: WheelEventHandler<HTMLDivElement> = useCallback(
    (event) => {
      if (!imageIds || imageIds.length <= 1) return;
      const step = event.deltaY > 0 ? space : -space;
      const maxIndex = Math.floor((imageIds.length - 1) / space) * space - (row * col + renderIndex - 1) * space;

      const nextIndex = Math.max(renderIndex * space, Math.min(maxIndex, currentImageIndex + step));
      onWheelHandler?.(series.Id, renderIndex, nextIndex, space);
      setCurrentImageIndex(nextIndex);
    },
    [space, imageIds, currentImageIndex, onWheelHandler]
  );

  return series ? (
    <div style={{ width: '100%', height: '100%' }}>
      <div ref={rendererRef} className={styles.dicomRender} onWheel={handleWheel} onContextMenu={(e) => e.preventDefault()} />
      {imgMetaData ? (
        <div style={{ position: 'absolute', top: 10, left: 10 }}>
          {Object.keys(imgMetaData as object)?.map((key) => (
            <div style={{ color: 'white' }}>
              {key}:{imgMetaData[key]?.toString()}
            </div>
          ))}
        </div>
      ) : null}
    </div>
  ) : (
    <span>请选择序列</span>
  );
});

const mapStateToProps = ({ prints }: { prints: PrintType }) => {
  const { mouseLeftTool } = prints;
  return { mouseLeftTool };
};

export default connect(mapStateToProps)(Dicom2DRender);
