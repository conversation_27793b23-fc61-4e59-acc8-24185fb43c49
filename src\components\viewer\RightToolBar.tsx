import React from 'react';
import { Row, Col } from 'antd';
import { LeftToolsBarWebGL } from 'rayplus-three-view';
import { useResizeDetector } from 'react-resize-detector';

// 阅片界面的组件
interface RightToolBar {
  loading?: boolean;
  dataSource?: any;
  columns?: any;
  total?: any;
  current?: number;
  setCurrent?: any;
}

const RightToolBar: React.FC<RightToolBar> = ({
  loading,
  dataSource,
  columns,
  total,
  current,
  setCurrent,
}) => {
  const { width, height, ref: containerRef } = useResizeDetector();
  return (
    <>
      <div
        style={{
          width: '100%',
          height: 'calc(100% - 52px)',
          backgroundColor: '#404040',
          overflowY: 'hidden',
          overflowX: 'hidden',
        }}
        ref={containerRef}
      >
        <LeftToolsBarWebGL width={width} />,
      </div>
    </>
  );
};
export default RightToolBar;
