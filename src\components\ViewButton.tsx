/**
 * 图片与文字组合而成的可点击按钮
 *
 */
import React, { useState } from 'react';
import { Image } from 'antd';

interface ViewButton {
  text: string;
  img: string;
  img_width: string | number;
  isSelected: boolean;

  // 点击事件
  onClick: () => void;
}

const ViewButton: React.FC<ViewButton> = ({ text, img, img_width, isSelected, onClick }) => {
  const [isHovered, setIsHovered] = useState(false);
  return (
    <div
      style={{
        userSelect: 'none',
        display: 'flex',
        flexDirection: 'column', // 图片在上，文字在下
        alignItems: 'center', // 居中对齐
        justifyContent: 'center',
        cursor: 'pointer',
        border: '1px solid #ccc', // 方框的边框
        borderRadius: 8, // 圆角
        padding: '10px', // 内边距
        //   backgroundColor: isHovered ? '#2A4561' : '#535353', // 悬停背景颜色变化
        backgroundColor: isHovered || isSelected ? '#2A4561' : '#535353', // 悬停或选中时的背景颜色变化
        width: '50px', // 固定宽度
        height: '70px', // 固定高度
        transition: 'background-color 0.3s ease', // 背景颜色过渡效果
      }}
      onMouseOver={(e) => {
        // 使鼠标变为选择状态
        (e.target as HTMLElement).style.cursor = 'pointer';
      }}
      onMouseEnter={() => setIsHovered(true)} // 当鼠标进入时设置悬停状态
      onMouseLeave={() => setIsHovered(false)} // 当鼠标离开时取消悬停状态
      onClick={onClick}
    >
      <Image src={img} preview={false} width={img_width} />
      <span
        style={{
          color: 'white',
          marginTop: 4, // 图片与文字的间距
          fontWeight: 'bold',
          fontSize: 12,
          textAlign: 'center', // 文本居中
        }}
      >
        {text}
      </span>
    </div>
  );
};

export default ViewButton;
