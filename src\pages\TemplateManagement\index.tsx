import React, { useState, useEffect } from 'react';
import { ConfigProvider, theme } from 'antd';
import { connect, Dispatch } from 'umi';
import type { ApiCategory, ApiTemplate, ApiGroupTemplate } from './data.d';
import CreateTemplate from './components/createTemplate';
import MyCategories from './components/myCategories';
import GroupCategories from './components/groupCategories';
import CreateCategoryModal from './components/createCategoryModal';
import MyTemplates from './components/myTemplates';
import GroupTemplates from './components/groupTemplates';
import ImportModal from './components/importModal';
import { TemplateManagementState } from './model';

interface TemplateManagementProps {
  dispatch: Dispatch;
  templateManagement: TemplateManagementState;
}

const TemplateManagement: React.FC<TemplateManagementProps> = ({ dispatch, templateManagement }) => {
  const [activeTab, setActiveTab] = useState('myTemplates');
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isImportModalVisible, setIsImportModalVisible] = useState(false);
  const [isCategoryModalVisible, setIsCategoryModalVisible] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<ApiTemplate | null>(null);
  const [editingGroupTemplate, setEditingGroupTemplate] = useState<ApiGroupTemplate | null>(null);
  const [editingReadOnly, setEditingReadOnly] = useState<boolean>(false);
  const [editingCategory, setEditingCategory] = useState<ApiCategory | null>(null);

  const {
    templates,
    templatePagination,
    templatesLoading,
    categories,
    categoryPagination,
    categoriesLoading,
    groupTemplates,
    groupTemplatePagination,
    groupTemplatesLoading,
    groupCategories,
    groupCategoryPagination,
    groupCategoriesLoading,
  } = templateManagement;

  useEffect(() => {
    if (activeTab === 'myTemplates') {
      dispatch({
        type: 'templateManagement/fetchTemplates',
        payload: { pagination: templatePagination },
      });
    } else if (activeTab === 'myCategories') {
      dispatch({
        type: 'templateManagement/fetchCategories',
        payload: { pagination: categoryPagination },
      });
    } else if (activeTab === 'teamTemplates') {
      dispatch({
        type: 'templateManagement/fetchGroupTemplates',
        payload: { pagination: groupTemplatePagination },
      });
    } else if (activeTab === 'teamCategories') {
      dispatch({
        type: 'templateManagement/fetchGroupCategories',
        payload: { pagination: groupCategoryPagination },
      });
    }
  }, [activeTab, dispatch]);

  const handleTemplateTableChange = (pagination: any) => {
    dispatch({
      type: 'templateManagement/fetchTemplates',
      payload: { pagination },
    });
  };

  const handleGroupTemplateTableChange = (pagination: any) => {
    dispatch({
      type: 'templateManagement/fetchGroupTemplates',
      payload: { pagination },
    });
  };

  const handleModalClose = () => {
    setIsCreateModalVisible(false);
    setEditingTemplate(null);
    setEditingGroupTemplate(null);
  };

  const handleEditTemplate = (template: ApiTemplate, readOnly: boolean = false) => {
    setEditingTemplate(template);
    setEditingGroupTemplate(null);
    setEditingReadOnly(readOnly);
    setIsCreateModalVisible(true);
  };

  const handleEditGroupTemplate = (template: ApiGroupTemplate, readOnly: boolean = false) => {
    setEditingGroupTemplate(template);
    setEditingTemplate(null);
    setEditingReadOnly(readOnly);
    setIsCreateModalVisible(true);
  };

  const handleCategoryModalClose = () => {
    setIsCategoryModalVisible(false);
    setEditingCategory(null);
  };

  const handleEditCategory = (category: ApiCategory) => {
    setEditingCategory(category);
    setIsCategoryModalVisible(true);
  };

  const handleCreateSuccess = () => {
    handleModalClose();
    // Refetch the templates list
    if (activeTab === 'myTemplates') {
      dispatch({
        type: 'templateManagement/fetchTemplates',
        payload: { pagination: templatePagination },
      });
    } else if (activeTab === 'teamTemplates') {
      dispatch({
        type: 'templateManagement/fetchGroupTemplates',
        payload: { pagination: groupTemplatePagination },
      });
    }
  }

  const handleCategoryCreateSuccess = () => {
    handleCategoryModalClose();
    // Refetch the categories list
    if (activeTab === 'teamCategories') {
      dispatch({
        type: 'templateManagement/fetchGroupCategories',
        payload: { pagination: groupCategoryPagination },
      });
    } else {
      dispatch({
        type: 'templateManagement/fetchCategories',
        payload: { pagination: categoryPagination },
      });
    }
  }

  const handleDeleteTemplate = (id: number) => {
    dispatch({
      type: 'templateManagement/deleteTemplate',
      payload: { id },
    });
  };

  const handleDeleteGroupTemplate = (id: number) => {
    dispatch({
      type: 'templateManagement/deleteGroupTemplate',
      payload: { id },
    });
  };

  const handleDeleteCategory = (id: number) => {
    dispatch({
      type: 'templateManagement/deleteCategory',
      payload: { id },
    });
  };

  const handleDeleteGroupCategory = (id: number) => {
    dispatch({
      type: 'templateManagement/deleteGroupCategory',
      payload: { id },
    });
  };

  const TabButton = ({ title, tabKey }: { title: string, tabKey: string }) => {
    const isActive = activeTab === tabKey;

    const style: React.CSSProperties = {
      padding: '8px 20px',
      cursor: 'pointer',
      color: 'white',
      fontSize: '14px',
      textAlign: 'center',
      backgroundColor: 'transparent',
    };

    if (isActive) {
      style.backgroundColor = '#1F69B4';
      style.borderRadius = '6px';
    }

    return (
      <div
        onClick={() => setActiveTab(tabKey)}
        style={style}
      >
        {title}
      </div>
    );
  };

  return (
    <ConfigProvider
      theme={{
        // algorithm: theme.darkAlgorithm,
        // token: {
        //   colorPrimary: '#1F69B4',
        //   colorBgLayout: '#141414',
        //   colorText: 'rgba(255, 255, 255, 0.85)',
        // },
        components: {
          Table: {
            headerBg: '#1D1D1D',
            colorBgContainer: '#141414',
            borderColor: '#303030',
            headerSplitColor: 'transparent',
            headerSortActiveBg: '#262628',
            headerSortHoverBg: '#262628',
            rowHoverBg: '#1D1D1D',
          },
          Select: {
            colorBgContainer: '#333233',
            colorText: 'white',
            colorTextPlaceholder: 'rgba(255, 255, 255, 0.45)',
            colorBgElevated: '#333233',
            controlItemBgHover: '#555555',
            optionSelectedBg: '#1F69B4',
          },
          // Button: {
          //   defaultBg: '#262628',
          //   defaultColor: '#fff',
          //   defaultBorderColor: 'transparent',
          //   colorLink: '#1F69B4',
          //   colorLinkHover: '#4e8cd4'
          // },
          // Pagination: {
          //   itemBg: 'transparent',
          // },
          // Input: {
          //   colorBgContainer: '#141414',
          //   colorBorder: '#303030'
          // }
        },
      }}
    >
      <div style={{ backgroundColor: '#141414', padding: '16px 24px 24px 24px', minHeight: 'calc(100vh - 90px)', borderRadius: '8px', overflow: 'hidden' }}>
        <div style={{
          display: 'flex',
          borderBottom: '1px solid #303030',
          backgroundColor: '#4C4C4C',
          margin: '-16px -24px 0 -24px',
          padding: '8px 24px',
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px'
        }}>
          <TabButton title="我的报告模板" tabKey="myTemplates" />
          <TabButton title="我的模板分类" tabKey="myCategories" />
          <TabButton title="团队报告模板" tabKey="teamTemplates" />
          <TabButton title="团队模板分类" tabKey="teamCategories" />
        </div>

        <div style={{ display: activeTab === 'myTemplates' ? 'block' : 'none' }}>
          <MyTemplates
            onNewTemplate={() => setIsCreateModalVisible(true)}
            onImport={() => setIsImportModalVisible(true)}
            dataSource={templates}
            loading={templatesLoading}
            pagination={templatePagination}
            onTableChange={handleTemplateTableChange}
            onEdit={handleEditTemplate}
            onDelete={handleDeleteTemplate}
          />
        </div>
        <div style={{ display: activeTab === 'myCategories' ? 'block' : 'none' }}>
          <MyCategories
            onNewCategory={() => {
              setEditingCategory(null);
              setIsCategoryModalVisible(true);
            }}
            onEditCategory={handleEditCategory}
            onDeleteCategory={handleDeleteCategory}
          />
        </div>
        <div style={{ display: activeTab === 'teamTemplates' ? 'block' : 'none' }}>
          <GroupTemplates
            dataSource={groupTemplates}
            loading={groupTemplatesLoading}
            pagination={groupTemplatePagination}
            onImport={() => setIsImportModalVisible(true)}
            onTableChange={handleGroupTemplateTableChange}
            onEdit={handleEditGroupTemplate}
            onNewTemplate={() => setIsCreateModalVisible(true)}
            onDelete={handleDeleteGroupTemplate}
          />
        </div>
        <div style={{ display: activeTab === 'teamCategories' ? 'block' : 'none' }}>
          <GroupCategories
            onNewCategory={() => {
              setEditingCategory(null);
              setIsCategoryModalVisible(true);
            }}
            onEditCategory={handleEditCategory}
            onDeleteCategory={handleDeleteGroupCategory}
          />
        </div>

      </div>
      {/* 导入模板 */}
      <ImportModal
        visible={isImportModalVisible}
        isGroupTemplate={activeTab === 'teamTemplates'}
        onClose={() => setIsImportModalVisible(false)}
        onSuccess={() => {
          setIsImportModalVisible(false)
          if (activeTab === 'myTemplates') {
            dispatch({
              type: 'templateManagement/fetchTemplates',
              payload: { pagination: templatePagination },
            });
          } else if (activeTab === 'teamTemplates') {
            dispatch({
              type: 'templateManagement/fetchGroupTemplates',
              payload: { pagination: groupTemplatePagination },
            });
          }
        }}
      />
      <CreateTemplate
        visible={isCreateModalVisible}
        onClose={handleModalClose}
        onSuccess={handleCreateSuccess}
        editingTemplate={editingTemplate}
        editingGroupTemplate={editingGroupTemplate}
        isGroupTemplate={activeTab === 'teamTemplates'}
        readOnly={editingReadOnly}
      />
      <CreateCategoryModal
        visible={isCategoryModalVisible}
        onClose={handleCategoryModalClose}
        onSuccess={handleCategoryCreateSuccess}
        editingCategory={editingCategory}
        isGroupCategory={activeTab === 'teamCategories'}
      />
    </ConfigProvider>
  );
};

const mapStateToProps = ({ templateManagement }: { templateManagement: TemplateManagementState }) => ({
  templateManagement,
});

export default connect(mapStateToProps)(TemplateManagement);
