/**
 * 敏感信息处理工具
 */

/**
 * 将文本替换为星号
 * @param text 原始文本
 * @returns 替换后的文本（全部星号）
 */
export const maskSensitiveText = (text: string | undefined | null): string => {
  if (!text || typeof text !== 'string') {
    return '';
  }

  // 将所有字符替换为星号
  return '*'.repeat(text.length);
};

/**
 * 根据是否隐藏敏感信息来处理文本
 * @param text 原始文本
 * @param isHidden 是否隐藏敏感信息
 * @returns 处理后的文本
 */
export const processSensitiveText = (text: string | undefined | null, isHidden: boolean): string => {
  if (!isHidden) {
    return text || '';
  }

  return maskSensitiveText(text);
};

/**
 * 检查字段是否为敏感信息字段
 * @param fieldName 字段名称
 * @returns 是否为敏感信息字段
 */
export const isSensitiveField = (fieldName: string): boolean => {
  const sensitiveFields = ['患者姓名', '患者编号', '患者性别', '患者年龄', '检查号', '姓名', '性别', '年龄'];

  return sensitiveFields.includes(fieldName);
};
