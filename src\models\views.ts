import {
  FetchInstances,
  FetchSeries,
  FetchStudies,
  FetchInstanceAllTag,
  saveScreenshot,
  listImages,
  getImage,
  deleteScreenshot,
} from '@/services/viwer';
import { Effect, Reducer } from 'umi';
import type { CornerInfoConfig } from '@/pages/viewer/components/LeftToolBar/CornerInfoModal';
import { defaultConfig } from '../pages/viewer/components/LeftToolBar/CornerInfoModal';
import { message } from 'antd'; // Added message import

export interface ViewType {
  patientId?: any;
  studyId?: any; // 新增字段，用于保存 sIdDetail
  curSeriesId?: any;
  seriesId?: any;
  studyLists?: any[];
  seriesLists?: any[];
  displayedSeriesList?: any[]; // 专门用于显示区域的序列列表，不影响右侧表格
  ctSeriesLists?: any[];
  ptSeriesLists?: any[];
  instanceLists?: any[];
  instanceListsCT?: any[];
  instanceListsPT?: any[];
  curInstances?: Map<string, any[]>;
  selectedColorMap?: any;
  selectedCt?: any;
  selectedPt?: any;
  InstanceTag?: any[];
  resetViewport: any;
  windowWidth: any;
  windowCenter: any;
  seriesDescriptions: Map<string, string>;
  isFusionMode?: boolean;
  annotationDeletedTrigger?: boolean;
  selectedPtTags?: {
    RadiopharmaceuticalStartTime?: string;
    RadionuclideTotalDose?: number;
    RadionuclideHalfLife?: number;
    PatientWeight?: number;
  };
  row?: any;
  col?: any;
  playSpeed: number; // 播放速度（毫秒）
  isPlaying: boolean; // 是否正在播放
  currentFrameIndex: any; // 当前帧索引
  selectedSeriesForPlay: any; // 当前选中用于播放的 seriesId
  screenshots?: string[]; // 截图列表
  screenshotInfos?: {
    // 截图信息
    url: string;
    timestamp: string; // 截图时间
    size?: string; // 文件大小
    width?: number; // 宽度
    height?: number; // 高度
    imageName?: string; // 图片名称，用于后端删除操作
  }[];
  isCapturingScreenshot?: boolean; // 是否正在截图
  screenshotUpdateTime?: number; // 截图更新时间戳
  isDeletingScreenshots?: boolean; // 是否正在删除截图
  cornerInfoConfig?: CornerInfoConfig; // 四角信息配置
  directionIndicatorVisible?: boolean; // 方向指示器是否可见
  sensitiveInfoHidden?: boolean; // 敏感信息是否隐藏
  selectedSeriesId?: string; // 当前选中的序列ID，用于右侧面板显示和切换
  currentTool?: string; // 当前选中的工具
  mipReconstructionParams?: {
    method: 'MinIP' | 'MIP' | 'Mean' | 'Sum';
    axial: number;
    coronal: number;
    sagittal: number;
    enabled: boolean;
  }; // MIP重建参数
}

// model内容：

export interface ModelType {
  state: ViewType;
  effects: {
    fetchStudies: Effect;
    fetchSeries: Effect;
    fetchInstances: Effect;
    fetchInstancesCT: Effect;
    fetchInstancesPT: Effect;
    FetchInstanceAllTag: Effect; // 新增的 FetchInstanceALLTag Effect
    FetchSeriesDescription: Effect;
    FetchPtSeriesTags: Effect;
    togglePlay: Effect;
    setPlaySpeed: Effect;
    nextFrame: Effect;
    captureScreenshot: Effect; // 截图功能
    deleteScreenshot: Effect;
    deleteAllScreenshots: Effect;
    batchDeleteScreenshots: Effect;
    updateCornerInfoConfig: Effect; // 更新四角信息配置
    selectSeries: Effect; // 选择序列
    updateMipReconstructionParams: Effect; // 更新MIP重建参数
    fetchHistoryScreenshots: Effect; // 获取历史截图
  };
  reducers: {
    save: Reducer<ViewType>;
  };
}

const Model: ModelType = {
  state: {
    patientId: undefined,
    studyId: undefined,
    curSeriesId: undefined,
    seriesId: undefined,
    studyLists: [],
    seriesLists: undefined,
    displayedSeriesList: undefined, // 专门用于显示区域的序列列表
    ctSeriesLists: [],
    ptSeriesLists: [],
    instanceLists: [],
    instanceListsCT: [],
    instanceListsPT: [],
    curInstances: new Map(),
    selectedColorMap: undefined,
    selectedCt: undefined,
    selectedPt: undefined,
    InstanceTag: [],
    resetViewport: undefined,
    windowWidth: undefined, // 设置默认窗宽
    windowCenter: undefined, // 设置默认窗位
    seriesDescriptions: new Map(),
    isFusionMode: undefined,
    annotationDeletedTrigger: undefined,
    selectedPtTags: undefined,
    row: 1, // 默认2x2布局，设置为1表示2行
    col: 1, // 默认2x2布局，设置为1表示2列
    playSpeed: 1000 / 30, // 默认 30FPS
    isPlaying: false,
    currentFrameIndex: 0,
    selectedSeriesForPlay: undefined,
    screenshots: [],
    screenshotInfos: [],
    isCapturingScreenshot: false,
    screenshotUpdateTime: undefined,
    cornerInfoConfig: defaultConfig, // 使用导入的默认配置，确保四角信息默认启用
    directionIndicatorVisible: true, // 方向指示器默认显示
    sensitiveInfoHidden: false, // 敏感信息默认不隐藏
    selectedSeriesId: undefined, // 当前选中的序列ID，默认未选中
    currentTool: 'Crosshairs', // 当前选中的工具，默认为十字线
    mipReconstructionParams: {
      method: 'MIP',
      axial: 10,
      coronal: 0,
      sagittal: 0,
      enabled: false,
    }, // MIP重建参数默认值
  },
  effects: {
    *fetchStudies({ payload }, { call, put }): any {
      const studies = yield call(FetchStudies, payload);
    },
    *fetchSeries({ payload }, { call, put }): any {
      //根据studyId获取study的series
      const curStudyId = payload.StudyId;

      // 先清除旧数据
      yield put({
        type: 'save',
        payload: {
          seriesLists: [],
          ctSeriesLists: [],
          ptSeriesLists: [],
          instanceLists: [],
          curInstances: new Map(),
        },
      });

      const series = yield call(FetchSeries, curStudyId);

      // 添加安全检查，确保 series.data 和 series.data.List 存在
      if (!series || !series.data || !series.data.List) {
        console.error('FetchSeries 返回的数据格式不正确:', series);
        return;
      }

      const ctSeriesData = series.data.List.filter((s: any) => s.Modality === 'CT');
      const ptSeriesData = series.data.List.filter((s: any) => s.Modality === 'PT');

      if (series) {
        yield put({
          type: 'save',
          payload: {
            studyId: curStudyId,
            seriesLists: series.data.List,
            ctSeriesLists: ctSeriesData,
            ptSeriesLists: ptSeriesData,
            // 不再自动设置默认选中的序列，让viewer组件根据URL参数决定
          },
        });
      }
    },
    *fetchInstances({ payload }, { call, put, select }): any {
      // 请求当前series的instances，并设置curSeriesId
      const curSeriesId = String(payload);

      const instances = yield call(FetchInstances, { SeriesId: curSeriesId });

      // 添加安全检查，确保 instances.data 和 instances.data.List 存在
      if (!instances || !instances.data || !instances.data.List) {
        console.error('FetchInstances 返回的数据格式不正确:', instances);
        return;
      }

      if (instances) {
        // 获取当前的
        const currentState = yield select((state: any) => state.views);
        // 确保 curInstances 是 Map 对象
        const curInstances =
          currentState.curInstances && typeof currentState.curInstances.get === 'function'
            ? currentState.curInstances
            : new Map();

        // 设置新的 seriesId -> instances 映射
        const newCurInstances = new Map(curInstances);
        newCurInstances.set(String(curSeriesId), instances.data.List);

        yield put({
          type: 'save',
          payload: {
            curSeriesId,
            instanceLists: instances.data.List,
            curInstances: newCurInstances,
          },
        });
      }
    },
    *fetchInstancesCT({ payload }, { call, put, select }): any {
      // 请求当前series的instances，并设置curSeriesId
      //console.log('---curSeriesId---', payload);
      const curSeriesId = String(payload);
      const instances = yield call(FetchInstances, { SeriesId: curSeriesId });
      // console.log('---测试多个fetchinstanceListsCT---');
      // console.log('instanceListsCT', instances.data);

      // 添加安全检查
      if (!instances || !instances.data || !instances.data.List) {
        console.error('FetchInstances (CT) 返回的数据格式不正确:', instances);
        return;
      }

      if (instances) {
        yield put({
          type: 'save',
          payload: {
            instanceListsCT: instances.data.List,
          },
        });
      }
    },
    *fetchInstancesPT({ payload }, { call, put, select }): any {
      // 请求当前series的instances，并设置curSeriesId
      //console.log('---curSeriesId---', payload);
      const curSeriesId = String(payload);
      const instances = yield call(FetchInstances, { SeriesId: curSeriesId });
      // console.log('---测试多个fetchinstanceListsPT---');
      // console.log('instanceListsPT', instances.data);

      // 添加安全检查
      if (!instances || !instances.data || !instances.data.List) {
        console.error('FetchInstances (PT) 返回的数据格式不正确:', instances);
        return;
      }

      if (instances) {
        yield put({
          type: 'save',
          payload: {
            instanceListsPT: instances.data.List,
          },
        });
      }
    },
    *FetchInstanceAllTag({ payload }, { call, put }): any {
      // 使用从listInstances接口返回的List字段下的Id
      const Id = payload;
      const instanceAllTag = yield call(FetchInstanceAllTag, { Id });

      if (instanceAllTag && instanceAllTag.data) {
        yield put({
          type: 'save',
          payload: {
            InstanceTag: instanceAllTag.data.TagMap,
          },
        });
      }
    },
    *togglePlay({ payload }, { put, select }): any {
      const state = yield select((state: any) => state.views);
      yield put({
        type: 'save',
        payload: {
          isPlaying: !state.isPlaying,
        },
      });
    },
    *setPlaySpeed({ payload }, { put }) {
      yield put({
        type: 'save',
        payload: {
          playSpeed: 1000 / payload,
        },
      });
    },
    *nextFrame(_, { put, select }): any {
      const state = yield select((s: { views: ViewType }) => s.views);
      const newIndex = (state.currentFrameIndex + 1) % state.instanceLists.length;
      yield put({
        type: 'save',
        payload: {
          currentFrameIndex: newIndex,
        },
      });
    },
    *FetchSeriesDescription({ payload }, { call, put, select }): any {
      const { seriesId } = payload;

      // 获取该 series 的 instances
      const instances = yield call(FetchInstances, { SeriesId: seriesId });
      if (!instances || !instances.data.List.length) {
        return;
      }

      // 获取第一个 instance 的 Id
      const instanceId = instances.data.List[0].Id;

      // 使用 instanceId 获取所有 tag
      const instanceTags = yield call(FetchInstanceAllTag, { Id: Number(instanceId) });

      if (instanceTags && instanceTags.data.TagMap) {
        const seriesDescription = instanceTags.data.TagMap['0008,103e']?.Value?.[0];

        // 更新 seriesDescriptions
        const currentState = yield select((state: any) => state.views);
        const curSeriesDescriptions = currentState.seriesDescriptions || new Map();

        const newSeriesDescriptions = new Map(currentState.seriesDescriptions);
        newSeriesDescriptions.set(String(seriesId), seriesDescription);

        yield put({
          type: 'save',
          payload: {
            seriesDescriptions: newSeriesDescriptions,
          },
        });
      }
    },
    *FetchPtSeriesTags({ payload }, { call, put, select }): any {
      const seriesId = payload;
      // 获取该 series 的 instances
      const instances = yield call(FetchInstances, { SeriesId: seriesId });
      if (!instances || !instances.data.List.length) {
        return;
      }
      // 获取第一个 instance 的 Id
      const instanceId = instances.data.List[0].Id;
      // 获取所有 tag
      const instanceTags = yield call(FetchInstanceAllTag, { Id: Number(instanceId) });
      if (instanceTags && instanceTags.data?.TagMap) {
        const tagMap = instanceTags.data.TagMap;

        const ptTags = {
          AcquisitionTime: tagMap['(0008,0032)']?.Value?.[0] || '',
          RadiopharmaceuticalStartTime: tagMap['(0018,1072)']?.Value?.[0] || '',
          RadionuclideTotalDose: parseFloat(tagMap['(0018,1074)']?.Value?.[0] || '0'),
          RadionuclideHalfLife: parseFloat(tagMap['(0018,1075)']?.Value?.[0] || '0'),
          PatientWeight: parseFloat(tagMap['(0010,1030)']?.Value?.[0] || '0'),
        };

        yield put({
          type: 'save',
          payload: {
            selectedPtTags: ptTags,
          },
        });
      }
    },
    *captureScreenshot({ payload }, { call, put, select }): any {
      try {
        yield put({
          type: 'save',
          payload: {
            isCapturingScreenshot: true,
          },
        });
        const { imageBlob, imageUrl, timestamp, size, width, height } = payload;

        // 获取studyId，如果有的话
        const state = yield select((state: any) => state.views);
        const studyId = state.studyId;

        // 如果有studyId，则调用API保存截图
        let savedImageName = null;
        if (studyId && imageBlob) {
          try {
            const result = yield call(saveScreenshot, imageBlob, studyId);

            // 从API响应中获取保存的文件名
            if (result && result.Message) {
              savedImageName = result.Message;
            }

            message.success('截图已成功保存到服务器');
          } catch (error) {
            console.error('保存截图到服务器失败:', error);
            message.error('保存截图到服务器失败，但已保存到本地');
            // 即使服务器保存失败，我们仍然保留本地预览
          }
        } else {
          if (!studyId) message.warning('缺少检查ID，无法保存到服务器');
          if (!imageBlob) message.warning('截图数据无效，无法保存到服务器');
        }
        // 无论服务器保存是否成功，都更新本地截图库
        const screenshots = state.screenshots || [];
        const screenshotInfos = state.screenshotInfos || [];

        // 创建新的截图信息对象
        const newScreenshotInfo = {
          url: imageUrl,
          timestamp: timestamp || new Date().toLocaleString(),
          size: size || '未知',
          width: width || 0,
          height: height || 0,
          imageName: savedImageName, // 保存服务器返回的文件名
        };

        // 将新截图添加到列表并按时间倒序排序
        const updatedScreenshotInfos = [...screenshotInfos, newScreenshotInfo];
        const updatedScreenshots = [...screenshots, imageUrl];
        
        // 创建包含索引的数组以便排序后能够匹配对应的URL
        const sortedResults = updatedScreenshotInfos.map((info, index) => ({
          info,
          url: updatedScreenshots[index],
          timestamp: new Date(info.timestamp)
        }));
        
        // 按时间戳倒序排序
        sortedResults.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
        
        // 重新组织排序后的数据
        const sortedScreenshots = sortedResults.map(item => item.url);
        const sortedScreenshotInfos = sortedResults.map(item => item.info);

        yield put({
          type: 'save',
          payload: {
            screenshots: sortedScreenshots,
            screenshotInfos: sortedScreenshotInfos,
            isCapturingScreenshot: false,
            screenshotUpdateTime: Date.now(),
          },
        });
      } catch (error) {
        console.error('截图保存失败:', error);
        yield put({
          type: 'save',
          payload: {
            isCapturingScreenshot: false,
          },
        });
      }
    },
    *deleteScreenshot({ payload }, { put, select }): any {
      try {
        const { index } = payload;
        const state = yield select((state: any) => state.views);
        const screenshots = [...(state.screenshots || [])];
        if (index !== undefined && index >= 0 && index < screenshots.length) {
          // 释放URL对象
          if (screenshots[index]?.startsWith('blob:')) {
            try {
              URL.revokeObjectURL(screenshots[index]);
            } catch (e) {
              console.warn('无法释放URL对象:', e);
            }
          }

          // 移除指定索引的截图
          screenshots.splice(index, 1);

          // 批量更新状态，减少重渲染
          yield put({
            type: 'save',
            payload: {
              screenshots,
              // 添加一个时间戳，确保状态变化能被检测到
              screenshotUpdateTime: Date.now(),
            },
          });
        }
      } catch (error) {
        console.error('删除截图失败:', error);
      }
    },
    *fetchHistoryScreenshots({ payload }, { call, put }): any {
      try {
        const { studyId } = payload;
        if (!studyId) {
          console.error('获取历史截图失败: 缺少studyId');
          return;
        }

        // 调用API获取历史截图列表
        const response = yield call(listImages, { StudyId: studyId });

        console.log('获取历史截图列表结果:', response);

        // 检查响应格式，兼容不同的返回结构
        const imagePaths = response?.Paths || response?.Data?.Paths || [];
        console.log('图片路径列表:', imagePaths);

        if (imagePaths && imagePaths.length > 0) {
          const screenshotInfos = [];
          const screenshots = [];

          // 处理每个截图
          for (const imageName of imagePaths) {
            try {
              // 获取截图内容
              console.log('获取截图:', { StudyId: studyId, ImageName: imageName });
              const imageBlob = yield call(getImage, { StudyId: studyId, ImageName: imageName });

              if (imageBlob && imageBlob instanceof Blob) {
                const imageUrl = URL.createObjectURL(imageBlob);
                screenshots.push(imageUrl);

                // 从文件名中提取时间信息 (YYMMDDHHMMSS+序号)
                let timestamp = new Date().toLocaleString();
                try {
                  // 尝试从文件名中提取时间信息
                  if (imageName && imageName.length >= 12) {
                    const year = '20' + imageName.substring(0, 2);
                    const month = imageName.substring(2, 4);
                    const day = imageName.substring(4, 6);
                    const hour = imageName.substring(6, 8);
                    const minute = imageName.substring(8, 10);
                    const second = imageName.substring(10, 12);
                    
                    const dateObj = new Date(`${year}-${month}-${day}T${hour}:${minute}:${second}`);
                    if (!isNaN(dateObj.getTime())) {
                      timestamp = dateObj.toLocaleString();
                    }
                  }
                } catch (e) {
                  console.warn('解析截图文件名时间失败:', e);
                }

                // 创建截图信息
                screenshotInfos.push({
                  url: imageUrl,
                  timestamp: timestamp,
                  size: `${Math.round(imageBlob.size / 1024)} KB`,
                  width: 0, // 无法直接获取图片尺寸
                  height: 0,
                  imageName, // 保存图片名称以便后续操作
                });
              }
            } catch (err) {
              console.error('获取截图内容失败:', err);
            }
          }

          // 按照截图时间倒序排列
          const sortedResults = screenshotInfos.map((info, index) => ({
            info,
            url: screenshots[index],
            timestamp: new Date(info.timestamp)
          }));
          
          // 按时间戳倒序排序
          sortedResults.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
          
          // 重新组织排序后的数据
          const sortedScreenshots = sortedResults.map(item => item.url);
          const sortedScreenshotInfos = sortedResults.map(item => item.info);

          yield put({
            type: 'save',
            payload: {
              screenshots: sortedScreenshots,
              screenshotInfos: sortedScreenshotInfos,
              screenshotUpdateTime: Date.now(),
            },
          });

          // 不显示加载历史截图的提示信息
          // message.success(`成功加载 ${screenshots.length} 张历史截图`);
        }
      } catch (error) {
        console.error('获取历史截图失败:', error);
        message.error('获取历史截图失败');
      }
    },

    // 批量删除截图（新增）
    *batchDeleteScreenshots({ payload }, { put, select, call }): any {
      try {
        const { indices } = payload;
        if (!indices || !indices.length) return;

        // 设置删除状态为true
        yield put({
          type: 'save',
          payload: {
            isDeletingScreenshots: true,
          },
        });

        const state = yield select((state: any) => state.views);
        const screenshots = [...(state.screenshots || [])];
        const screenshotInfos = [...(state.screenshotInfos || [])];

        // 从大到小排序索引，以避免删除时索引变化
        const sortedIndices = [...indices].sort((a, b) => b - a);

        // 收集要删除的图片名称和对应的索引
        const itemsToDelete: { imageName: string; index: number }[] = [];

        // 收集要删除的图片信息
        sortedIndices.forEach((index) => {
          if (index >= 0 && index < screenshots.length) {
            const screenshotInfo = screenshotInfos[index];
            if (screenshotInfo?.imageName) {
              itemsToDelete.push({
                imageName: screenshotInfo.imageName,
                index: index,
              });
            }
          }
        });

        // 先尝试删除后端数据
        const failedDeletions: string[] = [];
        for (const item of itemsToDelete) {
          try {
            yield call(deleteScreenshot, { Name: item.imageName });
            console.log(`成功删除截图: ${item.imageName}`);
          } catch (error) {
            console.error(`删除截图失败: ${item.imageName}`, error);
            failedDeletions.push(item.imageName);
          }
        }

        // 只有所有删除都成功时，才更新前端状态
        if (failedDeletions.length === 0) {
          // 释放所有要删除的URL对象
          sortedIndices.forEach((index) => {
            if (index >= 0 && index < screenshots.length && screenshots[index]?.startsWith('blob:')) {
              try {
                URL.revokeObjectURL(screenshots[index]);
              } catch (e) {
                console.warn('无法释放URL对象:', e);
              }
            }
          });

          // 移除所有指定索引的截图
          sortedIndices.forEach((index) => {
            if (index >= 0 && index < screenshots.length) {
              screenshots.splice(index, 1);
            }
            if (index >= 0 && index < screenshotInfos.length) {
              screenshotInfos.splice(index, 1);
            }
          });

          // 一次性更新状态
          yield put({
            type: 'save',
            payload: {
              screenshots,
              screenshotInfos,
              screenshotUpdateTime: Date.now(),
              isDeletingScreenshots: false,
            },
          });

          // 确保在删除成功后也设置sessionStorage标记
          const state = yield select((state: any) => state.views);
          if (state.studyId) {
            try {
              // 使用window.sessionStorage确保在model中也能访问
              window.sessionStorage.setItem(`deleted_screenshots_${state.studyId}`, 'true');
            } catch (e) {
              console.warn('无法设置sessionStorage:', e);
            }
          }

          message.success(`成功删除 ${sortedIndices.length} 张截图`);
        } else {
          // 如果有删除失败的，显示错误信息
          message.error(`删除失败: ${failedDeletions.join(', ')}`);
          console.error('部分截图删除失败，前端状态未更新');

          // 删除失败时，不重新加载截图列表，而是直接更新前端状态
          // 移除所有指定索引的截图（即使后端删除失败，前端也应该移除）
          sortedIndices.forEach((index) => {
            if (index >= 0 && index < screenshots.length) {
              screenshots.splice(index, 1);
            }
            if (index >= 0 && index < screenshotInfos.length) {
              screenshotInfos.splice(index, 1);
            }
          });

          // 一次性更新状态
          yield put({
            type: 'save',
            payload: {
              screenshots,
              screenshotInfos,
              screenshotUpdateTime: Date.now(),
              isDeletingScreenshots: false,
            },
          });
          
          // 记录日志，但不重新加载截图列表
          console.warn('前端已移除截图，但后端删除失败，可能导致数据不一致')

          // 确保在删除失败后也设置sessionStorage标记
          const state = yield select((state: any) => state.views);
          if (state.studyId) {
            try {
              // 使用window.sessionStorage确保在model中也能访问
              window.sessionStorage.setItem(`deleted_screenshots_${state.studyId}`, 'true');
            } catch (e) {
              console.warn('无法设置sessionStorage:', e);
            }
          }

          // 设置删除状态为false
          yield put({
            type: 'save',
            payload: {
              isDeletingScreenshots: false,
            },
          });
        }
      } catch (error) {
        console.error('批量删除截图失败:', error);
        message.error('删除截图时发生错误');

        // 即使在全局错误处理中也设置sessionStorage标记
        const state = yield select((state: any) => state.views);
        if (state.studyId) {
          try {
            // 使用window.sessionStorage确保在model中也能访问
            window.sessionStorage.setItem(`deleted_screenshots_${state.studyId}`, 'true');
          } catch (e) {
            console.warn('无法设置sessionStorage:', e);
          }
        }

        // 确保删除状态被重置
        yield put({
          type: 'save',
          payload: {
            isDeletingScreenshots: false,
          },
        });
      }
    },
    *deleteAllScreenshots(_, { put, select }): any {
      try {
        const state = yield select((state: any) => state.views);
        const screenshots = state.screenshots || [];
        // 释放所有URL对象
        screenshots.forEach((url: string) => {
          if (url?.startsWith('blob:')) {
            try {
              URL.revokeObjectURL(url);
            } catch (e) {
              console.warn('无法释放URL对象:', e);
            }
          }
        });
        yield put({
          type: 'save',
          payload: {
            screenshots: [],
            // 添加一个时间戳，确保状态变化能被检测到
            screenshotUpdateTime: Date.now(),
          },
        });
      } catch (error) {
        console.error('删除所有截图失败:', error);
      }
    },
    *updateCornerInfoConfig({ payload }, { put }): any {
      try {
        yield put({
          type: 'save',
          payload: {
            cornerInfoConfig: payload.config,
          },
        });
      } catch (error) {
        console.error('更新四角信息配置失败:', error);
      }
    },
    *selectSeries({ payload }, { put, call }): any {
      try {
        const { seriesId } = payload;

        // 更新选中的序列ID
        yield put({
          type: 'save',
          payload: { selectedSeriesId: seriesId },
        });
        // 获取选中序列的instances
        yield put({
          type: 'fetchInstances',
          payload: seriesId,
        });
        // 获取选中序列的第一个instance的标签数据
        const instances = yield call(FetchInstances, { SeriesId: seriesId });
        if (instances && instances.data && instances.data.List && instances.data.List.length > 0) {
          const firstInstanceId = instances.data.List[0].Id;
          yield put({
            type: 'FetchInstanceAllTag',
            payload: firstInstanceId,
          });
        }
        console.log('views/selectSeries: 序列选择完成');
      } catch (error) {
        console.error('选择序列失败:', error);
      }
    },
    *updateMipReconstructionParams({ payload }, { put }): any {
      try {
        console.log('views/updateMipReconstructionParams: 更新MIP重建参数', payload);
        yield put({
          type: 'save',
          payload: {
            mipReconstructionParams: payload.params,
          },
        });
        console.log('views/updateMipReconstructionParams: 参数更新成功');
      } catch (error) {
        console.error('更新MIP重建参数失败:', error);
      }
    },
  },
  reducers: {
    save(state, { payload }) {
      return { ...state, ...payload };
    },
  },
};

export default Model;
