.title {
  background: rgb(242, 210, 121);
}

.description {
  //样式穿透
  :global {
    // scrollbar - 使用全局样式
    .ant-descriptions-view {
      height: 11vh;
      overflow-y: auto !important;
      &::-webkit-scrollbar {
        height: 0px;
        width: 10px;
        overflow-y: auto;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 5px;
        background: #595959;
      }
      &::-webkit-scrollbar-track {
        -webkit-box-shadow: 0;
        border-radius: 5px;
        background: #7f7f7f;
      }
    }
    // label
    .ant-descriptions-item-label {
      color: #f0f0f0;
      font-family: 'Microsoft YaHei UI' !important;
      font-weight: 400 !important;
      font-size: 16px !important;
      line-height: 20px !important;
      padding: 4px 8px !important;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    // content
    .ant-descriptions-item-content {
      color: white;
      font-family: 'Microsoft YaHei UI' !important;
      font-weight: 400 !important;
      font-size: 16px !important;
      line-height: 20px !important;
      padding: 4px 8px !important;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    // item
    .ant-descriptions-item {
      padding-bottom: 6px !important;
    }
    // row
    .ant-descriptions-row {
      margin-bottom: 0 !important;
    }
  }
}

.list {
  //样式穿透
  :global {
    // scrollbar - 使用全局样式
    .ant-list-items {
      height: 60vh;
      overflow-y: auto !important;
      // &::-webkit-scrollbar {
      //   height: 0px;
      //   width: 10px;
      //   overflow-y: auto;
      // }
      // &::-webkit-scrollbar-thumb {
      //   border-radius: 5px;
      //   background: #595959;
      // }
      // &::-webkit-scrollbar-track {
      //   -webkit-box-shadow: 0;
      //   border-radius: 5px;
      //   background: #7f7f7f;
      // }
    }
  }
}

.quill {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  //样式穿透
  :global {
    .ql-container {
      border: none !important;
      border-radius: 0;
      flex: 1;
      display: flex;
      flex-direction: column;
    }
    
    .ql-editor {
      background-color: white;
      color: black;
      border: none !important;
      border-radius: 0;
      flex: 1;
      min-height: 0;
      // scrollbar - 使用全局样式
      overflow-y: auto !important;
      // &::-webkit-scrollbar {
      //   height: 0px;
      //   width: 10px;
      //   overflow-y: auto;
      // }
      // &::-webkit-scrollbar-thumb {
      //   border-radius: 5px;
      //   background: #595959;
      // }
      // &::-webkit-scrollbar-track {
      //   -webkit-box-shadow: 0;
      //   border-radius: 5px;
      //   background: #7f7f7f;
      // }
    }
    
    .ql-toolbar {
      background-color: #EDEDED !important;
      color: black;
      border: none !important;
      border-bottom: 1px solid #d9d9d9 !important;
      border-radius: 0;
      flex-shrink: 0;
    }
    
    // 确保整个编辑器的背景和边框一致
    .ql-snow {
      border: none !important;
    }

    // 自定义按钮样式 - 使用自定义图标
    .ql-undo:before {
      content: "";
      background-image: url('/icon/report/recover.png');
      background-size: 16px 16px;
      background-repeat: no-repeat;
      background-position: center;
      display: inline-block;
      width: 16px;
      height: 16px;
    }
    
    // 隐藏默认的SVG图标
    .ql-undo svg,
    .ql-redo svg,
    .ql-clean svg,
    .ql-format-painter svg,
    .ql-image svg,
    .ql-symbols svg {
      display: none !important;
    }
    
    .ql-redo:before {
      content: "";
      background-image: url('/icon/report/recover.png');
      background-size: 16px 16px;
      background-repeat: no-repeat;
      background-position: center;
      display: inline-block;
      width: 16px;
      height: 16px;
      transform: scaleX(-1); // 水平翻转来表示重做
    }
    
    .ql-clean:before {
      content: "";
      background-image: url('/icon/report/quash.png');
      background-size: 16px 16px;
      background-repeat: no-repeat;
      background-position: center;
      display: inline-block;
      width: 16px;
      height: 16px;
    }
    
    .ql-format-painter:before {
      content: "";
      background-image: url('/icon/report/format_painter.png');
      background-size: 16px 16px;
      background-repeat: no-repeat;
      background-position: center;
      display: inline-block;
      width: 16px;
      height: 16px;
    }
    
    .ql-header:before {
      content: "T";
      font-size: 16px;
      font-weight: bold;
      color: inherit;
      display: inline-block;
      width: 16px;
      height: 16px;
      text-align: center;
      line-height: 16px;
    }
    
    .ql-header-toggle:before {
      content: "T";
      font-size: 16px;
      font-weight: bold;
      color: inherit;
      display: inline-block;
      width: 16px;
      height: 16px;
      text-align: center;
      line-height: 16px;
    }
    
    .ql-size-toggle:before {
      content: "";
      background-image: url('/icon/report/font_size.png');
      background-size: 16px 16px;
      background-repeat: no-repeat;
      background-position: center;
      display: inline-block;
      width: 16px;
      height: 16px;
    }
    
    .ql-image:before {
      content: "";
      background-image: url('/icon/report/image.png');
      background-size: 16px 16px;
      background-repeat: no-repeat;
      background-position: center;
      display: inline-block;
      width: 16px;
      height: 16px;
    }
    
    .ql-symbols:before {
      content: "";
      background-image: url('/icon/report/symbol.png');
      background-size: 16px 16px;
      background-repeat: no-repeat;
      background-position: center;
      display: inline-block;
      width: 16px;
      height: 16px;
    }

    // 撤销和恢复按钮移除视觉变化效果
    .ql-toolbar button.ql-undo:hover,
    .ql-toolbar button.ql-redo:hover {
      color: inherit !important;
      background-color: transparent !important;
    }
    
    .ql-toolbar button.ql-undo.ql-active,
    .ql-toolbar button.ql-redo.ql-active {
      color: inherit !important;
      background-color: transparent !important;
    }
    
    .ql-toolbar button.ql-undo:focus,
    .ql-toolbar button.ql-redo:focus {
      outline: none !important;
      color: inherit !important;
      background-color: transparent !important;
    }

    // 工具栏按钮通用样式
    .ql-toolbar .ql-formats {
      margin-right: 0px;
    }
    
    .ql-toolbar button {
      padding: 5px;
      margin: 2px 11.25px;
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
    }
    
    // 第一个按钮左边距为0
    .ql-toolbar .ql-formats:first-child button:first-child {
      margin-left: 0;
    }
    
    // 最后一个按钮右边距为0
    .ql-toolbar .ql-formats:last-child button:last-child {
      margin-right: 0;
    }
    
    .ql-toolbar button:hover {
      color: #06c;
    }
    
    .ql-toolbar button.ql-active {
      color: #06c;
      background-color: #e6f7ff;
    }

    // 确保所有自定义图标按钮有正确的尺寸
    .ql-toolbar button {
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
    }

    // 为自定义图标按钮设置统一的hover效果
    .ql-toolbar button:hover::before {
      opacity: 0.7;
    }

    .ql-toolbar button.ql-active::before {
      opacity: 1;
      filter: brightness(1.2);
    }

    // 确保自定义图标按钮覆盖默认样式
    .ql-toolbar button.ql-undo,
    .ql-toolbar button.ql-redo,
    .ql-toolbar button.ql-clean,
    .ql-toolbar button.ql-format-painter,
    .ql-toolbar button.ql-image,
    .ql-toolbar button.ql-symbols,
    .ql-toolbar button.ql-header-toggle,
    .ql-toolbar button.ql-size-toggle {
      background: none !important;
      border: none !important;
      overflow: hidden;
    }

    // 确保before伪元素正确定位
    .ql-toolbar button.ql-undo:before,
    .ql-toolbar button.ql-redo:before,
    .ql-toolbar button.ql-clean:before,
    .ql-toolbar button.ql-format-painter:before,
    .ql-toolbar button.ql-image:before,
    .ql-toolbar button.ql-symbols:before,
    .ql-toolbar button.ql-header-toggle:before,
    .ql-toolbar button.ql-size-toggle:before {
      position: relative;
      z-index: 1;
    }
  }
}

// quill字体样式
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='SimSun']::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='SimSun']::before {
  font-family: 'SimSun' !important;
  content: '宋体';
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='SimHei']::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='SimHei']::before {
  font-family: 'SimHei';
  content: '黑体';
}

.ql-snow
  .ql-picker.ql-font
  .ql-picker-label[data-value='Microsoft-YaHei']::before,
.ql-snow
  .ql-picker.ql-font
  .ql-picker-item[data-value='Microsoft-YaHei']::before {
  font-family: '微软雅黑';
  content: '微软雅黑';
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='KaiTi']::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='KaiTi']::before {
  font-family: 'KaiTi' !important;
  content: '楷体';
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='FangSong']::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='FangSong']::before {
  font-family: 'FangSong';
  content: '仿宋';
}

/*  设置每个字体的css字体样式 */
.ql-font-SimSun {
  font-family: 'SimSun';
}

.ql-font-SimHei {
  font-family: 'SimHei';
}

.ql-font-Microsoft-YaHei {
  font-family: '微软雅黑';
}

.ql-font-KaiTi {
  font-family: 'KaiTi';
}

.ql-font-FangSong {
  font-family: 'FangSong';
}

// quill字体大小样式
.ql-snow .ql-picker.ql-size {
  width: 70px; // 菜单栏占比宽度
}
// .ql-snow .ql-picker.ql-size .ql-picker-label[data-value='10px']::before,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value='10px']::before {
//   content: '10px';
// }
// .ql-snow .ql-picker.ql-size .ql-picker-label[data-value='12px']::before,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value='12px']::before {
//   content: '12px';
// }
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='14px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='14px']::before {
  content: '14px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='16px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='16px']::before {
  content: '16px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='20px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='20px']::before {
  content: '20px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='24px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='24px']::before {
  content: '24px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='36px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='36px']::before {
  content: '36px';
}

// Collapse展开状态的自定义样式
.collapse {
  :global {
    // 展开状态的标题背景
    .ant-collapse-item-active .ant-collapse-header {
      background-color: #262628 !important;
    }
    
    // 展开状态的内容背景
    .ant-collapse-item-active .ant-collapse-content {
      background-color: #262628 !important;
    }
    
    // 展开状态的内容内部背景
    .ant-collapse-item-active .ant-collapse-content-box {
      background-color: #262628 !important;
    }

    // ADDED: Remove horizontal padding from collapse content box to prevent scrollbar
    // when inner content (Space) has a fixed width and auto margins.
    .ant-collapse-content > .ant-collapse-content-box {
      padding-left: 0px !important;
      padding-right: 0px !important;
    }

    // 截图库标题按钮左右下角不要圆角
    .ant-collapse-item:last-child .ant-collapse-header {
      border-bottom-left-radius: 0 !important;
      border-bottom-right-radius: 0 !important;
    }

    // 或者使用更通用的方式，如果截图库总是最后一个
    .ant-collapse > .ant-collapse-item:last-child .ant-collapse-header {
      border-bottom-left-radius: 0 !important;
      border-bottom-right-radius: 0 !important;
    }

    // 报告模板和截图库中Space内容的背景色
    .ant-collapse-content-box .ant-space {
      background-color: #191919 !important;
      padding: 16px !important;
      border-radius: 4px !important;
      width: calc(100% - 2px) !important;
      height: 610px !important;
      box-sizing: border-box !important;
      margin: 0 auto !important;
    }

    // 针对特定的ant-space类名设置宽度
    .ant-space.ant-space-vertical.ant-space-gap-row-middle.ant-space-gap-col-middle {
      width: 260px !important;
      margin: 0 auto !important;
      display: block !important;
    }

    // 确保Space内部的元素背景透明或继承
    .ant-collapse-content-box .ant-space .ant-space-item {
      background-color: transparent !important;
    }
  }
}


// 审核报告
.check {

  height: 100%;
  padding: 15px;
  border-radius: 10px;
  background-color: #262628;
  &_name {
    margin-bottom: 50px;
    font-size: 18px;
  }
  &_flow {
    flex: 1;
    overflow-y: scroll;
    div:first-child {
      .checkStepIcon .circle {
        background-color: #1F69B4!important;
      }
      .checkStepIcon .line {
        background-color: #1F69B4!important;
      }
    }
    div:last-child {
      .checkStepIcon .line {
        display: none!important;
      }
    }
  }
  &_action {
    margin-top: 20px;
  }
}
.step {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #1F69B4;
}
.checkStepIcon {
  margin-right: 8px;
  width: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  .circle {
    margin: 6px auto;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #AAA9A9;
  }
  .line {
    flex: 1;
    height: auto;
    width: 1px;
    background-color: #AAA9A9;
  }
}
.checkStep {
  justify-content: space-between;
  margin-bottom: 80px;
  border-radius: 10px;
  flex: 1;
  padding: 8px;
  background-color: #333233;
 &_name {
   font-size: 10px;
   color: #AAA9A9;
 }
 &_time {
  font-size: 10px;
  color: #AAA9A9;
 }
 &_reason {
  margin-top: 6px;
  font-size: 12px;
  color: #AAA9A9;
  word-break: break-all;
 }
}
.ant-steps.ant-steps-vertical.ant-steps-dot .ant-steps-item-process .ant-steps-item-icon {
  inset-inline-start: 0px;
}

