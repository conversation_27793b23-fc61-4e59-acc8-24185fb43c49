import React, { useState, SetStateAction, useEffect } from 'react';
import { Row, Col, InputNumber } from 'antd';
import { connect, type Dispatch } from 'umi';
import styles from './index.less';
import { ViewType } from '@/models/views';

interface GridSquareProps {
  posIndex: { row: number; col: number };
  hovered: boolean;
  isLocked: boolean;
  setIsLocked: Dispatch<SetStateAction<boolean>>;
  onChange: (row: number, col: number) => void;
}

const GridSquare: React.FC<GridSquareProps> = (props) => {
  const { posIndex, hovered, isLocked, setIsLocked, onChange } = props;
  const backgroundColor = hovered ? 'lightblue' : 'rgba(51, 50, 51, 1) ';

  return (
    <div
      className={styles.layoutGridDiv}
      style={{ backgroundColor }}
      onMouseEnter={() => {
        if (!isLocked) {
          onChange(posIndex.row, posIndex.col);
        }
      }}
      onClick={() => {
        if (!isLocked) {
          setIsLocked(true);
        }
        onChange(posIndex.row, posIndex.col);
      }}
    />
  );
};

interface ICustomGridProps {
  value: { row: number; col: number } | null;
  onChange: (row: number, col: number) => void;
  dispatch: Dispatch;
}

const GridSelect: React.FC<ICustomGridProps> = ({ value, onChange, dispatch }) => {
  const [isLocked, setIsLocked] = useState<boolean>(false);

  useEffect(() => {
    if (!value) {
      setIsLocked(false);
    }
  }, [value]);

  useEffect(() => {
    //console.log('GridSelect received new value:', value);
  }, [value]);

  const isHovered = (row: number, col: number): boolean => {
    if (!value) return false;
    return row <= value.row && col <= value.col;
  };

  return (
    <>
      <div className={styles.layoutGrid}>
        {[...Array(3)].map((_, rowIndex) => (
          <Row key={rowIndex}>
            {[...Array(3)].map((_, colIndex) => (
              <GridSquare
                isLocked={isLocked}
                setIsLocked={setIsLocked}
                key={`${rowIndex}-${colIndex}`}
                posIndex={{ row: rowIndex, col: colIndex }}
                hovered={isHovered(rowIndex, colIndex)}
                onChange={onChange}
              />
            ))}
          </Row>
        ))}
      </div>
      <Row>
        <Col span={12} className={styles.numberContainer}>
          行数:
          <InputNumber
            max={3}
            min={1}
            value={Number(value?.row) + 1 || null}
            className={styles.GridInputNumber}
            onChange={(newValue: number | null) => newValue && value && onChange(newValue, value.col)}
          />
        </Col>
        <Col span={12} className={styles.numberContainer}>
          列数:
          <InputNumber
            max={3}
            min={1}
            value={Number(value?.col) + 1 || null}
            className={styles.GridInputNumber}
            onChange={(newValue: number | null) => newValue && value && onChange(value.row, newValue)}
          />
        </Col>
      </Row>
    </>
  );
};

const mapStateToProps = ({ views }: { views: ViewType }) => {
  return {
    // 阅片model存储的信息
  };
};

export default connect(mapStateToProps)(GridSelect);
