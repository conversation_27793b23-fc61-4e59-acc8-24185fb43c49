import React, { useState } from 'react';
import {
  message,
  Upload,
  Button,
  Space,
  Progress,
  Modal,
  ConfigProvider,
} from 'antd';
import type { UploadFile, UploadProps } from 'antd';
import { InboxOutlined, CloseOutlined } from '@ant-design/icons';
import type { RcFile } from 'antd/es/upload/interface';
import axios from 'axios';
import storeUtil from '@/utils/store';
import styles from './index.less';

const { Dragger } = Upload;

interface UploadModal {
  open: boolean;
  setOpen: (val: boolean, type?: 'close' | 'success') => void;
}

const UploadModal: React.FC<UploadModal> = ({ open, setOpen }) => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploaded, setUploaded] = useState(false);

  const handleUpload = () => {
    setUploading(true);

    // 遍历文件列表，对每个文件进行切片和hash计算
    fileList.forEach(async (file) => {
      console.log(file,'-------------------')
      let reader = new FileReader();
      // 当文件读取完成时的回调
      reader.onload = function(event) {
        const binaryData = event.target.result; // 这里得到的是 ArrayBuffer 类型

        // 接下来可以上传 binaryData 或 uint8Array
        try {
            axios
            .post(
              `${CLOUD_API}/public/uploadStudy`,
              binaryData, 
              {
                headers: {
                  'Content-Type': 'application/octet-stream',
                  Authorization: storeUtil.get('token').value,
                },
                maxContentLength: Infinity,
                maxBodyLength: Infinity,
              }
            )
            .then((res) => {
              if (res.status !== 200) {
                message.error('分片上传失败！');
              }
              message.success('上传完成！');
              setUploaded(true);
              setUploading(false)
              setOpen(true,'success')
            }).catch((error) =>{
              message.error('分片上传失败！');
              setUploading(false)
            })
        } catch (error) {
        }
      };
      reader.readAsArrayBuffer(file?.originFileObj);
    });
  };

  const fileChange: UploadProps['onChange'] = (info) => {
    let newFileList = [...info.fileList];
    newFileList = newFileList.slice(-1);
    newFileList = newFileList.map((file) => {
      if (file.response) {
        file.url = file.response.url;
      }
      return file;
    });
    setFileList(newFileList);
  };

  const props: UploadProps = {
    name: 'file',
    multiple: true,
    onChange: fileChange,
    beforeUpload: (file) => {
      // 定义允许的文件格式
      const allowedFormats = ['zip'];
      // 获取文件扩展名
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      if (!fileExtension || !allowedFormats.includes(fileExtension)) {
        message.error(
          '不支持的文件格式，请上传 zip 文件。'
        );
        return Upload.LIST_IGNORE;
      }
      setFileList([...fileList, file]);
      return false; // 返回 true 表示上传，返回 false 表示不上传
    },
  };
  return (
    <>
      <ConfigProvider
        theme={{
          components: {
            Button: {
              colorBorder: 'white',
              primaryShadow: 'transparent',
            },
          },
        }}
      >
        <Modal
          classNames={{
            header: styles.modalHeader,
            content: styles.modalContent,
            body: styles.modalBody,
            footer: styles.modalFooter,
          }}
          title='导入页'
          centered
          open={open}
          onOk={() => {setOpen(true,'success'),setUploaded(false),setFileList([])}}
          onCancel={() => {setOpen(false,'close'),setUploaded(false),setFileList([])}}
          footer={null}
          closeIcon={<div className={styles.closeIcon}><CloseOutlined /></div>}
        >
          {uploading ? (
            <>
              <Upload
                {...props}
                fileList={fileList}
                showUploadList={{ showRemoveIcon: false }}
              />
            </>
          ) : (
            <Dragger {...props} fileList={fileList}>
              <p className='ant-upload-drag-icon'>
                <InboxOutlined />
              </p>
              <p className='ant-upload-text'>单击或拖动压缩包至此处</p>
            </Dragger>
          )}
          <br />
          <div style={{ display: 'flex', justifyContent: 'center' }}>
            <Space wrap align='center' size={'middle'}>
              {uploaded ? (
                <Button
                  type='primary'
                  onClick={() => {
                    setUploading(false);
                    setUploaded(false);
                    setFileList([]);
                  }}
                  style={{
                    width: '100px',
                    margin: 'auto',
                    display: 'inline-block',
                  }}
                >
                  继续上传
                </Button>
              ) : (
                <Button
                  onClick={handleUpload}
                  disabled={fileList.length === 0}
                  loading={uploading}
                  style={{
                    width: '100px',
                    margin: 'auto',
                    display: 'inline-block',
                  }}
                >
                  {uploading ? '上传中' : '开始上传'}
                </Button>
              )}
            </Space>
          </div>
        </Modal>
      </ConfigProvider>
    </>
  );
};

export default UploadModal;
