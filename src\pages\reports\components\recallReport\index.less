// 弹窗样式

.closeIcon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 28px;
  height: 28px;
  color: #ffffff;
  font-weight: 600;
  border-radius: 50%;
  background-color: #D92E2D;
}

.DropIcon {
  color: #ffffff;
}

// 弹出框 样式重新
.modalHeader {
  padding: 12px 20px!important;
  font-weight: 400!important;
  background-color: #4C4C4C!important;
}
.modalContent {
  color: #ffffff;
  padding: 0!important;
  background-color: #333233!important;
}
.modalBody {
  padding: 20px!important;
}
.modalFooter {
  border-radius: 10px!important;
  padding:  20px!important;
  background-color: #333233!important;
}