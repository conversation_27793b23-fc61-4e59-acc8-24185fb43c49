import * as cornerstoneTools from '@cornerstonejs/tools';
import type { Types } from '@cornerstonejs/tools';
import TextTool from '@/utils/TextTool';

/**
 * 工具类的按键绑定，后续可能进行扩展
 *
 */

const {
  LengthTool,
  AngleTool,
  ProbeTool,
  WindowLevelTool,
  StackScrollMouseWheelTool,
  StackScrollTool,
  PanTool,
  ZoomTool,
  TrackballRotateTool,
  CrosshairsTool,
  RectangleROITool,
  CircleROITool,
  EllipticalROITool,
  PlanarFreehandROITool,
  ArrowAnnotateTool,
  Enums: csToolsEnums,
} = cornerstoneTools;

const { MouseBindings, KeyboardBindings } = csToolsEnums;

let registered = false;

export type ToolBinding = {
  // A base tool to register.  Should only be defined once per tool
  tool?: any;
  // The tool name to base this on
  baseTool?: string;
  // The configuration to register with
  configuration?: Record<string, any>;
  // Sets to passive initially
  passive?: boolean;
  // Initial bindings
  bindings?: Types.IToolBinding[];
};

/**
 * Adds navigation bindings to the given tool group.  Registers the basic
 * tool with CS Tools if register is true.
 *
 * Adds:
 * * Pan on Right or Primary+Ctrl
 * * Zoom on Middle, Primary+Shift
 * * Stack Scroll on Mouse Wheel, Primary+Alt
 * * Length Tool on fourth button
 *
 * Also allows registering other tools by having them in the options.toolMap with configuration values.
 */
export default function addManipulationBindings(
  toolGroup: any,
  options: {
    enableShiftClickZoom?: boolean;
    is3DViewport?: boolean;
    toolMap?: Map<string, ToolBinding>;
  } = {}
) {
  const zoomBindings: Types.IToolBinding[] = [
    {
      mouseButton: MouseBindings.Secondary,
    },
  ];

  const { is3DViewport = false, enableShiftClickZoom = false, toolMap = new Map() } = options;

  if (enableShiftClickZoom === true) {
    zoomBindings.push({
      mouseButton: MouseBindings.Primary, // Shift Left Click
      modifierKey: KeyboardBindings.Shift,
    });
  }

  if (!registered) {
    const tools = [
      StackScrollMouseWheelTool,
      PanTool,
      ZoomTool,
      TrackballRotateTool,
      CrosshairsTool,
      LengthTool,
      AngleTool,
      ProbeTool,
      RectangleROITool,
      CircleROITool,
      EllipticalROITool,
      PlanarFreehandROITool,
      WindowLevelTool,
      ArrowAnnotateTool,
      // 注意：TextTool 是独立实现的，不通过cornerstone tools注册
      // 它通过 toolSwitcher.ts 中的事件监听来处理
    ];

    tools.forEach((tool) => {
      cornerstoneTools.addTool(tool);
    });

    for (const [, config] of toolMap) {
      if (config.tool) {
        cornerstoneTools.addTool(config.tool);
      }
    }
  }

  registered = true;

  // 检查并添加工具，避免重复添加
  const addToolIfNotExists = (toolName: string, config?: any) => {
    if (!toolGroup.hasTool(toolName)) {
      toolGroup.addTool(toolName, config);
    }
  };

  addToolIfNotExists(PanTool.toolName);
  // Allow significant zooming to occur
  addToolIfNotExists(ZoomTool.toolName, {
    minZoomScale: 0.001,
    maxZoomScale: 4000,
  });
  addToolIfNotExists(StackScrollMouseWheelTool.toolName);
  addToolIfNotExists(WindowLevelTool.toolName);
  addToolIfNotExists(CrosshairsTool.toolName);
  addToolIfNotExists(LengthTool.toolName);
  addToolIfNotExists(AngleTool.toolName);
  addToolIfNotExists(ProbeTool.toolName);
  addToolIfNotExists(RectangleROITool.toolName);
  addToolIfNotExists(CircleROITool.toolName);
  addToolIfNotExists(EllipticalROITool.toolName);
  addToolIfNotExists(PlanarFreehandROITool.toolName);
  addToolIfNotExists(ArrowAnnotateTool.toolName);

  // toolGroup.setToolActive(PanTool.toolName, {
  //   bindings: [
  //     {
  //       mouseButton: MouseBindings.Primary,
  //     },
  //   ],
  // });
  toolGroup.setToolActive(CrosshairsTool.toolName, {
    bindings: [
      {
        mouseButton: MouseBindings.Primary,
      },
    ],
  });
  toolGroup.setToolActive(ZoomTool.toolName, {
    bindings: [
      {
        mouseButton: MouseBindings.Secondary,
      },
    ],
  });
  toolGroup.setToolActive(StackScrollMouseWheelTool.toolName);
  toolGroup.setToolActive(WindowLevelTool.toolName, {
    bindings: [
      {
        mouseButton: MouseBindings.Auxiliary, // 中键拖动
      },
    ],
  });
  toolGroup.setToolPassive(LengthTool.toolName);
  toolGroup.setToolPassive(AngleTool.toolName);
  toolGroup.setToolPassive(ProbeTool.toolName);
  toolGroup.setToolPassive(RectangleROITool.toolName);
  toolGroup.setToolPassive(CircleROITool.toolName);
  toolGroup.setToolPassive(EllipticalROITool.toolName);
  toolGroup.setToolPassive(PlanarFreehandROITool.toolName);
  toolGroup.setToolPassive(ArrowAnnotateTool.toolName);

  // Need a binding to navigate without a wheel mouse
  // toolGroup.setToolActive(StackScrollTool.toolName, {
  //   bindings: [
  //     {
  //       mouseButton: MouseBindings.Primary,
  //       modifierKey: KeyboardBindings.Alt,
  //     },
  //     {
  //       numTouchPoints: 1,
  //       modifierKey: KeyboardBindings.Alt,
  //     },
  //   ],
  // });
  // Add a length tool binding to allow testing annotations on examples targetting
  // other use cases.  Use a primary button with shift+ctrl as that is relatively
  // unlikely to be otherwise used.
  // 关闭预先定义的lengthTool
  // toolGroup.setToolActive(LengthTool.toolName, {
  //   bindings: [
  //     {
  //       mouseButton: MouseBindings.Primary,
  //       modifierKey: KeyboardBindings.ShiftCtrl,
  //     },
  //     {
  //       numTouchPoints: 1,
  //       modifierKey: KeyboardBindings.ShiftCtrl,
  //     },
  //   ],
  // });

  // if (is3DViewport) {
  //   toolGroup.setToolActive(TrackballRotateTool.toolName, {
  //     bindings: [
  //       {
  //         mouseButton: MouseBindings.Primary,
  //       },
  //     ],
  //   });
  // } else {
  //   //toolGroup.setToolActive(StackScrollMouseWheelTool.toolName);
  // }

  // Add extra tools from the toolMap
  for (const [toolName, config] of toolMap) {
    if (config.baseTool) {
      if (!toolGroup.hasTool(config.baseTool)) {
        toolGroup.addTool(config.baseTool, toolMap.get(config.baseTool)?.configuration);
      }
      toolGroup.addToolInstance(toolName, config.baseTool, config.configuration);
    } else if (!toolGroup.hasTool(toolName)) {
      toolGroup.addTool(toolName, config.configuration);
    }
    if (config.passive) {
      // This can be applied during add/remove contours
      toolGroup.setToolPassive(toolName);
    }
    if (config.bindings || config.selected) {
      toolGroup.setToolActive(
        toolName,
        (config.bindings && config) || {
          bindings: [{ mouseButton: MouseBindings.Primary }],
        }
      );
    }
  }
}
