import React, { useEffect, useRef } from 'react';
import {
  CONSTANTS,
  Enums,
  RenderingEngine,
  setVolumesForViewports,
  volumeLoader,
} from '@cornerstonejs/core';
import {
  ToolGroupManager,
  TrackballRotateTool,
  Enums as csToolsEnums,
} from '@cornerstonejs/tools';
import type {
  IRenderingEngine,
  IViewport,
} from '@cornerstonejs/core/dist/types/types';
import type { IToolGroup } from '@cornerstonejs/tools/dist/types/types';
// @ts-ignore
import { v4 as uuidv4 } from 'uuid';

const { MouseBindings } = csToolsEnums;
export const Dicom3DRender: React.FC<{
  renderingEngine: IRenderingEngine | null;
}> = (props) => {
  const { renderingEngine } = props;
  const viewportRef = useRef(null);
  const volumeLoaderScheme = 'cornerstoneStreamingImageVolume';
  const viewportId = `${volumeLoaderScheme}:3D_VIEWPORT`;

  const addToolsForViewport = (viewport: IViewport) => {
    const toolGroup: IToolGroup | undefined =
      ToolGroupManager.createToolGroup(`${uuidv4()}`);
    if (toolGroup && renderingEngine) {
      toolGroup.addTool(TrackballRotateTool.toolName);
      toolGroup?.addViewport(viewport.id, renderingEngine.id);
      toolGroup.setToolActive(TrackballRotateTool.toolName, {
        bindings: [
          {
            mouseButton: MouseBindings.Primary,
          },
        ],
      });
    }
  };

  useEffect(() => {
    const init3DRender = async () => {
      if (viewportRef.current && renderingEngine) {
        const viewportInputArray = [
          {
            viewportId,
            type: Enums.ViewportType.VOLUME_3D,
            element: viewportRef.current,
            defaultOptions: {
              orientation: Enums.OrientationAxis.CORONAL,
              background: CONSTANTS.BACKGROUND_COLORS.slicer3D as [
                number,
                number,
                number
              ],
            },
          },
        ];
        renderingEngine?.setViewports(viewportInputArray);
        // toolGroup?.addViewport(viewportId, renderingEngine.id);

        const imageIds: string[] = new Array(180).fill(0).map((_, index) => {
          if (index < 10) return `wadouri:/testDicoms/000${index}.dcm`;
          if (index >= 10 && index < 100)
            return `wadouri:/testDicoms/00${index}.dcm`;
          return `wadouri:/testDicoms/0${index}.dcm`;
        });

        const volume = await volumeLoader.createAndCacheVolume('CT_VOLUME_ID', {
          imageIds,
        });
        volume.load();

        setVolumesForViewports(
          renderingEngine,
          [{ volumeId: 'CT_VOLUME_ID' }],
          [viewportId]
        ).then(() => {
          const viewport = renderingEngine.getViewport(viewportId);
          addToolsForViewport(viewport);
          viewport.setProperties({
            preset: viewportId,
          });

          viewport.render();
        });
      }
    };
    init3DRender();
    return () => {
      if (renderingEngine) {
        renderingEngine.destroy();
      }
    };
  }, [renderingEngine]);

  return <div ref={viewportRef} style={{ width: '500px', height: '500px' }} />;
};

export default Dicom3DRender;
