import React, { useEffect, useState } from 'react';
import { type Dispatch } from 'umi';
import type { MenuProps } from 'antd';
import { ConfigProvider, Col, Row, Card, Space, Tag, Table, Image, Empty } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import styles from './CaseDetail.less';

interface CaseDetail {
  listLoading?: boolean;
  listData?: any;
  reportLoading?: boolean;
  reportData?: any;
  imgs?: any[];
  dispatch: Dispatch;
}

const CaseDetail: React.FC<CaseDetail> = ({ listLoading, listData, reportData, reportLoading, imgs, dispatch }) => {
  const [seriesId, setSeriesId] = useState<string>();
  const [modality, setModality] = useState<string>();
  const [imgIndex, setImgIndex] = useState<number>(0); // 当前展示的四张图片的起始索引
  const [curIndex, setCurIndex] = useState<number>(0); // 当前选中的图片索引

  useEffect(() => {
    if (seriesId && modality) {
      // 获取当前展示的四张图片的索引
      const index = listData?.findIndex((item: any) => item.Series_id === seriesId);
      console.log('index:', index);

      if (index - imgIndex < 4 && index - imgIndex >= 0) {
        setCurIndex(index);
      } else {
        setImgIndex(index);
        setCurIndex(0);
      }
    }
  }, [seriesId, modality]);

  // ListData更新时获取所有图片
  useEffect(() => {
    if (listData) {
      dispatch({
        type: 'patients/fetchImgAll',
        payload: {
          listData,
        },
      });
    }
  }, [listData]);

  const columns: ColumnsType<any> = [
    // {
    //   title: '患者ID',
    //   dataIndex: 'Patient_id',
    //   ellipsis: true,
    //   align: 'center',
    // },
    {
      title: '模态',
      dataIndex: 'Modality',
      align: 'center',
      width: '0.7vw',
    },
    {
      title: '图像数',
      dataIndex: 'NumberOfSlices',
      align: 'center',
      width: '0.7vw',
    },
    {
      title: '姓名',
      dataIndex: 'Patient_name',
      align: 'center',
      width: '1vw',
    },
    {
      title: '序列号',
      dataIndex: 'Series_id',
      ellipsis: true,
      align: 'center',
      width: '2.5vw',
    },
    {
      title: '序列时间',
      dataIndex: 'Acquisition_date',
      align: 'center',
      width: '1vw',
    },
    // {
    //   title: 'Study',
    //   dataIndex: 'Study_id',
    //   ellipsis: true,
    //   align: 'center',
    // },
    // {
    //   title: '病灶位置',
    //   dataIndex: 'Patient_position',
    //   align: 'center',
    // },
    // {
    //   title: '操作',
    //   key: 'action',
    //   align: 'center',
    //   render: (_, record) => (
    //     <Space size='middle'>
    //       <a
    //         onClick={() => {
    //           setSeriesId(record.Series_id);
    //           setModality(record.Modality);
    //         }}
    //       >
    //         查看
    //       </a>
    //     </Space>
    //   ),
    // },
  ];

  // console.log('imgs:', imgs);

  // 生成图片组件
  const generateImg = (img: any, index: number, mod: string, id: string) => {
    return (
      <Image
        width={'14vw'}
        src={img}
        style={{
          borderRadius: 5,
          border: index === curIndex ? '2px solid #2277d5' : 'none',
        }}
        preview={{
          mask: (
            <>
              <span
                style={{
                  textAlign: 'left',
                  marginLeft: 10,
                  marginTop: 5,
                  // color: mod === 'CT' ? 'black' : 'white',
                  color: 'white',
                }}
              >
                {id?.slice(0, 15)}
                <br />
                {mod}
              </span>
              <span
                style={{
                  marginRight: 10,
                  marginTop: 5,
                  // color: mod === 'CT' ? 'black' : 'white',
                  color: 'white',
                }}
              >
                {listData[index]?.NumberOfSlices / 2}/{listData[index]?.NumberOfSlices}
              </span>
            </>
          ),
          maskClassName: styles.mask,
        }}
      />
    );
  };

  return (
    <ConfigProvider
      theme={{
        components: {},
      }}
    >
      <Row style={{ height: '32vh' }} gutter={{ xs: 8, sm: 16, md: 24 }}>
        <Col span={9} style={{ height: '32vh' }}>
          <Table
            className={styles.table}
            size='small'
            // virtual
            scroll={{ x: '30vw', y: '25vh' }}
            columns={columns}
            dataSource={listData?.map((record: any, index: number) => {
              // 手动加个key，后端可优化返回主键
              return {
                key: index,
                ...record,
              };
            })}
            loading={listLoading}
            pagination={false}
            rowSelection={{
              columnWidth: '0.7vw',
              columnTitle: '选择',
              type: 'radio',
              onChange(selectedRowKeys: React.Key[], selectedRows: { Series_id: string; Modality: string }[]) {
                setSeriesId(selectedRows[0].Series_id);
                setModality(selectedRows[0].Modality);
              },
            }}
          />
        </Col>
        <Col span={15}>
          <div
            style={{
              position: 'absolute',
              left: 20,
              top: '6vw',
              zIndex: 1,
            }}
            onMouseOver={(e) => {
              // 使鼠标变为选择状态
              (e.target as HTMLElement).style.cursor = 'pointer';
            }}
          >
            <Image src='/icon/pic_left.png' preview={false} width={40} />
          </div>
          <div
            style={{
              position: 'absolute',
              right: 20,
              top: '6vw',
              zIndex: 1,
            }}
            onMouseOver={(e) => {
              // 使鼠标变为选择状态
              (e.target as HTMLElement).style.cursor = 'pointer';
            }}
          >
            <Image src='/icon/pic_right.png' preview={false} width={40} />
          </div>
          <div
            style={{
              width: '100%',
              position: 'relative',
              display: 'flex',
              justifyContent: 'space-between',
            }}
          >
            {imgs?.[imgIndex] ? (
              generateImg(imgs[imgIndex], 0, listData[imgIndex]?.Modality, listData[imgIndex]?.Series_id)
            ) : (
              // <Empty description={false} style={{ width: 250 }} />
              <div style={{ width: '14vw', height: '14vw', borderRadius: 5, backgroundColor: '#404040' }} />
            )}
            {imgs?.[imgIndex + 1] ? (
              generateImg(imgs[imgIndex + 1], 1, listData[imgIndex + 1]?.Modality, listData[imgIndex + 1]?.Series_id)
            ) : (
              // <Empty description={false} style={{ width: 250 }} />
              <div style={{ width: '14vw', height: '14vw', borderRadius: 5, backgroundColor: '#404040' }} />
            )}
            {imgs?.[imgIndex + 2] ? (
              generateImg(imgs[imgIndex + 2], 2, listData[imgIndex + 2]?.Modality, listData[imgIndex + 2]?.Series_id)
            ) : (
              // <Empty description={false} style={{ width: 250 }} />
              <div style={{ width: '14vw', height: '14vw', borderRadius: 5, backgroundColor: '#404040' }} />
            )}
            {imgs?.[imgIndex + 3] ? (
              generateImg(imgs[imgIndex + 3], 3, listData[imgIndex + 3]?.Modality, listData[imgIndex + 3]?.Series_id)
            ) : (
              // <Empty description={false} style={{ width: 250 }} />
              <div style={{ width: '14vw', height: '14vw', borderRadius: 5, backgroundColor: '#404040' }} />
            )}
          </div>
        </Col>
      </Row>
    </ConfigProvider>
  );
};

export default CaseDetail;
