import React, { useEffect, useState } from 'react';
import { Row, Col, Button, Radio, Space, Empty, message } from 'antd';
import { useDispatch, useSelector, connect, type Dispatch } from 'umi';
import { ViewType } from '@/models/views';
import styles from './FusionModalContent.less';

interface FusionModalContentProps {
  dispatch: Dispatch;
  onOk?: () => void;
  onCancel?: () => void;
}

type Props = FusionModalContentProps & {
  ctSeriesLists?: any[];
  ptSeriesLists?: any[];
  seriesDescriptions?: Map<string, string>;
  selectedCt?: any;
  selectedPt?: any;
};

const FusionModalContent: React.FC<Props> = React.memo((props) => {
  const { dispatch, ctSeriesLists, ptSeriesLists, seriesDescriptions, selectedCt, selectedPt, onOk, onCancel } = props;

  // 处理确定按钮点击
  const handleOk = () => {
    if (selectedCt && selectedPt) {
      dispatch({
        type: 'views/save',
        payload: { isFusionMode: true },
      });
      message.success('融合成功');
      onOk?.();
    }
  };

  // 渲染CT序列表格
  const renderCtTable = () => {
    if (!ctSeriesLists || ctSeriesLists.length === 0) {
      return <Empty description='暂无数据' image={Empty.PRESENTED_IMAGE_SIMPLE} />;
    }

    return (
      <div>
        <div className={styles.tableHeader}>
          <div className={styles.radioCell}></div>
          <div className={styles.sequenceCell}>序号</div>
          <div className={styles.descriptionCell}>序列描述</div>
        </div>
        <div>
          {ctSeriesLists.map((series, index) => {
            const isSelected = selectedCt?.Id === series.Id;

            return (
              <div key={series.Id} className={`${styles.tableRow} ${styles.ctRow} ${isSelected ? styles.selected : ''}`}>
                <div className={styles.radioCell}>
                  <Radio
                    checked={isSelected}
                    onChange={() => {
                      dispatch({
                        type: 'views/save',
                        payload: {
                          selectedCt: series,
                        },
                      });
                    }}
                  />
                </div>
                <div className={`${styles.sequenceCell} ${isSelected ? styles.selected : ''}`}>
                  {isSelected ? <span style={{ color: '#1890ff' }}>{index + 1}</span> : index + 1}
                </div>
                <div className={`${styles.descriptionCell} ${isSelected ? styles.selected : ''}`}>
                  {series.SeriesDescription || '无描述'}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  // 渲染PET序列表格
  const renderPetTable = () => {
    if (!ptSeriesLists || ptSeriesLists.length === 0) {
      return <Empty description='暂无数据' image={Empty.PRESENTED_IMAGE_SIMPLE} />;
    }

    return (
      <div>
        <div className={styles.tableHeader}>
          <div className={styles.radioCell}></div>
          <div className={styles.sequenceCell}>序号</div>
          <div className={styles.descriptionCell}>序列描述</div>
        </div>
        <div>
          {ptSeriesLists.map((series, index) => {
            const isSelected = selectedPt?.Id === series.Id;

            return (
              <div key={series.Id} className={`${styles.tableRow} ${styles.petRow} ${isSelected ? styles.selected : ''}`}>
                <div className={styles.radioCell}>
                  <Radio
                    checked={isSelected}
                    onChange={() => {
                      dispatch({
                        type: 'views/save',
                        payload: {
                          selectedPt: series,
                        },
                      });
                      dispatch({
                        type: 'views/FetchPtSeriesTags',
                        payload: series.SeriesId,
                      });
                    }}
                  />
                </div>
                <div className={`${styles.sequenceCell} ${isSelected ? styles.selected : ''}`}>
                  {isSelected ? <span style={{ color: '#1890ff' }}>{index + 1}</span> : index + 1}
                </div>
                <div className={`${styles.descriptionCell} ${isSelected ? styles.selected : ''}`}>
                  {series.SeriesDescription || '无描述'}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className={styles.fusionModalContent}>
      <Row gutter={16} style={{ margin: 0 }}>
        <Col span={12} style={{ paddingLeft: 0, paddingRight: 8 }}>
          <div className={styles.seriesListHeader}>
            <h3>CT</h3>
          </div>
          <div className={styles.seriesListContainer}>{renderCtTable()}</div>
        </Col>
        <Col span={12} style={{ paddingLeft: 8, paddingRight: 0 }}>
          <div className={styles.seriesListHeader}>
            <h3>PET</h3>
          </div>
          <div className={styles.seriesListContainer}>{renderPetTable()}</div>
        </Col>
      </Row>
      <div className={styles.modalFooter}>
        <Button onClick={onCancel}>取消</Button>
        <Button
          type={!selectedCt || !selectedPt ? undefined : 'primary'}
          disabled={!selectedCt || !selectedPt}
          onClick={handleOk}
          style={
            !selectedCt || !selectedPt
              ? {
                  backgroundColor: 'rgba(0, 0, 0, 0.04)',
                  borderColor: 'rgb(126, 126, 127)',
                  color: 'rgb(126, 126, 127)',
                  textShadow: 'none',
                  boxShadow: 'none',
                  cursor: 'not-allowed',
                }
              : undefined
          }
        >
          确定
        </Button>
      </div>
    </div>
  );
});

const mapStateToProps = ({ views }: { views: ViewType }) => {
  return {
    // 阅片model存储的信息
    ctSeriesLists: views.ctSeriesLists,
    ptSeriesLists: views.ptSeriesLists,
    seriesDescriptions: views.seriesDescriptions,
    selectedCt: views.selectedCt,
    selectedPt: views.selectedPt,
  };
};

export default connect(mapStateToProps)(FusionModalContent);
