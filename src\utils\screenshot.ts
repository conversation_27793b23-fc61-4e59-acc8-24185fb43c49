import { getRenderingEngine } from '@cornerstonejs/core';
import { message } from 'antd';
import storeUtil from '@/utils/store';

// 声明全局变量 CLOUD_API
declare const CLOUD_API: string;

// 截图功能
export async function captureScreenshot(dispatch: any): Promise<string | null> {
  try {


    // 尝试找到DicomArea元素
    const dicomArea = document.querySelector('.ant-col-18');
    if (!dicomArea) {
      message.error('未找到图像区域');
      return null;
    }

    // 查找当前页面中的所有canvas元素
    const canvases = dicomArea.querySelectorAll('canvas');


    if (canvases.length === 0) {
      // 如果没有找到canvas，创建一个简单的黑色截图

      const defaultCanvas = document.createElement('canvas');
      defaultCanvas.width = 800;
      defaultCanvas.height = 600;
      const ctx = defaultCanvas.getContext('2d');
      if (ctx) {
        ctx.fillStyle = '#000000';
        ctx.fillRect(0, 0, defaultCanvas.width, defaultCanvas.height);
        ctx.fillStyle = '#ffffff';
        ctx.font = '20px Arial';
        ctx.fillText('未找到可截图的图像区域', 250, 300);

        return new Promise((resolve) => {
          defaultCanvas.toBlob((blob) => {
            if (!blob) {
              message.error('截图生成失败');
              resolve(null);
              return;
            }

            const imageUrl = URL.createObjectURL(blob);
            dispatch({
              type: 'views/captureScreenshot',
              payload: {
                imageBlob: blob,
                imageUrl: imageUrl,
              },
            });
            message.success('截图保存成功');
            resolve(imageUrl);
          }, 'image/png');
        });
      } else {
        message.error('未找到可截图的图像区域');
        return null;
      }
    }

    // 创建一个新的canvas来合并所有canvas
    const mergedCanvas = document.createElement('canvas');
    const ctx = mergedCanvas.getContext('2d');

    if (!ctx) {
      message.error('无法创建截图画布');
      return null;
    }

    // 计算合并后的canvas尺寸
    let maxWidth = 0;
    let maxHeight = 0;
    let totalWidth = 0;
    let totalHeight = 0;

    // 简单的合并策略：水平排列
    canvases.forEach((canvas) => {

      totalWidth += canvas.width;
      maxHeight = Math.max(maxHeight, canvas.height);
    });

    mergedCanvas.width = totalWidth || 800;
    mergedCanvas.height = maxHeight || 600;


    // 设置背景色为黑色
    ctx.fillStyle = '#000000';
    ctx.fillRect(0, 0, mergedCanvas.width, mergedCanvas.height);

    // 将所有canvas绘制到合并canvas上
    let currentX = 0;
    canvases.forEach((canvas) => {
      if (canvas.width > 0 && canvas.height > 0) {
        try {
          ctx.drawImage(canvas, currentX, 0);
          currentX += canvas.width;
        } catch (e) {

        }
      }
    });

    // 如果没有有效的canvas，使用第一个canvas或创建默认截图
    if (currentX === 0) {
      mergedCanvas.width = 800;
      mergedCanvas.height = 600;
      ctx.fillStyle = '#000000';
      ctx.fillRect(0, 0, mergedCanvas.width, mergedCanvas.height);
      ctx.fillStyle = '#ffffff';
      ctx.font = '20px Arial';
      ctx.fillText('截图生成失败', 350, 300);
    }



    // 转换为blob
    return new Promise((resolve) => {
      mergedCanvas.toBlob(async (blob) => {
        if (!blob) {
          message.error('截图生成失败');

          resolve(null);
          return;
        }

        try {


          // 直接生成本地预览URL，不依赖studyId
          const imageUrl = URL.createObjectURL(blob);


          // 通过dispatch调用views模型的截图功能

          dispatch({
            type: 'views/captureScreenshot',
            payload: {
              imageBlob: blob,
              imageUrl: imageUrl,
            },
          });

          message.success('截图保存成功');
          resolve(imageUrl);
        } catch (error) {

          message.error('保存截图失败');
          resolve(null);
        }
      }, 'image/png');
    });
  } catch (error) {

    message.error('截图失败');
    return null;
  }
}

// 保存截图到服务器
export async function saveScreenshot(imageBlob: Blob, studyId: string) {
  const formData = new FormData();
  formData.append('image', imageBlob);
  formData.append('StudyId', studyId);

  try {
    const token = storeUtil.get('token')?.value;
    const headers: HeadersInit = {
      'Content-Type': 'multipart/form-data',
    };

    if (token) {
      headers.Authorization = token;
    }

    const response = await fetch(`${CLOUD_API}/public/saveImage`, {
      method: 'POST',
      headers,
      body: formData,
    });
    return await response.json();
  } catch (error) {

    throw error;
  }
}

// 生成带有时间戳的截图URL
export function generateScreenshotUrl(blob: Blob): string {
  const timestamp = new Date().toLocaleString().replace(/[\/\s:,]/g, '_');
  const url = URL.createObjectURL(blob);
  return url;
}

// 捕获并添加截图到图库
export async function captureAndAddToGallery(dispatch: any): Promise<void> {
  try {
    // 查找DICOM区域
    const dicomArea = document.querySelector('.ant-col-18');
    if (!dicomArea) {
      message.error('未找到DICOM显示区域');
      return;
    }

    // 生成时间戳
    const timestamp = new Date().toLocaleString();

    try {
      // 动态导入html2canvas
      const html2canvasModule = await import('@html2canvas/html2canvas');
      const html2canvas = html2canvasModule.default;

      // 使用html2canvas捕获整个DICOM区域，包括DOM元素
      const canvas = await html2canvas(dicomArea, {
        useCORS: true,
        allowTaint: true,
        scale: 2, // 提高截图质量
        backgroundColor: '#000',
        logging: false,
        // 确保捕获完整内容
        windowWidth: dicomArea.scrollWidth,
        windowHeight: dicomArea.scrollHeight
      });

      // 转换为blob
      canvas.toBlob((blob) => {
        if (blob) {
          const imageUrl = URL.createObjectURL(blob);

          // 显示图片大小
          const fileSizeKB = blob.size / 1024;

          // 显示消息提示
          message.success(`截图保存成功，文件大小: ${fileSizeKB.toFixed(2)} KB`);

          // 通过dispatch调用views模型的截图功能
          dispatch({
            type: 'views/captureScreenshot',
            payload: {
              imageBlob: blob,
              imageUrl: imageUrl,
              timestamp: timestamp,
              size: `${fileSizeKB.toFixed(2)} KB`,
              width: canvas.width,
              height: canvas.height,
            },
          });

          // 触发自定义事件，通知截图库更新
          const event = new CustomEvent('newScreenshot', {
            detail: {
              imageUrl,
              timestamp,
              size: `${fileSizeKB.toFixed(2)} KB`,
              width: canvas.width,
              height: canvas.height,
            },
          });
          window.dispatchEvent(event);
        } else {
          message.error('截图生成失败');
          // 如果html2canvas截图失败，回退到原始的canvas合并方法
          fallbackCanvasCapture(dicomArea, dispatch, timestamp);
        }
      }, 'image/png');
    } catch (error) {
      console.error('html2canvas截图失败:', error);
      message.warning('高级截图模式失败，使用基本模式');
      // 如果html2canvas加载或执行失败，回退到原始的canvas合并方法
      fallbackCanvasCapture(dicomArea, dispatch, timestamp);
    }
  } catch (error) {
    console.error('截图失败:', error);
    message.error('截图失败');
  }
}

// 回退到原始的canvas合并方法
function fallbackCanvasCapture(dicomArea: Element, dispatch: any, timestamp: string): void {
  try {
    // 查找所有canvas元素
    const canvases = dicomArea.querySelectorAll('canvas');
    if (!canvases || canvases.length === 0) {
      message.error('未找到可截图的图像');
      return;
    }

    // 创建合并画布
    const mergedCanvas = document.createElement('canvas');
    const ctx = mergedCanvas.getContext('2d');
    if (!ctx) {
      message.error('无法创建截图上下文');
      return;
    }

    // 设置合并画布尺寸为DICOM区域尺寸
    const rect = dicomArea.getBoundingClientRect();
    mergedCanvas.width = rect.width;
    mergedCanvas.height = rect.height;

    // 填充黑色背景
    ctx.fillStyle = '#000';
    ctx.fillRect(0, 0, mergedCanvas.width, mergedCanvas.height);

    // 绘制所有canvas到合并画布
    canvases.forEach((canvas) => {
      const canvasRect = canvas.getBoundingClientRect();
      const x = canvasRect.left - rect.left;
      const y = canvasRect.top - rect.top;
      ctx.drawImage(canvas, x, y);
    });

    // 转换为blob
    mergedCanvas.toBlob((blob) => {
      if (blob) {
        const imageUrl = URL.createObjectURL(blob);

        // 显示图片大小
        const fileSizeKB = blob.size / 1024;

        // 显示消息提示
        message.success(`截图保存成功，文件大小: ${fileSizeKB.toFixed(2)} KB`);

        // 通过dispatch调用views模型的截图功能
        dispatch({
          type: 'views/captureScreenshot',
          payload: {
            imageBlob: blob,
            imageUrl: imageUrl,
            timestamp: timestamp,
            size: `${fileSizeKB.toFixed(2)} KB`,
            width: mergedCanvas.width,
            height: mergedCanvas.height,
          },
        });

        // 触发自定义事件，通知截图库更新
        const event = new CustomEvent('newScreenshot', {
          detail: {
            imageUrl,
            timestamp,
            size: `${fileSizeKB.toFixed(2)} KB`,
            width: mergedCanvas.width,
            height: mergedCanvas.height,
          },
        });
        window.dispatchEvent(event);
      } else {
        message.error('截图失败');
      }
    }, 'image/png');
  } catch (error) {
    console.error('回退截图失败:', error);
    message.error('截图失败');
  }
}
