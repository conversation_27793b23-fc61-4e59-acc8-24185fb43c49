import { getRenderingEngine, getRenderingEngines } from '@cornerstonejs/core';
import { Enums } from '@cornerstonejs/core';
import type { Types } from '@cornerstonejs/core';

interface MipReconstructionParams {
  method: 'MinIP' | 'MIP' | 'Mean' | 'Sum';
  axial: number;
  coronal: number;
  sagittal: number;
  enabled: boolean;
}

/**
 * MIP重建工具类
 * 负责处理医学影像的最大密度投影(MIP)、最小密度投影(MinIP)、平均值投影(Mean)和求和投影(Sum)
 */
class MipReconstructionManager {
  private renderingEngine: Types.IRenderingEngine | null = null;
  private currentParams: MipReconstructionParams | null = null;
  private originalViewportData: Map<string, any> = new Map();

  constructor() {
    this.initializeRenderingEngine();
  }

  /**
   * 初始化渲染引擎
   */
  private initializeRenderingEngine() {
    try {
      // 首先检查是否已经有渲染引擎
      if (this.renderingEngine) {
  
        return;
      }

      // 尝试多个可能的渲染引擎ID
      const possibleEngineIds = [
        'myRenderingEngine',
        'renderingEngine', 
        'default',
        'cornerstoneRenderingEngine',
        'main',
        'viewer'
      ];
      
  
      
      for (const engineId of possibleEngineIds) {
        try {
    
          this.renderingEngine = getRenderingEngine(engineId);
          if (this.renderingEngine) {
    
            // 验证渲染引擎是否可用
            const viewports = this.renderingEngine.getViewports();
    
            return;
          }
        } catch (e) {
  
          // 继续尝试下一个ID
        }
      }
      
      // 如果所有预定义ID都失败，尝试获取所有可用的渲染引擎
       try {
   
         const allEngines = getRenderingEngines();
         
         if (allEngines.length > 0) {
           this.renderingEngine = allEngines[0];
           return;
         }
         
         // 最后尝试默认方法
         this.renderingEngine = getRenderingEngine();
         if (this.renderingEngine) {
   
           return;
         }
       } catch (e) {
   
       }
      
      console.warn('MIP重建: 未找到任何可用的渲染引擎');
  
    } catch (error) {
      console.error('MIP重建: 初始化渲染引擎失败', error);
    }
  }

  /**
   * 应用MIP重建
   * @param params 重建参数
   * @param viewportId 视口ID
   */
  public async applyMipReconstruction(
    params: MipReconstructionParams,
    viewportId: string = 'CT_AXIAL_STACK'
  ): Promise<void> {
    try {

      
      if (!this.renderingEngine) {

        this.initializeRenderingEngine();
        if (!this.renderingEngine) {
          throw new Error('图像渲染引擎未初始化，请确保Cornerstone.js已正确加载并刷新页面重试');
        }

      } else {

      }
      
      // 验证渲染引擎状态
      try {
        const viewports = this.renderingEngine.getViewports();

      } catch (e) {
        console.error('MIP重建: 渲染引擎状态异常:', e);
        // 尝试重新初始化
        this.renderingEngine = null;
        this.initializeRenderingEngine();
        if (!this.renderingEngine) {
          throw new Error('图像渲染引擎状态异常，请刷新页面重试');
        }
      }

      // 尝试多个可能的视口ID
      const possibleViewportIds = [
        viewportId,
        'CT_AXIAL_STACK',
        'viewport-1',
        'viewport-0',
        'default'
      ];
      
      let viewport = null;
      let foundViewportId = '';
      
      for (const id of possibleViewportIds) {
        try {
          viewport = this.renderingEngine.getViewport(id);
          if (viewport) {
            foundViewportId = id;
            break;
          }
        } catch (e) {
          // 继续尝试下一个ID
        }
      }
      
      if (!viewport) {
        // 获取所有可用的视口
        const viewports = this.renderingEngine.getViewports();
        
        if (viewports.length > 0) {
          viewport = viewports[0];
          foundViewportId = viewport.id;

        } else {
          throw new Error('未找到任何可用的视口，请确保图像已正确加载');
        }
      }

      // 检查视口是否有图像数据
      try {
        const imageData = viewport.getImageData();
        if (!imageData) {
          throw new Error('视口中没有图像数据，请确保图像已完全加载');
        }

      } catch (e) {
        console.warn('MIP重建: 无法获取图像数据，但继续尝试重建');
      }

      // 保存当前参数
      this.currentParams = { ...params };

      if (params.enabled) {
        // 保存原始视口数据
        await this.saveOriginalViewportData(foundViewportId, viewport);
        
        // 应用重建
        await this.performReconstruction(viewport, params);
      } else {
        // 恢复原始数据
        await this.restoreOriginalViewportData(foundViewportId, viewport);
      }

      // 渲染视口
      viewport.render();
    } catch (error) {
      console.error('MIP重建: 应用重建失败', error);
      throw error;
    }
  }

  /**
   * 保存原始视口数据
   */
  private async saveOriginalViewportData(
    viewportId: string,
    viewport: Types.IStackViewport | Types.IVolumeViewport
  ): Promise<void> {
    try {
      if (!this.originalViewportData.has(viewportId)) {
        const viewportData = {
          camera: viewport.getCamera(),
          properties: viewport.getProperties(),
          // 可以根据需要添加更多数据
        };
        this.originalViewportData.set(viewportId, viewportData);

      }
    } catch (error) {
      console.error('MIP重建: 保存原始视口数据失败', error);
    }
  }

  /**
   * 恢复原始视口数据
   */
  private async restoreOriginalViewportData(
    viewportId: string,
    viewport: Types.IStackViewport | Types.IVolumeViewport
  ): Promise<void> {
    try {
      const originalData = this.originalViewportData.get(viewportId);
      if (originalData) {
        // 恢复相机设置
        if (originalData.camera) {
          viewport.setCamera(originalData.camera);
        }
        
        // 恢复属性设置
        if (originalData.properties) {
          viewport.setProperties(originalData.properties);
        }
        

      }
    } catch (error) {
      console.error('MIP重建: 恢复原始视口数据失败', error);
    }
  }

  /**
   * 执行重建
   */
  private async performReconstruction(
    viewport: Types.IStackViewport | Types.IVolumeViewport,
    params: MipReconstructionParams
  ): Promise<void> {
    try {

      
      // 根据重建方法执行不同的算法
      switch (params.method) {
        case 'MIP':
          await this.performMIP(viewport, params);
          break;
        case 'MinIP':
          await this.performMinIP(viewport, params);
          break;
        case 'Mean':
          await this.performMean(viewport, params);
          break;
        case 'Sum':
          await this.performSum(viewport, params);
          break;
        default:
          console.warn('MIP重建: 未知的重建方法', params.method);
      }
      
      // 应用厚度参数
      await this.applyThicknessParameters(viewport, params);
      
    } catch (error) {
      console.error('MIP重建: 执行重建失败', error);
      throw error;
    }
  }

  /**
   * 执行最大密度投影(MIP)
   */
  private async performMIP(
    viewport: Types.IStackViewport | Types.IVolumeViewport,
    params: MipReconstructionParams
  ): Promise<void> {
    try {

      
      // 设置MIP相关的渲染属性
      const properties = viewport.getProperties();
      
      // 使用正确的Cornerstone.js API设置MIP
      const mipProperties = {
        ...properties,
        blendMode: Enums.BlendModes.MAXIMUM_INTENSITY_BLEND,
      };
      
      viewport.setProperties(mipProperties);
      
      // 强制重新渲染
      viewport.render();
      
    } catch (error) {
      console.error('MIP重建: MIP算法执行失败', error);
    }
  }

  /**
   * 执行最小密度投影(MinIP)
   */
  private async performMinIP(
    viewport: Types.IStackViewport | Types.IVolumeViewport,
    params: MipReconstructionParams
  ): Promise<void> {
    try {

      
      const properties = viewport.getProperties();
      const minipProperties = {
        ...properties,
        blendMode: Enums.BlendModes.MINIMUM_INTENSITY_BLEND,
      };
      
      viewport.setProperties(minipProperties);
      viewport.render();
      
    } catch (error) {
      console.error('MIP重建: MinIP算法执行失败', error);
    }
  }

  /**
   * 执行平均值投影(Mean)
   */
  private async performMean(
    viewport: Types.IStackViewport | Types.IVolumeViewport,
    params: MipReconstructionParams
  ): Promise<void> {
    try {

      
      const properties = viewport.getProperties();
      const meanProperties = {
        ...properties,
        blendMode: Enums.BlendModes.AVERAGE_INTENSITY_BLEND,
      };
      
      viewport.setProperties(meanProperties);
      viewport.render();
      
    } catch (error) {
      console.error('MIP重建: Mean算法执行失败', error);
    }
  }

  /**
   * 执行求和投影(Sum)
   */
  private async performSum(
    viewport: Types.IStackViewport | Types.IVolumeViewport,
    params: MipReconstructionParams
  ): Promise<void> {
    try {

      
      const properties = viewport.getProperties();
      const sumProperties = {
        ...properties,
        blendMode: Enums.BlendModes.ADDITIVE_BLEND,
      };
      
      viewport.setProperties(sumProperties);
      viewport.render();
      
    } catch (error) {
      console.error('MIP重建: Sum算法执行失败', error);
    }
  }

  /**
   * 应用厚度参数到所有视口
   */
  private async applyThicknessParameters(
    viewport: Types.IStackViewport | Types.IVolumeViewport,
    params: MipReconstructionParams
  ): Promise<void> {
    try {

      
      if (!this.renderingEngine) {
        console.error('MIP重建: 渲染引擎未初始化');
        return;
      }
      
      // 获取所有视口
      const allViewports = this.renderingEngine.getViewports();

      
      // 为每个视口应用对应的厚度参数
      for (const vp of allViewports) {
        try {
          const viewportId = vp.id.toLowerCase();
          let slabThickness = params.axial; // 默认使用axial
          let viewportType = 'axial';
          
          // 根据视口ID判断方向
          if (viewportId.includes('coronal')) {
            slabThickness = params.coronal;
            viewportType = 'coronal';
          } else if (viewportId.includes('sagittal')) {
            slabThickness = params.sagittal;
            viewportType = 'sagittal';
          } else if (viewportId.includes('axial')) {
            slabThickness = params.axial;
            viewportType = 'axial';
          } else {
            // 如果ID中没有明确的方向信息，尝试通过相机方向判断
            const camera = vp.getCamera();
            const viewPlaneNormal = camera.viewPlaneNormal;
            
            if (Math.abs(viewPlaneNormal[0]) > 0.8) {
              slabThickness = params.sagittal;
              viewportType = 'sagittal';
            } else if (Math.abs(viewPlaneNormal[1]) > 0.8) {
              slabThickness = params.coronal;
              viewportType = 'coronal';
            } else {
              slabThickness = params.axial;
              viewportType = 'axial';
            }
          }
          

          
          // 设置slab thickness属性
          const properties = vp.getProperties();
          const newProperties = {
            ...properties,
            slabThickness: slabThickness,
          };
          
          vp.setProperties(newProperties);
          
          // 强制渲染该视口
          vp.render();
          
        } catch (error) {
          console.error(`MIP重建: 视口 ${vp.id} 应用厚度参数失败:`, error);
        }
      }
      
    } catch (error) {
      console.error('MIP重建: 应用厚度参数失败', error);
    }
  }

  /**
   * 获取当前重建参数
   */
  public getCurrentParams(): MipReconstructionParams | null {
    return this.currentParams;
  }

  /**
   * 调试信息 - 检查当前状态
   */
  public debugStatus(): void {

    
    // 检查渲染引擎状态

    
    if (this.renderingEngine) {
      try {

        const viewports = this.renderingEngine.getViewports();

        
        // 检查每个视口的详细信息
        viewports.forEach((viewport, index) => {
          try {
            
            
            // 检查图像数据
            try {
              const imageData = viewport.getImageData();
            } catch (e) {
  
            }
            
            // 检查相机状态
            try {
              const camera = viewport.getCamera();
  
            } catch (e) {
  
            }
            
            // 检查属性
            try {
              const properties = viewport.getProperties();
  
            } catch (e) {
  
            }
          } catch (e) {
  
          }
        });
      } catch (e) {

      }
    } else {
      // 尝试获取所有可用的渲染引擎进行诊断
      try {
        const allEngines = getRenderingEngines();

      } catch (e) {

      }
    }
    

  }

  /**
   * 强制重新初始化渲染引擎
   */
  public forceReinitialize(): boolean {
    this.renderingEngine = null;
    this.initializeRenderingEngine();
    
    if (this.renderingEngine) {
      return true;
    } else {
      return false;
    }
  }

  /**
   * 处理序列切换
   */
  public handleSeriesChange(): void {

    
    // 清理缓存数据
    this.cleanup();
    
    // 重新初始化渲染引擎
    this.renderingEngine = null;
    
    // 延迟初始化，确保新序列完全加载
    // 增加延迟时间，确保图像数据完全加载
    setTimeout(() => {
      this.initializeRenderingEngine();
      if (this.renderingEngine) {

        
        // 验证视口是否有图像数据
        const viewports = this.renderingEngine.getViewports();
        let hasImageData = false;
        
        for (const vp of viewports) {
          try {
            const imageData = vp.getImageData();
            if (imageData) {
              hasImageData = true;

            }
          } catch (e) {

          }
        }
        
        if (!hasImageData) {
          console.warn('MIP重建: 序列切换后图像数据未完全加载，MIP功能可能暂时不可用');
        }
      } else {
        console.warn('MIP重建: 序列切换后渲染引擎重新初始化失败');
      }
    }, 1200); // 增加延迟到1.2秒
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    this.originalViewportData.clear();
    this.currentParams = null;

  }
}

// 创建单例实例
const mipReconstructionManager = new MipReconstructionManager();

/**
 * 应用MIP重建
 * @param params 重建参数
 * @param viewportId 视口ID
 */
export const applyMipReconstruction = async (
  params: MipReconstructionParams,
  viewportId?: string
): Promise<void> => {
  return mipReconstructionManager.applyMipReconstruction(params, viewportId);
};

/**
 * 获取当前重建参数
 */
export const getCurrentMipParams = (): MipReconstructionParams | null => {
  return mipReconstructionManager.getCurrentParams();
};

/**
 * 调试MIP重建状态
 */
export const debugMipReconstruction = (): void => {
  return mipReconstructionManager.debugStatus();
};

/**
 * 强制重新初始化MIP重建渲染引擎
 */
export const forceReinitializeMipReconstruction = (): boolean => {
  return mipReconstructionManager.forceReinitialize();
};

/**
 * 清理MIP重建资源
 */
export const cleanupMipReconstruction = (): void => {
  return mipReconstructionManager.cleanup();
};

/**
 * 处理MIP重建序列切换
 */
export const handleMipSeriesChange = (): void => {
  return mipReconstructionManager.handleSeriesChange();
};

export default mipReconstructionManager;
export type { MipReconstructionParams };