import React, { useMemo, useState, useEffect } from 'react';
import storeUtil from '@/utils/store';
import { Row, Col, Modal, Select, Input, Button, message, Slider, Tooltip, Popover, Flex  } from 'antd';
import { CaretRightOutlined, PauseOutlined,CloseOutlined } from '@ant-design/icons';
import styles from './index.less';
import GridSelect from './GridSelect';
import FusionModalContent from './FusionModalContent';
import CornerInfoModal, { CornerInfoConfig, defaultConfig } from './CornerInfoModal';
import MipReconstructionPanel, { ReconstructionParams } from '@/components/MipReconstructionPanel';
import DicomInfoModal from './DicomInfoModal';
import OperationInstructionModal from './OperationInstructionModal';
import { applyMipReconstruction, debugMipReconstruction, forceReinitializeMipReconstruction } from '@/utils/mipReconstruction';
import { connect, type Dispatch } from 'umi';
import { ViewType } from '@/models/views';
import { changeTool, clearLastAnnotation, clearAllAnnotations } from '@/utils/toolSwitcher';
import { set } from '@kitware/vtk.js/macros';
import { captureAndAddToGallery } from '@/utils/screenshot';
import CustomIcon from '@/components/CustomIcon';

enum ModalTypeName {
  grid = 'grid',
  colorMap = 'colorMap',
  windowLevel = 'windowLevel', // 新增
  fusion = 'fusion',
}

interface LeftToolBarProps {
  dispatch: Dispatch;
  row?: number;
  col?: number;
  isPlaying?: boolean;
  playSpeed?: number;
  views: ViewType;
}

interface WindowPreset {
  windowWidth: number;
  windowCenter: number;
}

const WINDOW_LEVEL_PRESETS: Record<string, WindowPreset> = {
  // abdomen: { windowWidth: 400, windowCenter: 60 }, // F1 - 腹部
  // chest: { windowWidth: 400, windowCenter: 20 }, // F2 - 胸
  // mediastinum: { windowWidth: 350, windowCenter: 50 }, // F3 - 纵隔
  // brain: { windowWidth: 80, windowCenter: 40 }, // F4 - 脑
  // bone: { windowWidth: 2000, windowCenter: 500 }, // F5 - 骨
  // lung: { windowWidth: 1500, windowCenter: -500 }, // F6 - 肺
  // default: { windowWidth: 400, windowCenter: 40 }, // 默认
  chest: { windowWidth: 400, windowCenter: 40 }, // F2 - 胸
  mediastinum: { windowWidth: 400, windowCenter: 60 }, // F3 - 纵隔
  brain: { windowWidth: 80, windowCenter: 40 }, // F4 - 脑
  bone: { windowWidth: 1500, windowCenter: 300 }, // F5 - 骨
  lung: { windowWidth: 1500, windowCenter: -400 }, // F6 - 肺
  default: { windowWidth: 80, windowCenter: 40 }, // 默认
};

const LeftToolBar: React.FC<LeftToolBarProps> = ({ dispatch, views }) => {
  const [layoutModal, setLayoutModal] = useState<boolean>(false);
  const [layoutModalType, setLayoutModalType] = useState<ModalTypeName>(ModalTypeName.grid);
  const [curDicomsLayout, setCurDicomsLayout] = useState<{
    row: number;
    col: number;
  } | null>(null);
  const [curColor, setCurColor] = useState<string | null>(null);
  const [curWindowLevel, setCurWindowLevel] = useState<{
    width: number;
    center: number;
  } | null>(null);

  // 四角信息相关状态
  const [cornerInfoModalVisible, setCornerInfoModalVisible] = useState(false);

  // MIP重建面板相关状态
  const [mipPanelVisible, setMipPanelVisible] = useState(false);

  // DICOM信息模态框相关状态
  const [dicomInfoModalVisible, setDicomInfoModalVisible] = useState(false);

  // 操作说明模态框相关状态
  const [operationInstructionModalVisible, setOperationInstructionModalVisible] = useState(false);

  // dev_master分支的播放控制状态
  const [playSpeed, setPlaySpeed] = useState(30); // 默认 30fps
  const [isPlaying, setIsPlaying] = useState(false);
  const [msPerFrame, setMsPerFrame] = useState(1000 / 30);

  useEffect(() => {
    // 如果没有studyId，尝试从storeUtil获取
    if (!views?.studyId) {
      const storedStudyId = storeUtil.get('studyId')?.value;
      if (storedStudyId) {
        dispatch({
          type: 'views/save',
          payload: { studyId: storedStudyId },
        });
      }
    }

    // 获取序列数据
    dispatch({
      type: 'views/fetchSeries',
      payload: { StudyId: views?.studyId || storeUtil.get('studyId')?.value },
    });
  }, [views?.studyId]);

  useEffect(() => {
    setPlaySpeed(1000 / msPerFrame);
  }, [msPerFrame]);

  // 在组件加载完成后自动执行十字线功能
  useEffect(() => {
    // 确保视图已加载
    if (views?.seriesList && views.seriesList.length > 0) {
      console.log('自动应用十字线功能');
      // 延迟执行，确保渲染引擎已初始化
      const timer = setTimeout(() => {
        // 先点击一次十字线按钮，确保功能被激活
        handleToolClick('Crosshairs');
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [views?.seriesList]);

  // 添加快捷键监听器
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // 防止在输入框中触发快捷键
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return;
      }

      const keyToPreset: Record<string, string> = {
        F1: 'abdomen', // 腹部
        F2: 'chest', // 胸
        F3: 'mediastinum', // 纵隔
        F4: 'brain', // 脑
        F5: 'bone', // 骨
        F6: 'lung', // 肺
      };

      const presetKey = keyToPreset[event.key];
      if (presetKey) {
        event.preventDefault(); // 防止浏览器默认行为

        const preset = WINDOW_LEVEL_PRESETS[presetKey];
        if (preset) {
          console.log(`快捷键 ${event.key} 触发窗宽窗位预设: ${presetKey}`);

          // 直接应用窗宽窗位
          dispatch({
            type: 'views/save',
            payload: {
              windowWidth: preset.windowWidth,
              windowCenter: preset.windowCenter,
              windowLevelChanged: true,
            },
          });

          // 重置标志位
          setTimeout(() => {
            dispatch({
              type: 'views/save',
              payload: {
                windowLevelChanged: false,
              },
            });
          }, 500);

          // 更新当前选择状态
          setCurWindowLevel({ width: preset.windowWidth, center: preset.windowCenter });

          // 显示提示信息
          const presetLabels: Record<string, string> = {
            abdomen: '腹部',
            chest: '胸',
            mediastinum: '纵隔',
            brain: '脑',
            bone: '骨',
            lung: '肺',
          };
          message.info(`已应用 ${presetLabels[presetKey]} 窗宽窗位 (${preset.windowWidth}/${preset.windowCenter})`);
        }
      }
    };

    // 添加事件监听器
    document.addEventListener('keydown', handleKeyDown);

    // 清理函数
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [dispatch]);

  const handleToolClick = (newToolName: string) => {
    const currentTool = views.currentTool;

    console.log(`工具点击: 当前=${currentTool}, 新=${newToolName}`);

    if (currentTool === newToolName) {
      // 如果重复点击同一个工具，则关闭它
      console.log(`重复点击工具 ${newToolName}，执行关闭操作`);

      // 对于十字线工具，关闭后切换到平移工具
      if (newToolName === 'Crosshairs') {
        changeTool(currentTool, 'Pan');
        dispatch({
          type: 'views/save',
          payload: { currentTool: 'Pan' },
        });
        console.log('十字线工具已关闭，切换到平移工具');
      } else {
        changeTool(currentTool, '');
        // 对于其他工具，关闭后激活十字线工具
        dispatch({
          type: 'views/save',
          payload: { currentTool: 'Crosshairs' },
        });
        console.log(`工具 ${newToolName} 已关闭，切换回十字线工具`);
      }
    } else {
      // 切换到新工具
      console.log(`切换工具: ${currentTool} -> ${newToolName}`);
      if (currentTool) {
        changeTool(currentTool, newToolName);
      } else {
        changeTool('', newToolName);
      }
      dispatch({
        type: 'views/save',
        payload: { currentTool: newToolName },
      });
    }
  };

  const handleSpeedChange = (value: number) => {
    dispatch({
      type: 'views/setPlaySpeed',
      payload: value,
    });
  };

  const togglePlay = () => {
    dispatch({
      type: 'views/togglePlay',
      payload: {},
    });
  };

  const openModal = (type: ModalTypeName) => {
    setLayoutModalType(type);
    setLayoutModal(true);
  };

  const layoutModalConfigure = useMemo(() => {
    return {
      grid: {
        title: '图像布局设置',
        onOk: () => {
          if (curDicomsLayout) {
            const { row, col } = curDicomsLayout;
            dispatch({
              type: 'views/save',
              payload: { row, col },
            });
          }
          setLayoutModal(false);
        },
        onCancel: () => {
          setLayoutModal(false);
          setCurDicomsLayout(null);
        },
        content: (
          <GridSelect
            value={curDicomsLayout}
            onChange={(row, col) => {
              setCurDicomsLayout((prev) => ({ ...prev, row, col }));
              // console.log(row, col);
            }}
          />
        ),
      },
      colorMap: {
        title: '伪彩',
        onOk: () => {
          setLayoutModal(false);
          // console.log('value', curColor);
          if (curColor && ['Grayscale', 'X Ray', 'hsv', 'jet', 'suv'].includes(curColor)) {
            // console.log('value', curColor);
            dispatch({
              type: 'views/save',
              payload: { selectedColorMap: String(curColor) },
            });
          }
        },
        onCancel: () => {
          setLayoutModal(false);
        },
        content: (
          <Select
            defaultValue='伪彩方案'
            style={{ width: '100%' }}
            onChange={(value) => {
              setCurColor(value);
              // console.log('value', value);
            }}
            options={[
              {
                value: 'Grayscale',
                label: (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <img src='/textures/normal.png' style={{ width: 800, height: 20, marginRight: 8 }} />
                  </div>
                ),
              },
              {
                value: 'X Ray',
                label: (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <img src='/textures/black_white_black.png' style={{ width: 800, height: 20, marginRight: 8 }} />
                  </div>
                ),
              },
              {
                value: 'hsv',
                label: (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <img src='/textures/hsv.png' style={{ width: 800, height: 20, marginRight: 8 }} />
                  </div>
                ),
              },
              {
                value: 'jet',
                label: (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <img src='/textures/jet.png' style={{ width: 800, height: 20, marginRight: 8 }} />
                  </div>
                ),
              },
            ]}
          />
        ),
      },
      windowLevel: {
        title: '窗宽窗位设置',
        onOk: () => {
          if (curWindowLevel) {
            console.log('应用窗宽窗位设置:', curWindowLevel);
            // 立即应用窗宽窗位设置
            dispatch({
              type: 'views/save',
              payload: {
                windowWidth: curWindowLevel.width,
                windowCenter: curWindowLevel.center,
                windowLevelChanged: true, // 添加标志，表示窗宽窗位已更改
              },
            });

            // 延迟重置标志，确保视图已更新
            setTimeout(() => {
              dispatch({
                type: 'views/save',
                payload: {
                  windowLevelChanged: false,
                },
              });
            }, 500);
          }
          setLayoutModal(false);
        },
        onCancel: () => {
          setLayoutModal(false);
          setCurWindowLevel(null);
        },
        content: (
          <div>
            <div style={{ marginBottom: 16, color: '#999', fontSize: '12px' }}>
              快捷键  F2-胸 | F3-纵隔 | F4-脑 | F5-骨 | F6-肺
            </div>
            <Select
              defaultValue='default'
              className='windowLevelSelect'
              options={[
                // { value: 'abdomen', label: '腹部 F1' },
                { value: 'chest', label: '胸 F2' },
                { value: 'mediastinum', label: '纵隔 F3' },
                { value: 'brain', label: '脑 F4' },
                { value: 'bone', label: '骨 F5' },
                { value: 'lung', label: '肺 F6' },
                { value: 'default', label: '自定义' },
              ]}
              onChange={(value) => {
                const preset = WINDOW_LEVEL_PRESETS[value];
                if (preset) {
                  setCurWindowLevel({ width: preset.windowWidth, center: preset.windowCenter });
                }
              }}
              style={{ width: '100%', marginBottom: 16 }}
              dropdownStyle={{ backgroundColor: '#262628', border: '1px solid #555' }}
            />
            <Flex vertical gap={16} className={styles.GlobalInputStyle}>
              <Flex gap={16} align='center'>
                <label style={{ flexShrink: 0 }}>窗 宽</label>
                <Input
                  placeholder='窗宽'
                  value={curWindowLevel?.width}
                  onChange={(e) =>
                    setCurWindowLevel((prev) => ({
                      ...prev!,
                      width: Number(e.target.value),
                    }))
                  }
                />
              </Flex>
              <Flex gap={16} align='center'>
                <label style={{ flexShrink: 0 }}>窗 宽</label>
                <Input
                  placeholder='窗位'
                  value={curWindowLevel?.center}
                  onChange={(e) =>
                    setCurWindowLevel((prev) => ({
                      ...prev!,
                      center: Number(e.target.value),
                    }))
                  }
                />
              </Flex>
            </Flex>
          </div>
        ),
      },
      fusion: {
        title: '融合',
        // 移除onOk和onCancel，这些将通过FusionModalContent组件内部处理
        content: (
          <FusionModalContent
            onOk={() => {
              dispatch({
                type: 'views/save',
                payload: { isFusionMode: true },
              });
              setLayoutModal(false);
            }}
            onCancel={() => setLayoutModal(false)}
          />
        ),
      },
    };
  }, [curDicomsLayout, curColor, curWindowLevel]);

  // 截图功能处理函数
  const handleScreenshot = async () => {
    try {
      // 确保在截图前设置studyId
      if (views?.studyId) {
        dispatch({
          type: 'views/save',
          payload: { studyId: views.studyId },
        });
      }

      // console.log('开始截图，当前studyId:', views?.studyId);

      // 检查canvas是否存在
      const dicomArea = document.querySelector('.ant-col-18');
      const canvases = dicomArea?.querySelectorAll('canvas');
      // console.log('找到canvas元素数量:', canvases?.length || 0);

      if (!canvases || canvases.length === 0) {
        message.warning('未找到可截图的图像区域，请确保图像已加载');
      }

      await captureAndAddToGallery(dispatch);

      // 截图后检查views中的screenshots
      setTimeout(() => {
        // console.log('截图后的views状态:', views);
      }, 500);
    } catch (error) {
      console.error('截图失败:', error);
    }
  };

  // 处理四角信息配置
  const handleCornerInfoConfig = (config: CornerInfoConfig) => {
    dispatch({
      type: 'views/updateCornerInfoConfig',
      payload: { config },
    });
  };

  // 打开四角信息设置弹窗
  const openCornerInfoModal = () => {
    setCornerInfoModalVisible(true);
  };

  // 关闭四角信息设置弹窗
  const closeCornerInfoModal = () => {
    setCornerInfoModalVisible(false);
  };

  // 获取当前四角信息配置
  const getCurrentCornerInfoConfig = (): CornerInfoConfig => {
    return views.cornerInfoConfig || defaultConfig;
  };

  // 打开DICOM信息模态框
  const openDicomInfoModal = () => {
    setDicomInfoModalVisible(true);
  };

  // 关闭DICOM信息模态框
  const closeDicomInfoModal = () => {
    setDicomInfoModalVisible(false);
  };

  // 打开操作说明模态框
  const openOperationInstructionModal = () => {
    setOperationInstructionModalVisible(true);
  };

  // 关闭操作说明模态框
  const closeOperationInstructionModal = () => {
    setOperationInstructionModalVisible(false);
  };

  // 切换方向指示器显示/隐藏
  const toggleDirectionIndicator = () => {
    dispatch({
      type: 'views/save',
      payload: {
        directionIndicatorVisible: !views.directionIndicatorVisible,
      },
    });
  };

  // 切换敏感信息显示/隐藏
  const toggleSensitiveInfo = () => {
    dispatch({
      type: 'views/save',
      payload: {
        sensitiveInfoHidden: !views.sensitiveInfoHidden,
      },
    });
  };

  // 处理MIP按钮点击
  const handleMipClick = () => {
    setMipPanelVisible(!mipPanelVisible);
  };

  // 处理MIP重建参数变化
  const handleReconstructionChange = async (params: ReconstructionParams) => {
    try {
      // 更新Redux状态
      dispatch({
        type: 'views/updateMipReconstructionParams',
        payload: { params },
      });

      // 应用实际的MIP重建
      await applyMipReconstruction(params);
    } catch (error) {
      // 根据错误类型给出不同的提示和处理
      const errorMessage = error instanceof Error ? error.message : '未知错误';

      if (errorMessage.includes('渲染引擎')) {
        // 尝试强制重新初始化渲染引擎
        const reinitSuccess = forceReinitializeMipReconstruction();

        if (reinitSuccess) {
          message.warning('渲染引擎已重新初始化，请重试MIP重建');
        } else {
          message.error('图像渲染引擎初始化失败，请刷新页面重试');
        }
      } else if (errorMessage.includes('视口')) {
        message.error('未找到图像视口，请确保已打开DICOM图像');
      } else if (errorMessage.includes('图像数据')) {
        message.error('图像数据未完全加载，请等待加载完成后重试');
      } else {
        message.error(`MIP重建失败: ${errorMessage}`);
      }
    }
  };

  return (
    <div className={styles.container}>
      <div style={{ padding: '15px 10px', textAlign: 'center' }}>
        <img src='/icon/ray_logo.png' style={{ width: '80%' }} />
      </div>
      <div className={styles.ToolBarContainer}>
        <Tooltip title='三维'>
          <CustomIcon type='icon-MPR-01' className={styles.toolIcon} />
        </Tooltip>
        <Tooltip title='融合'>
          <CustomIcon type='icon-ronghe-01' className={styles.toolIcon} onClick={() => openModal(ModalTypeName.fusion)} />
        </Tooltip>
        <Tooltip title='布局'>
          <CustomIcon type='icon-buju-01' className={styles.toolIcon} onClick={() => openModal(ModalTypeName.grid)} />
        </Tooltip>
        <Tooltip title='窗宽窗位'>
          <CustomIcon
            type='icon-tiaochuang-01'
            className={styles.toolIcon}
            onClick={() => openModal(ModalTypeName.windowLevel)}
          />
        </Tooltip>
        <Tooltip title='MIP重建'>
          <CustomIcon
            className={`${styles.toolIcon} ${mipPanelVisible ? styles.activeIcon : ''}`}
            type='icon-MIP-01'
            onClick={handleMipClick}
          />
        </Tooltip>
        <Tooltip title='DICOM信息'>
          <CustomIcon type='icon-Dicombiaoqian-01' className={styles.toolIcon} onClick={openDicomInfoModal} />
        </Tooltip>
        <Tooltip title='平移工具'>
          <CustomIcon
            type='icon-pingyi-01'
            className={`${styles.toolIcon} ${views.currentTool === 'Pan' ? styles.activeIcon : ''}`}
            onClick={() => handleToolClick('Pan')}
          />
        </Tooltip>
        <Tooltip title='伪彩'>
          <CustomIcon type='icon-weicai-01' className={styles.toolIcon} onClick={() => openModal(ModalTypeName.colorMap)} />
        </Tooltip>
        <Tooltip title='重置'>
          <CustomIcon
            type='icon-zhongzhi-01'
            className={styles.toolIcon}
            onClick={() => {
              dispatch({
                type: 'views/save',
                payload: { resetViewport: true },
              });
            }}
          />
        </Tooltip>
        <div className={styles.separator}></div>
        <Tooltip title='操作说明'>
          <CustomIcon type='icon-shuoming-01' className={styles.toolIcon} onClick={openOperationInstructionModal} />
        </Tooltip>
        <Tooltip title='长度'>
          <CustomIcon type='icon-changdu-01' className={`${styles.toolIcon} ${views.currentTool === 'Length' ? styles.activeIcon : ''}`} onClick={() => handleToolClick('Length')} />
        </Tooltip>
        <Tooltip title='角度'>
          <CustomIcon type='icon-jiaodusvg-01' className={`${styles.toolIcon} ${views.currentTool === 'Angle' ? styles.activeIcon : ''}`} onClick={() => handleToolClick('Angle')} />
        </Tooltip>
        <Tooltip title='点测量'>
          <CustomIcon type='icon-dianceliang-01' className={`${styles.toolIcon} ${views.currentTool === 'Probe' ? styles.activeIcon : ''}`} onClick={() => handleToolClick('Probe')} />
        </Tooltip>
        <div className={styles.roiDropdown}>
          <Popover
            color="#000"
            trigger="hover"
            placement="right"
            content={
              <Flex gap={16}>
                  <Tooltip title='矩形ROI'>
                    <CustomIcon type='icon-juxing' className={`${styles.ROIIcon} ${views.currentTool === 'RectangleROI' ? styles.activeIcon : ''}`} onClick={() => handleToolClick('RectangleROI')} />
                  </Tooltip>
                  <Tooltip title='椭圆ROI'>
                    <CustomIcon type='icon-tuoyuan' className={`${styles.ROIIcon} ${views.currentTool === 'EllipticalROI' ? styles.activeIcon : ''}`} onClick={() => handleToolClick('EllipticalROI')} />
                  </Tooltip>
                  <Tooltip title='自由绘制ROI'>
                    <CustomIcon type='icon-ziyougouhua' className={`${styles.ROIIcon} ${views.currentTool === 'PlanarFreehandROI' ? styles.activeIcon : ''}`} onClick={() => handleToolClick('PlanarFreehandROI')} />
                  </Tooltip>
              </Flex>
            }
          >
            <Tooltip title='ROI'>
              <CustomIcon type='icon-ROI-01-01'  className={`${styles.toolIcon} ${
                views.currentTool === 'RectangleROI' ||
                views.currentTool === 'EllipticalROI' ||
                views.currentTool === 'PlanarFreehandROI'
                  ? styles.activeIcon
                  : ''
              }`} />
            </Tooltip>
          </Popover>  
          
        </div>
        <Tooltip title='箭头标注'>
          <CustomIcon type='icon-jiantou-01' className={`${styles.toolIcon} ${views.currentTool === 'ArrowAnnotate' ? styles.activeIcon : ''}`} onClick={() => handleToolClick('ArrowAnnotate')} />
        </Tooltip>
        <Tooltip title='文字'>
          <CustomIcon type='icon-wenzi-01' className={`${styles.toolIcon} ${views.currentTool === 'Text' ? styles.activeIcon : ''}`} onClick={() => handleToolClick('Text')} />
        </Tooltip>
        <Tooltip title='清除最近'>
          <CustomIcon type='icon-qingchuzuijin' className={styles.toolIcon} onClick={() => clearLastAnnotation(dispatch)} />
        </Tooltip>
        <Tooltip title='清除全部'>
          <CustomIcon type='icon-qingchuquanbu-01' className={styles.toolIcon} onClick={() => clearAllAnnotations(dispatch)} />
        </Tooltip>
        <Tooltip title='截图'>
          <CustomIcon type='icon-jietu-01' className={styles.toolIcon} onClick={handleScreenshot} />
        </Tooltip>
        <div className={styles.separator}></div>
        <Tooltip title='四角信息'>
          <CustomIcon type='icon-sijiaoxinxi-01' className={styles.toolIcon} onClick={openCornerInfoModal} />
        </Tooltip>
        <Tooltip title='方向'>
          <CustomIcon type='icon-fangxiang-01' className={styles.toolIcon} onClick={toggleDirectionIndicator} />
        </Tooltip>
        <Tooltip title='十字线'>
          <CustomIcon type='icon-shizixian-01' className={`${styles.toolIcon} ${views.currentTool === 'Crosshairs' ? styles.activeIcon : ''}`} onClick={() => handleToolClick('Crosshairs')} />
        </Tooltip>
        <Tooltip title='敏感信息'>
          <CustomIcon type='icon-minganxinxi-01' className={styles.toolIcon} onClick={toggleSensitiveInfo} />
        </Tooltip>
        <Tooltip title='重置'>
          <CustomIcon type='icon-zhongzhi-01' className={styles.toolIcon} onClick={() => {
            dispatch({
              type: 'views/save',
              payload: { resetViewport: true },
            });
          }}/>
        </Tooltip>
        <div className={styles.separator}></div>
        <img src='/icon/leftToolBar/brain.png' className={styles.colorIcon} title='脑部' />
        <img src='/icon/leftToolBar/heart.png' className={styles.colorIcon} title='心脏' />
      </div>

      {/* <Select
        style={{ width: 100 }}
        defaultValue={30}
        onChange={handleSpeedChange}
        options={[
          { value: 15, label: '15 FPS' },
          { value: 30, label: '30 FPS' },
          { value: 60, label: '60 FPS' },
        ]}
      />
      <Button onClick={togglePlay}>{isPlaying ? '暂停' : '开始'}</Button> */}

      <Modal
        classNames={{
          header: styles.modalHeader,
          content: styles.modalContent,
          body: styles.modalBody,
          footer: styles.modalFooter,
        }}
        title={<span >{layoutModalConfigure[layoutModalType]?.title || ''}</span>}
        open={layoutModal}
        footer={
          layoutModalType === ModalTypeName.fusion
            ? null
            : [
                <Button key='cancel' onClick={() => setLayoutModal(false)}>
                  取消
                </Button>,
                <Button
                  key='confirm'
                  type='primary'
                  onClick={() => {
                    const config = layoutModalConfigure[layoutModalType];
                    if (config?.onOk) {
                      config.onOk();
                    } else {
                      setLayoutModal(false);
                    }
                  }}
                >
                  确定
                </Button>,
              ]
        }
        width={layoutModalType === ModalTypeName.fusion ? 800 : 500}
        wrapClassName='fusion-modal-wrapper'
        onCancel={() => setLayoutModal(false)}
        closeIcon={<div className={styles.closeIcon}><CloseOutlined /></div>}
      >
        {layoutModalConfigure[layoutModalType]?.content}
      </Modal>

      {/* 四角信息设置模态框 */}
      {cornerInfoModalVisible && (
        <CornerInfoModal
          visible={cornerInfoModalVisible}
          onClose={closeCornerInfoModal}
          onConfirm={handleCornerInfoConfig}
          initialConfig={getCurrentCornerInfoConfig()}
        />
      )}

      {/* MIP重建面板 */}
      <MipReconstructionPanel
        visible={mipPanelVisible}
        onClose={() => setMipPanelVisible(false)}
        onReconstructionChange={handleReconstructionChange}
      />

      {/* DICOM信息模态框 */}
      <DicomInfoModal visible={dicomInfoModalVisible} onClose={closeDicomInfoModal} dispatch={dispatch} views={views} />

      {/* 操作说明模态框 */}
      <OperationInstructionModal visible={operationInstructionModalVisible} onClose={closeOperationInstructionModal} />
    </div>
  );
};

const mapStateToProps = ({ views }: { views: ViewType }) => {
  return {
    views,
  };
};

export default connect(mapStateToProps)(LeftToolBar);
