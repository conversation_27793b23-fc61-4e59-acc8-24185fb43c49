.card {
  // padding: 12px 16px;
  // background-color: #262628;
  .cardTit {
    color: #ffffff;
    font-size: 18px;
    font-weight: 400;
  }
  .cardBody {
    display: flex;
    flex-direction: column;
    align-items: center;
    .item {
      margin-bottom: 25px;
      color: #ffffff;
      text-align: center;
      cursor: pointer;
      .imgBox {
        margin-bottom: 8px;
        height: 147px;
        width: 300px;
        border-radius: 12px;
        background-color: #000000;
          img {
            height: 100%;
            width: auto;
          }
      }
      &:hover {
        transform: scale(1.03);
        box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 12px 0px;
        transition: all 0.3s ease-in-out;
      }
    }
  }
}
.tableBox {
  height: calc((100vh - 104px)/2);
  border-radius: 10px;
  padding: 16px 20px;
  background-color: rgb(38, 38, 40);
  .tableTitle {
    font-size: 18px;
    line-height: 18px;
    color: #ffffff;
  }
  .searchInput {
    // border-color: #B3B3B3;
    background-color: #262628;
    ::placeholder {
      color: #B3B3B3;
    }
  }
  .ant-input-clear-icon {
    color: #B3B3B3!important;
  }
  .searchIcon {
    color: #B3B3B3;
  }
  .cursor {
    cursor: pointer;
  }
}
.ant-popover .ant-popover-inner {
  padding: 0!important;
}
.searchBox {
  width: 200px;
  background-color: #262628;
  .searchBox_title {
    color: #ffffff;
    font-size: 14px;
    height: 35px;
    line-height: 35px;
    padding: 0 24px;
    background-color: #4C4C4C;
  }
  .searchBox_checkbox {
    display: flex;
    flex-direction: column;
    padding: 4px 24px 20px;
  }
  .searchBox_action {
    margin-top: 20px;
  }
}
.iconStyle {
  color: #ffffff;
  font-size: 16px;
}
.rowHeight {
  height: calc((100vh - 104px)/2);
}
.dicom_area {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  background-color: #000000;
  border-radius: 10px;
}