import React from 'react';
import { Table, Pagination } from 'antd';

// 自定义的分页可控的表格
interface TablePagable {
  loading?: boolean;
  dataSource?: any;
  columns?: any;
  total?: any;
  current?: number;
  setCurrent?: any;
}

const TablePagable: React.FC<TablePagable> = ({
  loading,
  dataSource,
  columns,
  total,
  current,
  setCurrent,
}) => {
  return (
    <>
      <Table
        loading={loading}
        dataSource={dataSource}
        columns={columns}
        // Id默认使用，后期需要变化时迭代
        pagination={false}
        rowKey='Id'
      />
      <br />
      <Pagination
        total={total}
        showTotal={(total, range) =>
          `第${range[0]}-${range[1]}条/总共${total}条`
        }
        showSizeChanger={false}
        current={current}
        onChange={(page) => setCurrent(page)}
        style={{ height: '32px', lineHeight: '32px', textAlign: 'center' }}
      />
    </>
  );
};
export default TablePagable;
