.mip-reconstruction-panel {
  position: absolute;
  top: 50px;
  left: 60px;
  width: 320px; // 增加面板宽度以容纳更长的滑动条
  background: rgba(45, 45, 45, 0.95);
  border: 1px solid #555;
  border-radius: 8px;
  padding: 16px;
  color: #fff;
  font-size: 12px;
  z-index: 9999; // 提高z-index确保面板在最上层
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  pointer-events: auto; // 确保面板可以接收指针事件
  user-select: none; // 防止文本选择干扰拖拽

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .panel-title {
      font-size: 14px;
      font-weight: 500;
      color: #fff;
    }
  }

  .panel-content {
    .method-selection {
      display: flex;
      gap: 8px;
      margin-bottom: 20px;
      
      .method-item {
        flex: 1;
        padding: 6px 8px;
        text-align: center;
        background: rgba(80, 80, 80, 0.6);
        border: 1px solid #666;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 11px;
        color: #ccc;
        
        &:hover {
          background: rgba(100, 100, 100, 0.8);
          border-color: #888;
        }
        
        &.active {
          background: rgba(24, 144, 255, 0.8);
          border-color: #1890ff;
          color: #fff;
        }
        
        &.mip-active {
          background: rgba(255, 140, 0, 0.8);
          border-color: #ff8c00;
          color: #fff;
        }
      }
    }

    .parameter-controls {
      .control-row {
        margin-bottom: 16px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .control-label {
          font-size: 12px;
          color: #fff;
          margin-bottom: 8px;
          font-weight: 500;
        }
        
        .control-content {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .slider-container {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 120px; // 确保滑动条有最小宽度
            pointer-events: auto; // 确保容器允许指针事件
            
            .slider-min,
            .slider-max {
              font-size: 10px;
              color: #999;
              min-width: 16px;
              flex-shrink: 0; // 防止标签被压缩
            }
            
            :global(.ant-slider) {
              flex: 1;
              margin: 0;
              min-width: 80px; // 滑动条本身的最小宽度
              position: relative; // 确保滑动条有正确的定位
              z-index: 10; // 确保滑动条在其他元素之上
              pointer-events: auto !important; // 强制启用指针事件
              
              .ant-slider-rail {
                background: rgba(255, 255, 255, 0.2);
                height: 4px;
                cursor: pointer; // 确保轨道可点击
                pointer-events: auto !important;
              }
              
              .ant-slider-track {
                height: 4px;
                cursor: pointer; // 确保轨道可点击
                pointer-events: auto !important;
              }
              
              .ant-slider-handle {
                width: 12px;
                height: 12px;
                margin-top: -4px;
                border: 2px solid #fff;
                cursor: grab; // 明确指定拖拽光标
                z-index: 11; // 确保手柄在最上层
                pointer-events: auto !important;
                
                &:hover,
                &:focus {
                  box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.2);
                  cursor: grab;
                }
                
                &:active {
                  cursor: grabbing; // 拖拽时的光标
                }
              }
              
              // 确保滑动条步骤可点击
              .ant-slider-step {
                cursor: pointer;
                pointer-events: auto !important;
              }
            }
            
            // Axial - 红色
            .axial-slider :global(.ant-slider-track) {
              background: #ff4d4f;
            }
            
            .axial-slider :global(.ant-slider-handle) {
              border-color: #ff4d4f;
            }
            
            // Coronal - 绿色
            .coronal-slider :global(.ant-slider-track) {
              background: #52c41a;
            }
            
            .coronal-slider :global(.ant-slider-handle) {
              border-color: #52c41a;
            }
            
            // Sagittal - 蓝色
            .sagittal-slider :global(.ant-slider-track) {
              background: #1890ff;
            }
            
            .sagittal-slider :global(.ant-slider-handle) {
              border-color: #1890ff;
            }
          }
          
          .value-input {
            width: 45px; // 稍微减小输入框宽度
            flex-shrink: 0; // 防止输入框被压缩
            
            :global(.ant-input-number) {
              background: rgba(60, 60, 60, 0.8);
              border: 1px solid #555;
              color: #fff;
              
              .ant-input-number-input {
                background: transparent;
                color: #fff;
                padding: 2px 6px;
              }
              
              .ant-input-number-handler-wrap {
                background: rgba(80, 80, 80, 0.8);
                border-left: 1px solid #555;
                
                .ant-input-number-handler {
                  border: none;
                  color: #ccc;
                  
                  &:hover {
                    color: #fff;
                  }
                }
              }
            }
          }
          
          .unit {
            font-size: 10px;
            color: #999;
            min-width: 18px;
            flex-shrink: 0; // 防止单位标签被压缩
          }
        }
      }
    }
  }

  // Switch 样式覆盖
  :global(.ant-switch) {
    background: rgba(100, 100, 100, 0.6);
    
    &.ant-switch-checked {
      background: #1890ff;
    }
    
    .ant-switch-handle {
      &::before {
        background: #fff;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .mip-reconstruction-panel {
    width: 300px; // 保持足够宽度
    left: 50px;
  }
}

@media (max-width: 768px) {
  .mip-reconstruction-panel {
    width: 280px; // 即使在小屏幕也保持最小宽度
    left: 40px;
    padding: 12px;
    
    .panel-content {
      .parameter-controls {
        .control-content {
          .slider-container {
            min-width: 100px; // 小屏幕下的最小滑动条宽度
            
            :global(.ant-slider) {
              min-width: 60px;
            }
          }
        }
      }
    }
  }
}