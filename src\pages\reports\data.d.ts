export interface TemplateItem {
  id: string;
  name: string;
  content: {
    radiographicFinding: string;
    clinicalDiagnosis: string;
  };
  type: 'personal' | 'team';
}

export interface TemplateCategory {
  key: string;
  title: string;
  icon: React.ReactNode;
  templates: TemplateItem[];
}

export interface ApiTemplateType {
  Id: number;
  Name: string;
}

export interface ApiTemplateItem {
  Id: number;
  Name: string;
  RadiographicFindingTemplate: string;
  ClinicalDiagnosisTemplate: string;
}

export interface TemplateContent {
  RadiographicFindingTemplate: string;
  ClinicalDiagnosisTemplate: string;
}

export interface ReportData {
  Id: number;
  Name: string;
  Sex: string;
  Age: number;
  Weight: string;
  StudyId: string;
  Medicine: string;
  PartChecked: string;
  ClinicalDiagnosis: string;
  ExamDate: string;
  RadiographicFinding: string;
  StatusOfReport: number;
}
