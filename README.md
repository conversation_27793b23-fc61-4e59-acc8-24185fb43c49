# 云阅片前端项目

<!-- PROJECT LOGO -->
<br />

 本篇README.md面向开发者
 
## 目录

- [上手指南](#上手指南)
  - [开发前的配置要求](#开发前的配置要求)
  - [安装步骤](#安装步骤)
- [文件目录说明](#文件目录说明)
- [开发的框架](#开发的框架)
- [部署](#部署)

### 上手指南

主要参考[umi4开发文档](https://umijs.org/docs/guides/getting-started)以及[antd5组件使用说明](https://ant-design.antgroup.com/components/overview-cn/)



###### 开发前的配置要求

1. nodejs版本：16.18.1
2. yarn版本：1.22.19

###### **安装步骤**

1. 克隆仓库

```sh
git clone ssh://git@************:20106/zzl/cloud-frontend.git
```
2. 在项目根目录运行yarn，安装项目依赖
```sh
yarn
```
3. 运行项目，在localhost:8000端口可以查看项目运行结果
```sh
yarn dev
```

### 文件目录说明
参考Umi官方文档给出的[目录结构](https://umijs.org/docs/guides/directory-structure)


### 开发的框架 

请阅读[React官方文档](https://react.docschina.org/) 查阅为该项目的基本架构。

### 部署

1. 打包项目
```sh
yarn build:dev
```
2. 将dist中的文件移动至相应运行文件夹，如宝塔的上传文件

### 更新threeView

1. 从pacs-frontend-monorepo库中打包threeView
```sh
yarn compile:threeView
```
2. 将packages/threeView文件夹整个复制到该项目对应位置，注意使用复制替换，不要删除文件，猜测：改文件夹与node_modules包下的rayplus-three-view直接关联
3. 在该项目执行打包命令，更新包体
```sh
yarn
```

