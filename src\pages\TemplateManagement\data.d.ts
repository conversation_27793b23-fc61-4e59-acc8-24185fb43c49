export interface ApiTemplate {
  Id: number;
  Name: string;
  TypeName: string;
  Creator: string;
  CreateTime: string;
}

export interface ApiListTemplateData {
  TemplateList: ApiTemplate[];
  Total: number;
}

export interface ApiListTemplateResponse {
  Data: ApiListTemplateData;
}

export interface ApiCategory {
  Id: number;
  Name: string;
  Creator: string;
  CreateTime: string;
}

export interface ApiListCategoryData {
  TemplateTypeList: ApiCategory[];
  Total: number;
}

export interface ApiTemplateType {
  Id: number;
  Name: string;
}

export interface TemplateContent {
  RadiographicFindingTemplate: string;
  ClinicalDiagnosisTemplate: string;
}

export interface ApiGroupTemplate {
  Id: number;
  Name: string;
  TypeName: string;
  Creator: string;
  CreateTime: string;
}

export interface ApiListGroupTemplateData {
  TemplateList: ApiGroupTemplate[];
  Total: number;
}

export interface ApiListGroupTemplateResponse {
  Data: ApiListGroupTemplateData;
}

export interface ApiGroupCategory {
  Id: number;
  Name: string;
  Creator: string;
  CreateTime: string;
}

export interface ApiListGroupCategoryData {
  TemplateTypeList: ApiGroupCategory[];
  Total: number;
}

// 图片上传相关类型定义
export interface ImageUploadResponse {
  Name: string;
}

export interface ImageUploadParams {
  file: File;
  onSuccess?: (imageName: string) => void;
  onFailure?: () => void;
}
