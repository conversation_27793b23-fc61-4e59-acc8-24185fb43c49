import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Switch, InputNumber } from 'antd';
import { connect } from 'umi';
import CustomSlider from './CustomSlider';
import './index.less';

interface MipReconstructionPanelProps {
  visible: boolean;
  onClose: () => void;
  onReconstructionChange: (params: ReconstructionParams) => void;
  initialParams?: ReconstructionParams;
}

interface ReconstructionParams {
  method: 'MinIP' | 'MIP' | 'Mean' | 'Sum';
  axial: number;
  coronal: number;
  sagittal: number;
  enabled: boolean;
}

const MipReconstructionPanel: React.FC<MipReconstructionPanelProps> = ({
  visible,
  onClose,
  onReconstructionChange,
  initialParams,
}) => {
  const [enabled, setEnabled] = useState(initialParams?.enabled || false);
  const [selectedMethod, setSelectedMethod] = useState<'MinIP' | 'MIP' | 'Mean' | 'Sum'>(initialParams?.method || 'MIP');
  const [axialValue, setAxialValue] = useState(initialParams?.axial || 10);
  const [coronalValue, setCoronalValue] = useState(initialParams?.coronal || 0);
  const [sagittalValue, setSagittalValue] = useState(initialParams?.sagittal || 0);

  // 当initialParams变化时更新状态
  useEffect(() => {
    if (initialParams) {
      setEnabled(initialParams.enabled);
      setSelectedMethod(initialParams.method);
      setAxialValue(initialParams.axial);
      setCoronalValue(initialParams.coronal);
      setSagittalValue(initialParams.sagittal);
    }
  }, [initialParams]);

  const handleEnabledChange = useCallback((checked: boolean) => {
    setEnabled(checked);
    onReconstructionChange({
      method: selectedMethod,
      axial: axialValue,
      coronal: coronalValue,
      sagittal: sagittalValue,
      enabled: checked,
    });
  }, [selectedMethod, axialValue, coronalValue, sagittalValue, onReconstructionChange]);

  const handleMethodChange = useCallback((method: 'MinIP' | 'MIP' | 'Mean' | 'Sum') => {
    setSelectedMethod(method);
    onReconstructionChange({
      method,
      axial: axialValue,
      coronal: coronalValue,
      sagittal: sagittalValue,
      enabled,
    });
  }, [axialValue, coronalValue, sagittalValue, enabled, onReconstructionChange]);

  const handleAxialChange = useCallback((value: number) => {
    setAxialValue(value);
    onReconstructionChange({
      method: selectedMethod,
      axial: value,
      coronal: coronalValue,
      sagittal: sagittalValue,
      enabled,
    });
  }, [selectedMethod, coronalValue, sagittalValue, enabled, onReconstructionChange]);

  const handleCoronalChange = useCallback((value: number) => {
    setCoronalValue(value);
    onReconstructionChange({
      method: selectedMethod,
      axial: axialValue,
      coronal: value,
      sagittal: sagittalValue,
      enabled,
    });
  }, [selectedMethod, axialValue, sagittalValue, enabled, onReconstructionChange]);

  const handleSagittalChange = useCallback((value: number) => {
    setSagittalValue(value);
    onReconstructionChange({
      method: selectedMethod,
      axial: axialValue,
      coronal: coronalValue,
      sagittal: value,
      enabled,
    });
  }, [selectedMethod, axialValue, coronalValue, enabled, onReconstructionChange]);

  const handleInputChange = useCallback((type: 'axial' | 'coronal' | 'sagittal', value: number | null) => {
    const newValue = value || 0;
    if (type === 'axial') {
      handleAxialChange(newValue);
    } else if (type === 'coronal') {
      handleCoronalChange(newValue);
    } else if (type === 'sagittal') {
      handleSagittalChange(newValue);
    }
  }, [handleAxialChange, handleCoronalChange, handleSagittalChange]);

  if (!visible) {
    return null;
  }

  return (
    <div className="mip-reconstruction-panel">
      <div className="panel-header">
        <span className="panel-title">启用MIP</span>
        <Switch 
          checked={enabled} 
          onChange={handleEnabledChange}
          size="small"
        />
      </div>
      
      {enabled && (
        <div className="panel-content">
          {/* 重建方法选择 */}
          <div className="method-selection">
            {(['MinIP', 'MIP', 'Mean', 'Sum'] as const).map((method) => (
              <div
                key={method}
                className={`method-item ${
                  selectedMethod === method ? 'active' : ''
                }`}
                onClick={() => handleMethodChange(method)}
              >
                {method}
              </div>
            ))}
          </div>

          {/* 参数调节 */}
          <div className="parameter-controls">
            {/* Axial */}
            <div className="control-row">
              <div className="control-label">Axial</div>
              <div className="control-content">
                <div className="slider-container">
                  <span className="slider-min">0</span>
                  <CustomSlider
                    min={0}
                    max={100}
                    value={axialValue}
                    onChange={handleAxialChange}
                    className="axial-slider"
                    color="#ff4d4f"
                    disabled={false}
                  />
                  <span className="slider-max">100</span>
                </div>
                <InputNumber
                  min={0}
                  max={100}
                  value={axialValue}
                  onChange={(value) => handleInputChange('axial', value)}
                  size="small"
                  className="value-input"
                />
                <span className="unit">mm</span>
              </div>
            </div>

            {/* Coronal */}
            <div className="control-row">
              <div className="control-label">Coronal</div>
              <div className="control-content">
                <div className="slider-container">
                  <span className="slider-min">0</span>
                  <CustomSlider
                    min={0}
                    max={100}
                    value={coronalValue}
                    onChange={handleCoronalChange}
                    className="coronal-slider"
                    color="#52c41a"
                    disabled={false}
                  />
                  <span className="slider-max">100</span>
                </div>
                <InputNumber
                  min={0}
                  max={100}
                  value={coronalValue}
                  onChange={(value) => handleInputChange('coronal', value)}
                  size="small"
                  className="value-input"
                />
                <span className="unit">mm</span>
              </div>
            </div>

            {/* Sagittal */}
            <div className="control-row">
              <div className="control-label">Sagittal</div>
              <div className="control-content">
                <div className="slider-container">
                  <span className="slider-min">0</span>
                  <CustomSlider
                    min={0}
                    max={100}
                    value={sagittalValue}
                    onChange={handleSagittalChange}
                    className="sagittal-slider"
                    color="#1890ff"
                    disabled={false}
                  />
                  <span className="slider-max">100</span>
                </div>
                <InputNumber
                  min={0}
                  max={100}
                  value={sagittalValue}
                  onChange={(value) => handleInputChange('sagittal', value)}
                  size="small"
                  className="value-input"
                />
                <span className="unit">mm</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// 连接Redux状态的包装组件
interface ConnectedMipReconstructionPanelProps extends Omit<MipReconstructionPanelProps, 'initialParams'> {
  views?: {
    mipReconstructionParams?: ReconstructionParams;
  };
}

const ConnectedMipReconstructionPanel: React.FC<ConnectedMipReconstructionPanelProps> = ({
  views,
  ...props
}) => {
  return (
    <MipReconstructionPanel
      {...props}
      initialParams={views?.mipReconstructionParams}
    />
  );
};

const mapStateToProps = ({ views }: { views: any }) => ({
  views,
});

export default connect(mapStateToProps)(ConnectedMipReconstructionPanel);
export type { ReconstructionParams };