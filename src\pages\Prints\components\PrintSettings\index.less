.printSetting {
  height: 100%;
  border-radius: 0;
  border: 1px solid gray;
  background-color: rgb(64, 64, 64);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.siderItemTitle {
  width: 100%;
  height: 10%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #2d2d2d;
  border-bottom: rgba(128, 128, 128, 0.5) 1px solid;
  line-height: 100%;
  font-size: calc(1.2vw);
  color: white;
  padding: 0 10px;
}

.contentContainer {
  margin-top: 5px;
  height: 90%;
  width: 100%;
  overflow-y: scroll;
  overflow-x: hidden;
  padding: 20px 10px;
  // border: 1px solid #5e5c5c;
  border-radius: 5px;
}

.layoutTitle {
  margin-top: 25px;
  font-size: 16px;
  color: #ffffff;
  margin-bottom: 5px;
}

.contentContainer button {
  width: 88px;
}

.settingRow {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-gap: 10px;
  width: 100%;
}

.divider span {
  color: white;
}

.divider::before,
.divider::after {
  background-color: gray;
}

.layoutGrid {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  justify-content: center;
  margin-bottom: 20px;
}
.layoutGridDiv {
  width: 30px;
  height: 30px;
  border: 1px solid #ccc;
  border-radius: 2px;
  box-sizing: border-box;
  background-color: white;
}
.numberContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
}

.quickerDesc {
  font-size: 15px;
  color: #5cc6e0;
}

.buttonIcon svg {
  width: 30px;
  height: 30px;
}

.radioGroup {
  :global {
    .ant-radio-button-wrapper {
      padding-inline: 10px;
    }
  }
}
