.login_bg {
  position: relative;
  background: url('/icon/login_back.png');
  /* 背景图垂直、水平均居中 */
  background-position: center center;
  /* 背景图不平铺 */
  background-repeat: no-repeat;
  /* 当内容高度大于图片高度时，背景图像的位置相对于viewport固定 */
  background-attachment: fixed;
  /* 让背景图基于容器大小伸缩 */
  background-size: cover;
  /* 设置背景颜色，背景图加载过程中会显示背景色 */
  background-color: #39bbdb4a;
  height: 100vh;
  width: 100%;
}
.center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.login_form {
  width: 1000px;
  height: 550px;
  border-radius: 10px;
  overflow: hidden;
  .left {
    position: relative;
    height: 100%;
    background: #64677166;
    .img {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .right {
    display: flex;
    flex-direction: column;
    justify-content: top;
    align-items: center;
    height: 100%;
    background-color: #ffffff;
    .logo_img {
      margin-top: 80px;
      margin-bottom: 45px;
    }
    .button {
      margin-top: 20px;
      width: 100%;
    }
  }
}
.copyright {
  margin-top: 30px;
  font-size: 14px;
  text-align: center;
  color: #ffffff;
}
.inputSty {
  background-color: #ffffff!important;
}
