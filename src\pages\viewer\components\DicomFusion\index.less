.fusionContainer {
  width: 100%;
  height: 100%;
  background-color: #000;
  padding: 16px;
  box-sizing: border-box;
  overflow: hidden;

  :global {
    .ant-row {
      height: calc(50% - 4px);
    }

    .ant-col {
      height: 100%;
    }
  }

  .viewportContainer {
    height: 100%;
    width: 100%;
    background-color: #1e1e1e;
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
    border: 1px solid #333;
  }

  .viewportTitle {
    height: 30px;
    line-height: 30px;
    background-color: rgba(76, 76, 76, 1);
    color: #fff;
    padding: 0 10px;
    font-size: 14px;
    text-align: left;
  }

  .viewport {
    flex: 1;
    width: 100%;
    background-color: #000;
    position: relative;
    overflow: hidden;
  }
}
