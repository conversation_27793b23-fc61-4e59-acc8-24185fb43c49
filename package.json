{"private": true, "author": "zzl", "scripts": {"dev": "cross-env REACT_APP_ENV=develop UMI_ENV=develop umi dev", "build:dev": "cross-env REACT_APP_ENV=develop UMI_ENV=develop umi build", "build:test": "cross-env REACT_APP_ENV=test UMI_ENV=test umi build", "build:prod": "cross-env REACT_APP_ENV=release UMI_ENV=release umi build", "postinstall": "umi setup", "setup": "umi setup", "start": "npm run dev", "compile:threeView": "yarn --cwd packages/threeView compile", "dev:threeView": "yarn --cwd packages/threeView dev", "ls": "lerna ls", "publishPack": "lerna <PERSON>"}, "dependencies": {"@ant-design/icons": "^5.5.1", "@ant-design/pro-components": "^2.6.18", "@cornerstonejs/core": "^1.82.4", "@cornerstonejs/dicom-image-loader": "^1.82.4", "@cornerstonejs/streaming-image-volume-loader": "1.82.4", "@cornerstonejs/tools": "^1.82.4", "@kitware/vtk.js": "^30.4.1", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-commonjs": "^19.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^13.0.0", "antd": "^5.8.6", "axios": "^1.5.0", "dva": "^2.4.1", "dva-loading": "^3.0.25", "dva-model-persist": "^1.0.0", "lerna": "^4.0.0", "lodash": "^4.17.21", "moment": "^2.30.1", "plotly.js-dist": "^2.35.3", "print-js": "1.6.0", "quill-image-resize-module-react": "^3.0.0", "react-color": "^2.19.3", "react-grid-layout": "^1.4.4", "react-quill": "^2.0.0", "react-resize-detector": "^10.0.1", "rollup": "^2.53.3", "rollup-plugin-cleaner": "^1.0.0", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-copy": "^3.4.0", "rollup-plugin-dts": "^3.0.2", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-postcss": "^4.0.0", "rollup-plugin-string": "^3.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.30.0", "styled-components": "^5", "three": "0.133.1", "three-csg": "^1.0.0", "umi": "^4.4.11", "uuid": "^9.0.1"}, "workspaces": ["packages/*"], "devDependencies": {"@html2canvas/html2canvas": "1.6.2", "@types/lodash": "^4.17.18", "@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "@umijs/plugins": "^4.4.11", "babel-loader": "^8.2.2", "copy-webpack-plugin": "6", "cross-env": "^7.0.3", "css-loader": "^5.2.6", "dicom-character-set": "^1.0.4", "eslint-plugin-react-hooks": "4.6.2", "file-loader": "^6.2.0", "glslify-loader": "^2.0.0", "js-cookie": "^3.0.5", "jspdf": "2.5.1", "less": "^4.1.1", "less-loader": "^10.0.1", "raw-loader": "^4.0.2", "react-konva": "^17.0.1-3", "source-map-loader": "^3.0.0", "style-loader": "^2.0.0", "ts-loader": "^9.2.3", "typescript": "^5.0.3", "url-loader": "^4.1.1", "webpack": "^5.42.1", "webpack-cli": "^4.7.2", "webpack-dev-server": "^3.11.2", "webpack-merge": "^5.8.0"}}