import React from 'react';
import { Modal, Button } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import CustomIcon from '@/components/CustomIcon';
import styles from './styles.less';

interface DeleteConfirmModalProps {
  visible: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  message?: string; // 自定义提示文案
  showConfirm?: boolean; // 是否展示确定按钮
}

/**
 * 自定义删除确认弹窗
 * 用于删除个人报告模板等需要二次确认的场景
 */
const DeleteConfirmModal: React.FC<DeleteConfirmModalProps> = ({ visible, onConfirm, onCancel, message, showConfirm = true }) => {
  return (
    <Modal
      classNames={{ 
        header: styles.modalHeader,
        content: styles.modalContent,
        body: styles.modalBody,
        footer: styles.modalFooter,
      }}
      title='删除提示'
      centered
      width={680}
      open={visible}
      footer={
        <div>
          <Button onClick={onCancel} style={{ marginRight: 12 }}>取 消</Button>
          {showConfirm && (<Button type="primary" style={{ background: '#1F69B4' }} onClick={onConfirm}>确 定</Button>)}
        </div>
      }
      onCancel={onCancel}
      closeIcon={
        <div className={styles.closeIcon} >
          <CloseOutlined />
        </div>
      }
    >
      {/* content */}
      <div
        style={{
          padding: '60px 24px 40px 24px',
          color: '#FFFFFF',
          textAlign: 'center',
        }}
      >
        <div style={{ fontSize: 18, marginTop: 8, lineHeight: '28px' }}>
          <CustomIcon type="icon-shanchu" style={{ marginRight: 8 }} />
          { message || '确认删除该报告模板吗？删除后数据将无法恢复！' }
        </div>
      </div>
    </Modal>
  );
};

export default DeleteConfirmModal; 